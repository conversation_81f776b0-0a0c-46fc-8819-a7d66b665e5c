"use client"

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { AdminLayout, PageContainer } from '@/components/admin/layout/admin-layout'
import { DataTable } from '@/components/admin/ui/data-table'
import { SuccessAlert } from '@/components/admin/ui/alert'
import { Badge } from '@/components/admin/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Plus, 
  Users,
  Star,
  Clock,
  Edit,
  Trash2,
  Eye,
  ToggleLeft,
  ToggleRight,
  Phone,
  Mail,
  Award
} from 'lucide-react'
import { staffStore, appointmentStore } from '@/lib/admin/storage'
import { Staff, Appointment } from '@/lib/types/admin'

export default function StaffPage() {
  const searchParams = useSearchParams()
  const [staff, setStaff] = useState<Staff[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([])
  const [showSuccessAlert, setShowSuccessAlert] = useState(false)
  const [successMessage, setSuccessMessage] = useState('')
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })

  useEffect(() => {
    loadData()
  }, [pagination.current, pagination.pageSize])

  // 检测成功状态参数
  useEffect(() => {
    const success = searchParams.get('success')
    if (success) {
      if (success === 'created') {
        setSuccessMessage('员工添加成功！')
      } else if (success === 'updated') {
        setSuccessMessage('员工信息更新成功！')
      }
      setShowSuccessAlert(true)

      // 清除URL参数
      const url = new URL(window.location.href)
      url.searchParams.delete('success')
      window.history.replaceState({}, '', url.toString())
    }
  }, [searchParams])

  const loadData = () => {
    setLoading(true)
    try {
      const result = staffStore.getPaginated({
        page: pagination.current,
        limit: pagination.pageSize,
        sortBy: 'name',
        sortOrder: 'asc'
      })
      
      setStaff(result.data)
      setPagination(prev => ({
        ...prev,
        total: result.total
      }))
    } catch (error) {
      console.error('Failed to load staff:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (searchValue: string) => {
    setLoading(true)
    try {
      const result = staffStore.getPaginated({
        page: 1,
        limit: pagination.pageSize,
        search: searchValue,
        sortBy: 'name',
        sortOrder: 'asc'
      })
      
      setStaff(result.data)
      setPagination(prev => ({
        ...prev,
        current: 1,
        total: result.total
      }))
    } catch (error) {
      console.error('Failed to search staff:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleToggleStatus = (staffId: string, currentStatus: boolean) => {
    try {
      staffStore.update(staffId, { isActive: !currentStatus })
      loadData()
    } catch (error) {
      console.error('Failed to toggle staff status:', error)
    }
  }

  const handleDelete = (staffId: string) => {
    // 检查是否有预约记录
    const appointments = appointmentStore.getAll()
    const staffAppointments = appointments.filter(apt => apt.staffId === staffId)
    
    if (staffAppointments.length > 0) {
      alert(`无法删除此员工，还有 ${staffAppointments.length} 个预约记录关联此员工。`)
      return
    }

    if (confirm('确定要删除这个员工吗？')) {
      try {
        staffStore.delete(staffId)
        loadData()
      } catch (error) {
        console.error('Failed to delete staff:', error)
      }
    }
  }

  const getStaffStats = (staffMember: Staff) => {
    const appointments = appointmentStore.getAll()
    const staffAppointments = appointments.filter(apt => apt.staffId === staffMember.id)
    const completedAppointments = staffAppointments.filter(apt => apt.status === 'completed')
    
    const thisMonth = new Date()
    thisMonth.setDate(1)
    const thisMonthAppointments = completedAppointments.filter(apt => 
      new Date(apt.date) >= thisMonth
    )

    return {
      totalAppointments: staffAppointments.length,
      completedAppointments: completedAppointments.length,
      thisMonthAppointments: thisMonthAppointments.length
    }
  }

  const formatWorkingHours = (hours: string[]) => {
    if (!hours || hours.length === 0) return '未设置'
    if (hours.length === 7) return '全周'
    
    const dayNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    const workingDays = hours.map(h => {
      const dayIndex = parseInt(h) - 1
      return dayNames[dayIndex] || h
    })
    
    return workingDays.join(', ')
  }

  const columns = [
    {
      key: 'name',
      title: '员工信息',
      render: (value: string, record: Staff) => (
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
            <span className="text-lg font-semibold text-primary">
              {value.charAt(0)}
            </span>
          </div>
          <div>
            <div className="flex items-center space-x-2">
              <p className="font-medium">{value}</p>
              {!record.isActive && (
                <Badge variant="destructive" size="sm">
                  已停用
                </Badge>
              )}
              {record.isManager && (
                <Badge variant="default" size="sm" className="bg-orange-500">
                  <Award className="h-3 w-3 mr-1" />
                  经理
                </Badge>
              )}
            </div>
            <div className="space-y-1 text-sm text-muted-foreground">
              <div className="flex items-center space-x-1">
                <Phone className="h-3 w-3" />
                <span>{record.phone}</span>
              </div>
              {record.email && (
                <div className="flex items-center space-x-1">
                  <Mail className="h-3 w-3" />
                  <span>{record.email}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'specialties',
      title: '专业技能',
      render: (value: string[]) => (
        <div className="space-y-1">
          {value && value.length > 0 ? (
            <div className="flex flex-wrap gap-1">
              {value.slice(0, 3).map((skill, index) => (
                <Badge key={index} variant="outline" size="sm">
                  {skill}
                </Badge>
              ))}
              {value.length > 3 && (
                <Badge variant="outline" size="sm">
                  +{value.length - 3}
                </Badge>
              )}
            </div>
          ) : (
            <span className="text-sm text-muted-foreground">未设置</span>
          )}
        </div>
      )
    },
    {
      key: 'workingHours',
      title: '工作时间',
      render: (value: string[], record: Staff) => (
        <div className="space-y-1">
          <div className="flex items-center space-x-1">
            <Clock className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">{formatWorkingHours(value)}</span>
          </div>
          <div className="text-xs text-muted-foreground">
            {record.startTime} - {record.endTime}
          </div>
        </div>
      )
    },
    {
      key: 'rating',
      title: '评分统计',
      render: (value: number, record: Staff) => {
        const stats = getStaffStats(record)
        return (
          <div className="text-center space-y-1">
            <div className="flex items-center justify-center space-x-1">
              <Star className="h-4 w-4 text-yellow-500 fill-current" />
              <span className="font-medium">{value.toFixed(1)}</span>
            </div>
            <div className="text-xs text-muted-foreground">
              {stats.completedAppointments} 次服务
            </div>
            <div className="text-xs text-blue-600">
              本月 {stats.thisMonthAppointments} 次
            </div>
          </div>
        )
      },
      sortable: true
    },
    {
      key: 'isActive',
      title: '状态',
      render: (value: boolean, record: Staff) => (
        <div className="flex items-center space-x-2">
          <button
            onClick={() => handleToggleStatus(record.id, value)}
            className="flex items-center space-x-1 hover:opacity-75 transition-opacity"
          >
            {value ? (
              <>
                <ToggleRight className="h-5 w-5 text-green-500" />
                <span className="text-sm text-green-600">在职</span>
              </>
            ) : (
              <>
                <ToggleLeft className="h-5 w-5 text-gray-400" />
                <span className="text-sm text-gray-500">离职</span>
              </>
            )}
          </button>
        </div>
      )
    }
  ]

  const activeStaff = staff.filter(s => s.isActive)
  const managers = staff.filter(s => s.isManager)
  const avgRating = staff.length > 0 ? staff.reduce((sum, s) => sum + s.rating, 0) / staff.length : 0

  return (
    <AdminLayout>
      <PageContainer
        title="员工管理"
        description="管理理发店员工信息、工作时间和技能设置"
        action={
          <Link href="/admin/staff/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              新增员工
            </Button>
          </Link>
        }
      >
        {/* 成功提示 */}
        {showSuccessAlert && (
          <div className="mb-6">
            <SuccessAlert
              message={successMessage}
              onClose={() => setShowSuccessAlert(false)}
            />
          </div>
        )}

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-card border border-border rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-primary">{staff.length}</div>
            <div className="text-sm text-muted-foreground">总员工数</div>
          </div>
          <div className="bg-card border border-border rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{activeStaff.length}</div>
            <div className="text-sm text-muted-foreground">在职员工</div>
          </div>
          <div className="bg-card border border-border rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">{managers.length}</div>
            <div className="text-sm text-muted-foreground">管理人员</div>
          </div>
          <div className="bg-card border border-border rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{avgRating.toFixed(1)}</div>
            <div className="text-sm text-muted-foreground">平均评分</div>
          </div>
        </div>

        <div className="bg-card border border-border rounded-lg">
          <DataTable
            data={staff}
            columns={columns}
            loading={loading}
            searchable
            searchPlaceholder="搜索员工姓名、电话或邮箱..."
            onSearch={handleSearch}
            selectable
            selectedRowKeys={selectedRowKeys}
            onSelectChange={setSelectedRowKeys}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              onChange: (page, pageSize) => {
                setPagination(prev => ({
                  ...prev,
                  current: page,
                  pageSize
                }))
              }
            }}
            actions={{
              title: '操作',
              render: (record: Staff) => (
                <div className="flex items-center space-x-1">
                  <Link href={`/admin/staff/${record.id}`}>
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </Link>
                  <Link href={`/admin/staff/${record.id}/edit`}>
                    <Button variant="ghost" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                  </Link>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => handleDelete(record.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              )
            }}
          />
        </div>

        {/* 批量操作 */}
        {selectedRowKeys.length > 0 && (
          <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-card border border-border rounded-lg shadow-lg p-4">
            <div className="flex items-center space-x-4">
              <span className="text-sm text-muted-foreground">
                已选择 {selectedRowKeys.length} 个员工
              </span>
              <div className="flex space-x-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => {
                    selectedRowKeys.forEach(id => {
                      staffStore.update(id, { isActive: true })
                    })
                    setSelectedRowKeys([])
                    loadData()
                  }}
                >
                  批量启用
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => {
                    selectedRowKeys.forEach(id => {
                      staffStore.update(id, { isActive: false })
                    })
                    setSelectedRowKeys([])
                    loadData()
                  }}
                >
                  批量停用
                </Button>
              </div>
            </div>
          </div>
        )}
      </PageContainer>
    </AdminLayout>
  )
}
