"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { FadeIn, ScaleIn } from "@/components/animations/fade-in"

const testimonials = [
  {
    name: "张先生",
    rating: 5,
    comment: "服务非常专业，理发师技术精湛，店内环境也很舒适。已经是这里的老客户了，强烈推荐！",
    service: "经典理发",
  },
  {
    name: "李先生",
    rating: 5,
    comment: "胡须修剪得很精细，理发师很有耐心，会根据我的脸型给出专业建议。价格也很合理。",
    service: "胡须修剪",
  },
  {
    name: "王先生",
    rating: 5,
    comment: "第一次来就被专业的服务打动了，理发师很细心，剪出来的发型很满意。环境也很干净。",
    service: "时尚造型",
  },
  {
    name: "陈先生",
    rating: 5,
    comment: "朋友推荐来的，果然没有失望。理发师技术很好，而且会给出很好的建议。会继续光顾的。",
    service: "商务造型",
  },
  {
    name: "刘先生",
    rating: 5,
    comment: "预约很方便，服务很周到。理发师会仔细询问我的需求，剪出来的效果超出预期。",
    service: "洗发护理",
  },
]

export function TestimonialsSection() {
  const [currentIndex, setCurrentIndex] = useState(0)

  const nextTestimonial = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
    )
  }

  const prevTestimonial = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1
    )
  }

  // Auto-rotate testimonials
  useEffect(() => {
    const interval = setInterval(nextTestimonial, 5000)
    return () => clearInterval(interval)
  }, [])

  return (
    <section className="py-20 bg-muted/50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <FadeIn>
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              客户评价
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              听听我们客户的真实反馈，他们的满意是我们最大的动力
            </p>
          </div>
        </FadeIn>

        {/* Testimonials Carousel */}
        <ScaleIn delay={300}>
          <div className="max-w-4xl mx-auto">
            <Card className="bg-background/50 backdrop-blur-sm border-2">
              <CardContent className="p-8 md:p-12">
                <div className="text-center">
                  {/* Stars */}
                  <div className="flex justify-center mb-6">
                    {[...Array(testimonials[currentIndex].rating)].map((_, i) => (
                      <span key={i} className="text-2xl text-yellow-400">⭐</span>
                    ))}
                  </div>
                  
                  {/* Comment */}
                  <blockquote className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
                    "{testimonials[currentIndex].comment}"
                  </blockquote>
                  
                  {/* Customer Info */}
                  <div className="space-y-2">
                    <div className="text-lg font-semibold">
                      {testimonials[currentIndex].name}
                    </div>
                    <div className="text-sm text-primary">
                      {testimonials[currentIndex].service}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Navigation */}
            <div className="flex justify-center items-center mt-8 space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={prevTestimonial}
                className="rounded-full w-10 h-10 p-0"
              >
                ←
              </Button>
              
              {/* Dots */}
              <div className="flex space-x-2">
                {testimonials.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentIndex(index)}
                    className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      index === currentIndex 
                        ? 'bg-primary scale-125' 
                        : 'bg-muted-foreground/30 hover:bg-muted-foreground/50'
                    }`}
                  />
                ))}
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={nextTestimonial}
                className="rounded-full w-10 h-10 p-0"
              >
                →
              </Button>
            </div>
          </div>
        </ScaleIn>
      </div>
    </section>
  )
}
