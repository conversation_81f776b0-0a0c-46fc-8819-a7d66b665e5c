"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"

const testimonials = [
  {
    name: "张先生",
    rating: 5,
    comment: "服务非常专业，理发师技术精湛，店内环境也很舒适。已经是这里的老客户了，强烈推荐！",
    service: "经典理发",
  },
  {
    name: "李先生",
    rating: 5,
    comment: "胡须修剪得很精细，理发师很有耐心，会根据我的脸型给出专业建议。价格也很合理。",
    service: "胡须修剪",
  },
  {
    name: "王先生",
    rating: 5,
    comment: "第一次来就被这里的专业服务打动了。理发师不仅技术好，服务态度也很棒。会继续光顾的。",
    service: "头发造型",
  },
  {
    name: "陈先生",
    rating: 5,
    comment: "朋友推荐来的，果然没有失望。理发师很专业，剪出来的发型很满意，店内氛围也很好。",
    service: "经典理发",
  },
  {
    name: "刘先生",
    rating: 5,
    comment: "预约很方便，服务很周到。理发师会仔细询问我的需求，剪出来的效果超出预期。",
    service: "洗发护理",
  },
]

export function TestimonialsSection() {
  const [currentIndex, setCurrentIndex] = useState(0)

  const nextTestimonial = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
    )
  }

  const prevTestimonial = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1
    )
  }

  // Auto-rotate testimonials
  useEffect(() => {
    const interval = setInterval(nextTestimonial, 5000)
    return () => clearInterval(interval)
  }, [])

  return (
    <section className="py-20 bg-muted/50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            客户评价
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            听听我们客户的真实反馈，他们的满意是我们前进的动力
          </p>
        </div>

        {/* Testimonials Carousel */}
        <div className="relative max-w-4xl mx-auto">
          <Card className="min-h-[200px]">
            <CardContent className="p-8">
              <div className="text-center">
                {/* Stars */}
                <div className="flex justify-center mb-4">
                  {[...Array(testimonials[currentIndex].rating)].map((_, i) => (
                    <span key={i} className="text-accent text-xl">⭐</span>
                  ))}
                </div>

                {/* Comment */}
                <blockquote className="text-lg md:text-xl text-muted-foreground mb-6 italic">
                  "{testimonials[currentIndex].comment}"
                </blockquote>

                {/* Author Info */}
                <div className="space-y-1">
                  <div className="font-semibold text-lg">
                    {testimonials[currentIndex].name}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {testimonials[currentIndex].service}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Navigation Buttons */}
          <Button
            variant="outline"
            size="icon"
            className="absolute left-4 top-1/2 transform -translate-y-1/2"
            onClick={prevTestimonial}
          >
            <span>←</span>
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="absolute right-4 top-1/2 transform -translate-y-1/2"
            onClick={nextTestimonial}
          >
            <span>→</span>
          </Button>

          {/* Dots Indicator */}
          <div className="flex justify-center mt-6 space-x-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full transition-colors ${
                  index === currentIndex ? "bg-primary" : "bg-muted-foreground/30"
                }`}
                onClick={() => setCurrentIndex(index)}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
