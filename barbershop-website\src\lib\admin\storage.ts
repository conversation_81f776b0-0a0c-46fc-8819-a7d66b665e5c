// <PERSON>'s Barbershop - 数据存储管理系统

import {
  User, Customer, Appointment, Service, Staff, Analytics, SystemSettings,
  ServiceCategory, QueryParams, PaginatedResponse, ServicePreference
} from '@/lib/types/admin'

// 数据存储基类
class DataStore<T extends { id: string }> {
  private storageKey: string
  private data: T[] = []

  constructor(storageKey: string) {
    this.storageKey = storageKey
    this.loadData()
  }

  // 从 localStorage 加载数据
  private loadData(): void {
    try {
      const stored = localStorage.getItem(this.storageKey)
      if (stored) {
        this.data = JSON.parse(stored)
      }
    } catch (error) {
      console.error(`Error loading data for ${this.storageKey}:`, error)
      this.data = []
    }
  }

  // 保存数据到 localStorage
  private saveData(): void {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(this.data))
    } catch (error) {
      console.error(`Error saving data for ${this.storageKey}:`, error)
    }
  }

  // 获取所有数据
  getAll(): T[] {
    return [...this.data]
  }

  // 根据ID获取单个数据
  getById(id: string): T | undefined {
    return this.data.find(item => item.id === id)
  }

  // 分页查询
  getPaginated(params: QueryParams): PaginatedResponse<T> {
    let filteredData = [...this.data]

    // 搜索过滤
    if (params.search) {
      const searchTerm = params.search.toLowerCase()
      filteredData = filteredData.filter(item => 
        JSON.stringify(item).toLowerCase().includes(searchTerm)
      )
    }

    // 自定义过滤
    if (params.filter) {
      filteredData = filteredData.filter(item => {
        return Object.entries(params.filter!).every(([key, value]) => {
          const itemValue = (item as any)[key]
          if (Array.isArray(value)) {
            return value.includes(itemValue)
          }
          return itemValue === value
        })
      })
    }

    // 排序
    if (params.sortBy) {
      filteredData.sort((a, b) => {
        const aValue = (a as any)[params.sortBy!]
        const bValue = (b as any)[params.sortBy!]
        
        if (aValue < bValue) return params.sortOrder === 'desc' ? 1 : -1
        if (aValue > bValue) return params.sortOrder === 'desc' ? -1 : 1
        return 0
      })
    }

    // 分页
    const page = params.page || 1
    const limit = params.limit || 10
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedData = filteredData.slice(startIndex, endIndex)

    return {
      data: paginatedData,
      total: filteredData.length,
      page,
      limit,
      totalPages: Math.ceil(filteredData.length / limit)
    }
  }

  // 创建新数据
  create(item: Omit<T, 'id' | 'createdAt' | 'updatedAt'>): T {
    const newItem: T = {
      ...item,
      id: this.generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    } as unknown as T

    this.data.push(newItem)
    this.saveData()
    return newItem
  }

  // 更新数据
  update(id: string, updates: Partial<Omit<T, 'id' | 'createdAt'>>): T | null {
    const index = this.data.findIndex(item => item.id === id)
    if (index === -1) return null

    this.data[index] = {
      ...this.data[index],
      ...updates,
      updatedAt: new Date().toISOString()
    } as T

    this.saveData()
    return this.data[index]
  }

  // 删除数据
  delete(id: string): boolean {
    const index = this.data.findIndex(item => item.id === id)
    if (index === -1) return false

    this.data.splice(index, 1)
    this.saveData()
    return true
  }

  // 批量删除
  deleteMany(ids: string[]): number {
    const initialLength = this.data.length
    this.data = this.data.filter(item => !ids.includes(item.id))
    const deletedCount = initialLength - this.data.length
    
    if (deletedCount > 0) {
      this.saveData()
    }
    
    return deletedCount
  }

  // 生成唯一ID
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  // 清空所有数据
  clear(): void {
    this.data = []
    this.saveData()
  }

  // 导入数据
  import(data: T[]): void {
    this.data = data
    this.saveData()
  }

  // 导出数据
  export(): T[] {
    return [...this.data]
  }
}

// 具体的数据存储实例
export const userStore = new DataStore<User>('barbershop_users')
export const customerStore = new DataStore<Customer>('barbershop_customers')
export const appointmentStore = new DataStore<Appointment>('barbershop_appointments')
export const serviceStore = new DataStore<Service>('barbershop_services')
export const staffStore = new DataStore<Staff>('barbershop_staff')
export const categoryStore = new DataStore<ServiceCategory>('barbershop_categories')

// 系统设置存储 (单例)
class SettingsStore {
  private storageKey = 'barbershop_settings'
  private settings: SystemSettings | null = null

  constructor() {
    this.loadSettings()
  }

  private loadSettings(): void {
    try {
      const stored = localStorage.getItem(this.storageKey)
      if (stored) {
        this.settings = JSON.parse(stored)
      }
    } catch (error) {
      console.error('Error loading settings:', error)
      this.settings = null
    }
  }

  private saveSettings(): void {
    try {
      if (this.settings) {
        localStorage.setItem(this.storageKey, JSON.stringify(this.settings))
      }
    } catch (error) {
      console.error('Error saving settings:', error)
    }
  }

  get(): SystemSettings | null {
    return this.settings
  }

  update(updates: Partial<SystemSettings>): SystemSettings {
    this.settings = {
      ...this.settings,
      ...updates
    } as SystemSettings

    this.saveSettings()
    return this.settings
  }

  reset(): void {
    this.settings = null
    localStorage.removeItem(this.storageKey)
  }
}

export const settingsStore = new SettingsStore()

// 分析数据存储 (计算型数据，不持久化)
class AnalyticsStore {
  // 计算营收数据
  calculateRevenue(): Analytics['revenue'] {
    const appointments = appointmentStore.getAll()
    const completedAppointments = appointments.filter(apt => apt.status === 'completed')

    // 按日期分组计算
    const dailyData = new Map<string, { revenue: number, appointments: number }>()
    
    completedAppointments.forEach(apt => {
      const date = apt.date
      const current = dailyData.get(date) || { revenue: 0, appointments: 0 }
      dailyData.set(date, {
        revenue: current.revenue + apt.totalPrice,
        appointments: current.appointments + 1
      })
    })

    const daily = Array.from(dailyData.entries()).map(([date, data]) => ({
      date,
      revenue: data.revenue,
      appointments: data.appointments
    }))

    // TODO: 实现周、月、年度统计
    return {
      daily,
      weekly: [],
      monthly: [],
      yearly: []
    }
  }

  // 计算预约统计
  calculateAppointmentStats(): Analytics['appointments'] {
    const appointments = appointmentStore.getAll()
    const today = new Date().toISOString().split('T')[0]
    
    // 获取本周开始日期
    const now = new Date()
    const weekStart = new Date(now.setDate(now.getDate() - now.getDay()))
    const weekStartStr = weekStart.toISOString().split('T')[0]
    
    // 获取本月开始日期
    const monthStart = new Date(new Date().getFullYear(), new Date().getMonth(), 1)
    const monthStartStr = monthStart.toISOString().split('T')[0]

    return {
      total: appointments.length,
      pending: appointments.filter(apt => apt.status === 'pending').length,
      confirmed: appointments.filter(apt => apt.status === 'confirmed').length,
      completed: appointments.filter(apt => apt.status === 'completed').length,
      cancelled: appointments.filter(apt => apt.status === 'cancelled').length,
      noShow: appointments.filter(apt => apt.status === 'no_show').length,
      todayTotal: appointments.filter(apt => apt.date === today).length,
      weekTotal: appointments.filter(apt => apt.date >= weekStartStr).length,
      monthTotal: appointments.filter(apt => apt.date >= monthStartStr).length
    }
  }

  // 获取完整分析数据
  getAnalytics(): Analytics {
    return {
      revenue: this.calculateRevenue(),
      appointments: this.calculateAppointmentStats(),
      customers: {
        total: customerStore.getAll().length,
        new: 0, // TODO: 计算本月新客户
        returning: 0, // TODO: 计算回头客
        averageSpent: 0, // TODO: 计算平均消费
        topCustomers: [] // TODO: 计算顶级客户
      },
      services: {
        total: serviceStore.getAll().length,
        popular: [], // TODO: 计算热门服务
        revenue: [] // TODO: 计算服务营收
      },
      staff: {
        total: staffStore.getAll().length,
        active: staffStore.getAll().filter(staff => staff.isActive).length,
        performance: [] // TODO: 计算员工表现
      }
    }
  }
}

export const analyticsStore = new AnalyticsStore()

// 数据初始化函数
export function initializeData(): void {
  // 检查是否已有数据，如果没有则创建默认数据
  if (userStore.getAll().length === 0) {
    // 创建默认管理员用户
    userStore.create({
      username: 'admin',
      password: 'barbershop2024', // 实际应用中应该加密
      role: 'super_admin',
      name: '系统管理员'
    } as Omit<User, 'id' | 'createdAt' | 'updatedAt'>)
  }

  // 创建默认服务分类
  if (categoryStore.getAll().length === 0) {
    const categories = [
      { name: '理发服务', description: '各种理发和造型服务', order: 1, isActive: true },
      { name: '胡须护理', description: '专业胡须修剪和护理', order: 2, isActive: true },
      { name: '头发护理', description: '洗发、护发等服务', order: 3, isActive: true },
      { name: '特色服务', description: '婚礼造型等特殊服务', order: 4, isActive: true }
    ]

    categories.forEach(category => {
      categoryStore.create(category as Omit<ServiceCategory, 'id' | 'createdAt' | 'updatedAt'>)
    })
  }
}

// 数据备份和恢复
export function backupData(): string {
  const backup = {
    users: userStore.export(),
    customers: customerStore.export(),
    appointments: appointmentStore.export(),
    services: serviceStore.export(),
    staff: staffStore.export(),
    categories: categoryStore.export(),
    settings: settingsStore.get(),
    timestamp: new Date().toISOString()
  }

  return JSON.stringify(backup, null, 2)
}

export function restoreData(backupData: string): boolean {
  try {
    const backup = JSON.parse(backupData)
    
    userStore.import(backup.users || [])
    customerStore.import(backup.customers || [])
    appointmentStore.import(backup.appointments || [])
    serviceStore.import(backup.services || [])
    staffStore.import(backup.staff || [])
    categoryStore.import(backup.categories || [])
    
    if (backup.settings) {
      settingsStore.update(backup.settings)
    }

    return true
  } catch (error) {
    console.error('Error restoring data:', error)
    return false
  }
}

// 初始化示例数据
export function initializeSampleData() {
  // 检查是否已有数据
  if (customerStore.getAll().length > 0) return

  // 创建示例客户
  const customers = [
    {
      name: '张先生',
      phone: '13800138001',
      email: '<EMAIL>',
      preferences: [] as ServicePreference[],
      totalVisits: 5,
      totalSpent: 450
    },
    {
      name: '李女士',
      phone: '13800138002',
      email: '<EMAIL>',
      preferences: [] as ServicePreference[],
      totalVisits: 3,
      totalSpent: 270
    },
    {
      name: '王先生',
      phone: '13800138003',
      email: '<EMAIL>',
      preferences: [] as ServicePreference[],
      totalVisits: 8,
      totalSpent: 720
    }
  ]

  const createdCustomers = customers.map(customer => customerStore.create(customer))

  // 创建示例员工
  const staffMembers = [
    {
      name: 'Tony',
      phone: '13900139001',
      email: '<EMAIL>',
      specialties: ['经典理发', '时尚造型', '胡须修剪', '头发护理'],
      workingHours: ['1', '2', '3', '4', '5', '6'], // 周一到周六
      startTime: '09:00',
      endTime: '18:00',
      isManager: true,
      isActive: true,
      notes: '店长，拥有15年理发经验，擅长各种发型设计',
      rating: 4.8
    },
    {
      name: '小李',
      phone: '13900139002',
      email: '<EMAIL>',
      specialties: ['洗剪吹套餐', '头发护理', '基础理发'],
      workingHours: ['1', '2', '3', '4', '5', '6', '7'], // 全周
      startTime: '10:00',
      endTime: '19:00',
      isManager: false,
      isActive: true,
      notes: '高级理发师，服务态度好，技术娴熟',
      rating: 4.6
    },
    {
      name: '小王',
      phone: '13900139003',
      email: '<EMAIL>',
      specialties: ['造型设计', '胡须护理', '护理套餐'],
      workingHours: ['2', '3', '4', '5', '6'], // 周二到周六
      startTime: '09:30',
      endTime: '18:30',
      isManager: false,
      isActive: true,
      notes: '年轻理发师，创意十足，深受年轻客户喜爱',
      rating: 4.5
    }
  ]

  const createdStaff = staffMembers.map(staff => staffStore.create(staff))

  // 创建示例服务分类
  const categories = [
    {
      name: '基础理发',
      description: '传统理发服务',
      order: 1,
      isActive: true
    },
    {
      name: '造型设计',
      description: '时尚发型设计服务',
      order: 2,
      isActive: true
    },
    {
      name: '胡须护理',
      description: '胡须修剪和造型',
      order: 3,
      isActive: true
    },
    {
      name: '护理套餐',
      description: '头发护理和保养',
      order: 4,
      isActive: true
    }
  ]

  const createdCategories = categories.map(category => categoryStore.create(category))

  // 创建示例服务
  const services = [
    {
      name: '经典理发',
      description: '传统男士理发服务',
      category: createdCategories[0].name,
      price: 50,
      duration: 30,
      isActive: true,
      popularity: 85
    },
    {
      name: '时尚造型',
      description: '现代时尚发型设计',
      category: createdCategories[1].name,
      price: 80,
      duration: 45,
      isActive: true,
      popularity: 90
    },
    {
      name: '胡须修剪',
      description: '专业胡须造型服务',
      category: createdCategories[2].name,
      price: 30,
      duration: 20,
      isActive: true,
      popularity: 70
    },
    {
      name: '洗剪吹套餐',
      description: '洗发+理发+吹干造型',
      category: createdCategories[3].name,
      price: 90,
      duration: 60,
      isActive: true,
      popularity: 95
    },
    {
      name: '头发护理',
      description: '深层清洁和营养护理',
      category: createdCategories[3].name,
      price: 120,
      duration: 90,
      isActive: true,
      popularity: 75
    }
  ]

  const createdServices = services.map(service => serviceStore.create(service))

  // 创建示例预约
  const today = new Date()
  const tomorrow = new Date(today)
  tomorrow.setDate(today.getDate() + 1)

  const appointments = [
    {
      customerId: createdCustomers[0].id,
      customerName: createdCustomers[0].name,
      customerPhone: createdCustomers[0].phone,
      staffId: createdStaff[0].id,
      staffName: createdStaff[0].name,
      serviceIds: [createdServices[0].id],
      services: [{
        serviceId: createdServices[0].id,
        serviceName: createdServices[0].name,
        duration: createdServices[0].duration,
        price: createdServices[0].price
      }],
      date: today.toISOString().split('T')[0],
      startTime: '14:30',
      endTime: '15:00',
      duration: 30,
      totalPrice: 50,
      status: 'confirmed' as const,
      notes: '客户要求稍微短一些'
    },
    {
      customerId: createdCustomers[1].id,
      customerName: createdCustomers[1].name,
      customerPhone: createdCustomers[1].phone,
      staffId: createdStaff[1].id,
      staffName: createdStaff[1].name,
      serviceIds: [createdServices[3].id],
      services: [{
        serviceId: createdServices[3].id,
        serviceName: createdServices[3].name,
        duration: createdServices[3].duration,
        price: createdServices[3].price
      }],
      date: today.toISOString().split('T')[0],
      startTime: '15:00',
      endTime: '16:00',
      duration: 60,
      totalPrice: 90,
      status: 'pending' as const,
      notes: ''
    },
    {
      customerId: createdCustomers[2].id,
      customerName: createdCustomers[2].name,
      customerPhone: createdCustomers[2].phone,
      staffId: createdStaff[0].id,
      staffName: createdStaff[0].name,
      serviceIds: [createdServices[1].id, createdServices[2].id],
      services: [
        {
          serviceId: createdServices[1].id,
          serviceName: createdServices[1].name,
          duration: createdServices[1].duration,
          price: createdServices[1].price
        },
        {
          serviceId: createdServices[2].id,
          serviceName: createdServices[2].name,
          duration: createdServices[2].duration,
          price: createdServices[2].price
        }
      ],
      date: tomorrow.toISOString().split('T')[0],
      startTime: '10:00',
      endTime: '11:05',
      duration: 65,
      totalPrice: 110,
      status: 'confirmed' as const,
      notes: '重要客户，请提供最好的服务'
    }
  ]

  appointments.forEach(appointment => appointmentStore.create(appointment))

  console.log('示例数据初始化完成')
}
