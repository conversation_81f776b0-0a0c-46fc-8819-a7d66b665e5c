# Tony's Barbershop - 管理后台系统架构

## 🏗️ 系统架构设计

### 📁 文件结构规划

```
src/
├── app/
│   ├── admin/                    # 管理后台路由组
│   │   ├── layout.tsx           # 管理后台布局
│   │   ├── page.tsx             # 仪表板首页
│   │   ├── login/
│   │   │   └── page.tsx         # 登录页面
│   │   ├── appointments/
│   │   │   ├── page.tsx         # 预约管理列表
│   │   │   ├── [id]/
│   │   │   │   └── page.tsx     # 预约详情/编辑
│   │   │   └── new/
│   │   │       └── page.tsx     # 新建预约
│   │   ├── customers/
│   │   │   ├── page.tsx         # 客户管理列表
│   │   │   ├── [id]/
│   │   │   │   └── page.tsx     # 客户详情/编辑
│   │   │   └── new/
│   │   │       └── page.tsx     # 新建客户
│   │   ├── services/
│   │   │   ├── page.tsx         # 服务项目管理
│   │   │   ├── [id]/
│   │   │   │   └── page.tsx     # 服务编辑
│   │   │   └── new/
│   │   │       └── page.tsx     # 新建服务
│   │   ├── staff/
│   │   │   ├── page.tsx         # 员工管理
│   │   │   ├── [id]/
│   │   │   │   └── page.tsx     # 员工详情/编辑
│   │   │   └── new/
│   │   │       └── page.tsx     # 新建员工
│   │   └── analytics/
│   │       └── page.tsx         # 数据统计
│   └── api/
│       └── admin/               # API 路由
│           ├── auth/
│           ├── appointments/
│           ├── customers/
│           ├── services/
│           ├── staff/
│           └── analytics/
├── components/
│   ├── admin/                   # 管理后台专用组件
│   │   ├── layout/
│   │   │   ├── admin-layout.tsx      # 主布局组件
│   │   │   ├── sidebar.tsx           # 侧边栏导航
│   │   │   ├── header.tsx            # 顶部栏
│   │   │   └── breadcrumb.tsx        # 面包屑导航
│   │   ├── auth/
│   │   │   ├── login-form.tsx        # 登录表单
│   │   │   └── auth-guard.tsx        # 路由保护
│   │   ├── dashboard/
│   │   │   ├── stats-cards.tsx       # 统计卡片
│   │   │   ├── recent-appointments.tsx # 最近预约
│   │   │   └── quick-actions.tsx     # 快速操作
│   │   ├── appointments/
│   │   │   ├── appointment-list.tsx  # 预约列表
│   │   │   ├── appointment-form.tsx  # 预约表单
│   │   │   ├── appointment-calendar.tsx # 日历视图
│   │   │   └── appointment-status.tsx # 状态管理
│   │   ├── customers/
│   │   │   ├── customer-list.tsx     # 客户列表
│   │   │   ├── customer-form.tsx     # 客户表单
│   │   │   ├── customer-history.tsx  # 服务历史
│   │   │   └── customer-preferences.tsx # 偏好设置
│   │   ├── services/
│   │   │   ├── service-list.tsx      # 服务列表
│   │   │   ├── service-form.tsx      # 服务表单
│   │   │   └── service-pricing.tsx   # 价格设置
│   │   ├── staff/
│   │   │   ├── staff-list.tsx        # 员工列表
│   │   │   ├── staff-form.tsx        # 员工表单
│   │   │   ├── staff-schedule.tsx    # 工作安排
│   │   │   └── staff-skills.tsx      # 技能设置
│   │   ├── analytics/
│   │   │   ├── revenue-chart.tsx     # 营收图表
│   │   │   ├── service-stats.tsx     # 服务统计
│   │   │   └── customer-stats.tsx    # 客户统计
│   │   └── ui/
│   │       ├── data-table.tsx        # 数据表格
│   │       ├── date-picker.tsx       # 日期选择器
│   │       ├── time-picker.tsx       # 时间选择器
│   │       ├── select.tsx            # 下拉选择
│   │       ├── badge.tsx             # 状态徽章
│   │       ├── dialog.tsx            # 对话框
│   │       └── toast.tsx             # 提示消息
│   └── ui/                      # 现有 UI 组件库
├── lib/
│   ├── admin/
│   │   ├── auth.ts              # 身份验证逻辑
│   │   ├── storage.ts           # 数据存储管理
│   │   ├── validation.ts        # 表单验证
│   │   └── utils.ts             # 工具函数
│   └── types/
│       └── admin.ts             # TypeScript 类型定义
└── data/
    └── admin/                   # 本地数据存储
        ├── users.json           # 管理员用户
        ├── appointments.json    # 预约数据
        ├── customers.json       # 客户数据
        ├── services.json        # 服务项目
        ├── staff.json           # 员工数据
        └── settings.json        # 系统设置
```

## 🎨 设计系统

### 色彩方案
- **主色调**: 继承现有品牌色彩
  - Primary: #000000 (黑色)
  - Accent: #d4af37 (金色)
  - Secondary: #dc2626 (红色)
  - Background: #ffffff (白色)
  - Muted: #f8f9fa (浅灰)

### 组件设计原则
- 保持与前台网站一致的视觉风格
- 使用现有的 shadcn/ui 组件系统
- 添加管理后台专用的数据展示组件
- 确保响应式设计，支持桌面和平板操作

## 📊 数据模型设计

### 核心实体关系
```
Customer (客户)
├── id: string
├── name: string
├── phone: string
├── email?: string
├── preferences: ServicePreference[]
├── history: AppointmentHistory[]
└── createdAt: Date

Appointment (预约)
├── id: string
├── customerId: string
├── staffId: string
├── serviceIds: string[]
├── date: Date
├── startTime: string
├── endTime: string
├── status: 'pending' | 'confirmed' | 'completed' | 'cancelled'
├── notes?: string
└── createdAt: Date

Service (服务项目)
├── id: string
├── name: string
├── description: string
├── duration: number (分钟)
├── price: number
├── category: string
├── isActive: boolean
└── createdAt: Date

Staff (员工)
├── id: string
├── name: string
├── role: string
├── skills: string[]
├── workingHours: WorkingHours
├── isActive: boolean
└── createdAt: Date
```

## 🔐 身份验证系统

### 简单认证方案
- 使用本地存储的用户名/密码
- JWT Token 存储在 localStorage
- 路由级别的权限保护
- 会话超时自动登出

### 默认管理员账户
- 用户名: admin
- 密码: barbershop2024
- 角色: super_admin

## 🚀 开发计划

### Phase 1: 基础架构 (1-2天)
1. 创建数据模型和存储系统
2. 实现身份验证系统
3. 构建管理后台布局

### Phase 2: 核心功能 (3-4天)
1. 预约管理模块
2. 客户管理模块
3. 服务项目管理模块

### Phase 3: 高级功能 (2-3天)
1. 员工管理模块
2. 数据统计模块
3. 系统优化和测试

## 📱 响应式设计

### 断点设计
- Desktop: >= 1024px (完整侧边栏)
- Tablet: 768px - 1023px (可折叠侧边栏)
- Mobile: < 768px (底部导航栏)

### 布局适配
- 桌面端：固定侧边栏 + 主内容区
- 平板端：可折叠侧边栏 + 主内容区
- 移动端：顶部导航 + 底部标签栏

## 🔧 技术栈

### 前端技术
- Next.js 15 (App Router)
- React 19 + TypeScript
- Tailwind CSS 4
- shadcn/ui 组件库
- lucide-react 图标库

### 数据管理
- 本地 JSON 文件存储
- React Context 状态管理
- 自定义 hooks 数据操作

### 开发工具
- ESLint + TypeScript 代码检查
- 现有的构建和部署流程
