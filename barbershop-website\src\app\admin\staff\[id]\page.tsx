"use client"

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import { AdminLayout, PageContainer, CardContainer } from '@/components/admin/layout/admin-layout'
import { Badge } from '@/components/admin/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  User, 
  Phone, 
  Mail,
  Clock,
  Star,
  Award,
  Edit,
  Calendar,
  TrendingUp,
  BarChart3
} from 'lucide-react'
import { staffStore, appointmentStore } from '@/lib/admin/storage'
import { Staff, Appointment } from '@/lib/types/admin'

export default function StaffDetailPage() {
  const params = useParams()
  const [staff, setStaff] = useState<Staff | null>(null)
  const [appointments, setAppointments] = useState<Appointment[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (params.id) {
      loadStaffData(params.id as string)
    }
  }, [params.id])

  const loadStaffData = (id: string) => {
    setLoading(true)
    try {
      const staffData = staffStore.getById(id)
      setStaff(staffData || null)

      // 获取相关预约记录
      const allAppointments = appointmentStore.getAll()
      const staffAppointments = allAppointments.filter(apt => apt.staffId === id)
      setAppointments(staffAppointments)
    } catch (error) {
      console.error('Failed to load staff data:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatWorkingHours = (hours: string[]) => {
    if (!hours || hours.length === 0) return '未设置'
    if (hours.length === 7) return '全周'
    
    const dayNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    const workingDays = hours.map(h => {
      const dayIndex = parseInt(h) - 1
      return dayNames[dayIndex] || h
    })
    
    return workingDays.join(', ')
  }

  const getStaffStats = () => {
    const completedAppointments = appointments.filter(apt => apt.status === 'completed')
    const totalRevenue = completedAppointments.reduce((sum, apt) => {
      return sum + apt.services.reduce((serviceSum, service) => serviceSum + service.price, 0)
    }, 0)

    const thisMonth = new Date()
    thisMonth.setDate(1)
    const thisMonthAppointments = completedAppointments.filter(apt => 
      new Date(apt.date) >= thisMonth
    )

    const lastMonth = new Date(thisMonth)
    lastMonth.setMonth(lastMonth.getMonth() - 1)
    const lastMonthAppointments = completedAppointments.filter(apt => {
      const aptDate = new Date(apt.date)
      return aptDate >= lastMonth && aptDate < thisMonth
    })

    return {
      totalAppointments: appointments.length,
      completedAppointments: completedAppointments.length,
      totalRevenue,
      thisMonthAppointments: thisMonthAppointments.length,
      lastMonthAppointments: lastMonthAppointments.length,
      avgRating: staff?.rating || 0
    }
  }

  const getRecentAppointments = () => {
    return appointments
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, 5)
  }

  if (loading) {
    return (
      <AdminLayout>
        <PageContainer>
          <div className="flex items-center justify-center py-12">
            <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin" />
            <span className="ml-2">加载中...</span>
          </div>
        </PageContainer>
      </AdminLayout>
    )
  }

  if (!staff) {
    return (
      <AdminLayout>
        <PageContainer>
          <div className="text-center py-12">
            <p className="text-muted-foreground">员工不存在</p>
          </div>
        </PageContainer>
      </AdminLayout>
    )
  }

  const stats = getStaffStats()
  const recentAppointments = getRecentAppointments()

  return (
    <AdminLayout>
      <PageContainer
        title={staff.name}
        description="员工详细信息和工作统计"
        action={
          <Link href={`/admin/staff/${staff.id}/edit`}>
            <Button>
              <Edit className="h-4 w-4 mr-2" />
              编辑员工
            </Button>
          </Link>
        }
      >
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 员工基本信息 */}
          <div className="lg:col-span-1">
            <CardContainer title="基本信息">
              <div className="space-y-6">
                {/* 员工头像和状态 */}
                <div className="text-center">
                  <div className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl font-bold text-primary">
                      {staff.name.charAt(0)}
                    </span>
                  </div>
                  <h3 className="text-xl font-semibold">{staff.name}</h3>
                  <div className="flex justify-center space-x-2 mt-2">
                    <Badge variant={staff.isActive ? 'success' : 'destructive'} size="sm">
                      {staff.isActive ? '在职' : '离职'}
                    </Badge>
                    {staff.isManager && (
                      <Badge variant="default" size="sm" className="bg-orange-500">
                        <Award className="h-3 w-3 mr-1" />
                        管理员
                      </Badge>
                    )}
                  </div>
                </div>

                {/* 联系信息 */}
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{staff.phone}</span>
                  </div>

                  {staff.email && (
                    <div className="flex items-center space-x-3">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{staff.email}</span>
                    </div>
                  )}

                  <div className="flex items-center space-x-3">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <div className="text-sm">
                      <div>{formatWorkingHours(staff.workingHours)}</div>
                      <div className="text-muted-foreground">
                        {staff.startTime} - {staff.endTime}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <Star className="h-4 w-4 text-yellow-500 fill-current" />
                    <span className="text-sm font-medium">{staff.rating.toFixed(1)} 分</span>
                  </div>

                  <div className="flex items-center space-x-3">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">
                      入职时间：{new Date(staff.createdAt).toLocaleDateString('zh-CN')}
                    </span>
                  </div>
                </div>

                {/* 专业技能 */}
                <div className="pt-4 border-t">
                  <h4 className="font-medium mb-3">专业技能</h4>
                  <div className="flex flex-wrap gap-2">
                    {staff.specialties && staff.specialties.length > 0 ? (
                      staff.specialties.map((skill, index) => (
                        <Badge key={index} variant="outline" size="sm">
                          {skill}
                        </Badge>
                      ))
                    ) : (
                      <span className="text-sm text-muted-foreground">暂无技能设置</span>
                    )}
                  </div>
                </div>

                {/* 备注信息 */}
                {staff.notes && (
                  <div className="pt-4 border-t">
                    <h4 className="font-medium mb-2">备注信息</h4>
                    <p className="text-sm text-muted-foreground">{staff.notes}</p>
                  </div>
                )}
              </div>
            </CardContainer>

            {/* 工作统计 */}
            <CardContainer title="工作统计" className="mt-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">{stats.totalAppointments}</div>
                  <div className="text-sm text-muted-foreground">总预约</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{stats.completedAppointments}</div>
                  <div className="text-sm text-muted-foreground">已完成</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">¥{stats.totalRevenue}</div>
                  <div className="text-sm text-muted-foreground">总收入</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{stats.thisMonthAppointments}</div>
                  <div className="text-sm text-muted-foreground">本月预约</div>
                </div>
              </div>
            </CardContainer>
          </div>

          {/* 预约记录 */}
          <div className="lg:col-span-2">
            <CardContainer
              title="预约记录"
              action={
                <Link href={`/admin/appointments?staffId=${staff.id}`}>
                  <Button size="sm" variant="outline">
                    查看全部
                  </Button>
                </Link>
              }
            >
              <div className="space-y-4">
                {recentAppointments.length > 0 ? (
                  recentAppointments.map(appointment => (
                    <div key={appointment.id} className="border border-border rounded-lg p-4 hover:bg-accent/5 transition-colors">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <span className="font-medium">{appointment.customerName}</span>
                            <Badge 
                              variant={
                                appointment.status === 'completed' ? 'success' :
                                appointment.status === 'confirmed' ? 'default' :
                                appointment.status === 'pending' ? 'warning' : 'destructive'
                              } 
                              size="sm"
                            >
                              {appointment.status === 'pending' && '待确认'}
                              {appointment.status === 'confirmed' && '已确认'}
                              {appointment.status === 'in_progress' && '进行中'}
                              {appointment.status === 'completed' && '已完成'}
                              {appointment.status === 'cancelled' && '已取消'}
                              {appointment.status === 'no_show' && '未到店'}
                            </Badge>
                          </div>
                          
                          <div className="space-y-1 text-sm text-muted-foreground">
                            <div>
                              预约时间：{new Date(appointment.date).toLocaleDateString('zh-CN')} {appointment.startTime}
                            </div>
                            <div>
                              服务项目：{appointment.services.map(s => s.serviceName).join(', ')}
                            </div>
                            {appointment.notes && (
                              <div>
                                备注：{appointment.notes}
                              </div>
                            )}
                          </div>
                        </div>
                        
                        <div className="text-right">
                          <div className="text-lg font-semibold text-primary">
                            ¥{appointment.services.reduce((sum, s) => sum + s.price, 0)}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {appointment.services.reduce((sum, s) => sum + s.duration, 0)}分钟
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex justify-end mt-3">
                        <Link href={`/admin/appointments/${appointment.id}`}>
                          <Button variant="outline" size="sm">
                            查看详情
                          </Button>
                        </Link>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <Calendar className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                    <p className="text-muted-foreground">暂无预约记录</p>
                  </div>
                )}
              </div>
            </CardContainer>

            {/* 性能指标 */}
            <CardContainer title="性能指标" className="mt-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-muted/20 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{stats.avgRating.toFixed(1)}</div>
                  <div className="text-sm text-muted-foreground">客户评分</div>
                  <div className="flex justify-center mt-1">
                    {'★'.repeat(Math.floor(stats.avgRating))}
                    {'☆'.repeat(5 - Math.floor(stats.avgRating))}
                  </div>
                </div>
                
                <div className="text-center p-4 bg-muted/20 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {stats.completedAppointments > 0 ? Math.round((stats.completedAppointments / stats.totalAppointments) * 100) : 0}%
                  </div>
                  <div className="text-sm text-muted-foreground">完成率</div>
                </div>
                
                <div className="text-center p-4 bg-muted/20 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">
                    {stats.lastMonthAppointments > 0 ? 
                      Math.round(((stats.thisMonthAppointments - stats.lastMonthAppointments) / stats.lastMonthAppointments) * 100) : 
                      (stats.thisMonthAppointments > 0 ? 100 : 0)
                    }%
                  </div>
                  <div className="text-sm text-muted-foreground">月度增长</div>
                </div>
              </div>
            </CardContainer>
          </div>
        </div>
      </PageContainer>
    </AdminLayout>
  )
}
