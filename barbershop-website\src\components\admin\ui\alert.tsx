"use client"

import { useState, useEffect } from 'react'
import { CheckCircle, XCircle, AlertCircle, Info, X } from 'lucide-react'
import { cn } from '@/lib/utils'

export interface AlertProps {
  type?: 'success' | 'error' | 'warning' | 'info'
  title?: string
  message: string
  autoClose?: boolean
  autoCloseDelay?: number
  onClose?: () => void
  className?: string
}

const alertStyles = {
  success: {
    container: 'bg-green-50 border-green-200 text-green-800',
    icon: CheckCircle,
    iconColor: 'text-green-600'
  },
  error: {
    container: 'bg-red-50 border-red-200 text-red-800',
    icon: XCircle,
    iconColor: 'text-red-600'
  },
  warning: {
    container: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    icon: AlertCircle,
    iconColor: 'text-yellow-600'
  },
  info: {
    container: 'bg-blue-50 border-blue-200 text-blue-800',
    icon: Info,
    iconColor: 'text-blue-600'
  }
}

export function Alert({
  type = 'info',
  title,
  message,
  autoClose = false,
  autoCloseDelay = 5000,
  onClose,
  className
}: AlertProps) {
  const [isVisible, setIsVisible] = useState(true)
  const style = alertStyles[type]
  const Icon = style.icon

  useEffect(() => {
    if (autoClose && autoCloseDelay > 0) {
      const timer = setTimeout(() => {
        handleClose()
      }, autoCloseDelay)

      return () => clearTimeout(timer)
    }
  }, [autoClose, autoCloseDelay])

  const handleClose = () => {
    setIsVisible(false)
    onClose?.()
  }

  if (!isVisible) {
    return null
  }

  return (
    <div className={cn(
      'border rounded-lg p-4 flex items-start space-x-3',
      style.container,
      className
    )}>
      <Icon className={cn('h-5 w-5 mt-0.5 flex-shrink-0', style.iconColor)} />
      <div className="flex-1 min-w-0">
        {title && (
          <h4 className="font-medium mb-1">{title}</h4>
        )}
        <p className="text-sm">{message}</p>
      </div>
      {onClose && (
        <button
          onClick={handleClose}
          className="flex-shrink-0 p-1 hover:bg-black/5 rounded-md transition-colors"
        >
          <X className="h-4 w-4" />
        </button>
      )}
    </div>
  )
}

// 成功提示的快捷组件
export function SuccessAlert({ message, onClose, ...props }: Omit<AlertProps, 'type'>) {
  return (
    <Alert
      type="success"
      message={message}
      onClose={onClose}
      autoClose={true}
      {...props}
    />
  )
}

// 错误提示的快捷组件
export function ErrorAlert({ message, onClose, ...props }: Omit<AlertProps, 'type'>) {
  return (
    <Alert
      type="error"
      message={message}
      onClose={onClose}
      {...props}
    />
  )
}
