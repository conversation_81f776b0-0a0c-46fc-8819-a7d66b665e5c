import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Scissors, Zap, Sparkles, Brush } from "lucide-react"

const services = [
  {
    icon: Scissors,
    title: "经典理发",
    description: "专业理发师为您提供精准的剪发服务，打造适合您的完美发型",
    price: "¥80-120",
    duration: "45分钟",
  },
  {
    icon: Zap,
    title: "胡须修剪",
    description: "精细的胡须造型和修剪，让您的面部轮廓更加立体有型",
    price: "¥60-80",
    duration: "30分钟",
  },
  {
    icon: Sparkles,
    title: "头发造型",
    description: "使用专业造型产品，为您打造时尚个性的发型造型",
    price: "¥50-70",
    duration: "20分钟",
  },
  {
    icon: Brush,
    title: "洗发护理",
    description: "深层清洁和滋养护理，让您的头发健康有光泽",
    price: "¥40-60",
    duration: "25分钟",
  },
]

export function ServicesPreview() {
  return (
    <section className="py-20 bg-muted/50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            专业服务项目
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            我们提供全方位的男士理发和护理服务，每一项服务都体现我们对品质的追求
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {services.map((service, index) => {
            const IconComponent = service.icon
            return (
              <Card key={index} className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 p-3 bg-primary/10 rounded-full w-fit group-hover:bg-primary/20 transition-colors">
                    <IconComponent className="h-8 w-8 text-primary" />
                  </div>
                  <CardTitle className="text-xl">{service.title}</CardTitle>
                  <CardDescription className="text-sm">
                    {service.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-center">
                  <div className="space-y-2">
                    <div className="text-2xl font-bold text-primary">
                      {service.price}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {service.duration}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* CTA */}
        <div className="text-center">
          <Button asChild size="lg" className="px-8">
            <Link href="/services">查看所有服务</Link>
          </Button>
        </div>
      </div>
    </section>
  )
}
