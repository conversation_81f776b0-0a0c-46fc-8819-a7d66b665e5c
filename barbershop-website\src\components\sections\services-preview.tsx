"use client"

import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { FadeIn, StaggeredFadeIn, ScaleIn } from "@/components/animations/fade-in"

const services = [
  {
    icon: "✂️",
    title: "经典理发",
    description: "专业理发师为您提供精准的剪发服务，打造适合您的完美发型",
    price: "¥80-120",
    duration: "45分钟",
  },
  {
    icon: "⚡",
    title: "胡须修剪",
    description: "精细的胡须造型和修剪，让您的面部轮廓更加立体有型",
    price: "¥60-80",
    duration: "30分钟",
  },
  {
    icon: "✨",
    title: "头发造型",
    description: "使用专业造型产品，为您打造时尚个性的发型造型",
    price: "¥50-70",
    duration: "20分钟",
  },
  {
    icon: "🧴",
    title: "洗发护理",
    description: "深层清洁和滋养护理，让您的头发健康有光泽",
    price: "¥40-60",
    duration: "25分钟",
  },
]

export function ServicesPreview() {
  return (
    <section className="py-20 bg-muted/50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <FadeIn direction="up" distance={30}>
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              专业服务项目
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              我们提供全方位的男士理发和护理服务，每一项服务都体现我们对品质的追求
            </p>
          </div>
        </FadeIn>

        {/* Services Grid */}
        <StaggeredFadeIn delay={200} staggerDelay={150} direction="up" distance={30}>
          {services.map((service, index) => (
            <ScaleIn delay={index * 100} key={index}>
              <Link href="/services" className="block">
                <Card hover interactive className="group h-full">
                  <CardHeader className="text-center">
                    <div className="mx-auto mb-4 p-3 bg-primary/10 rounded-full w-fit group-hover:bg-primary/20 transition-colors">
                      <span className="text-3xl">{service.icon}</span>
                    </div>
                    <CardTitle className="text-xl">{service.title}</CardTitle>
                    <CardDescription className="text-sm">
                      {service.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="text-center">
                    <div className="space-y-2">
                      <div className="text-2xl font-bold text-primary">
                        {service.price}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {service.duration}
                      </div>
                    </div>
                    <div className="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div className="text-sm text-primary font-medium flex items-center justify-center gap-1">
                        点击查看详情 <span className="text-xs">→</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            </ScaleIn>
          ))}
        </StaggeredFadeIn>

        {/* CTA */}
        <FadeIn delay={800} direction="up" distance={20}>
          <div className="text-center">
            <Button asChild size="lg" variant="shine" className="px-8" icon="👁️" rightIcon="→">
              <Link href="/services">查看所有服务</Link>
            </Button>
          </div>
        </FadeIn>
      </div>
    </section>
  )
}
