"use client"

import { useEffect, useState } from 'react'
import { AdminLayout, PageContainer, CardContainer } from '@/components/admin/layout/admin-layout'
import { Badge } from '@/components/admin/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Calendar, 
  Users, 
  DollarSign, 
  TrendingUp,
  Clock,
  UserPlus,
  CalendarPlus,
  BarChart3,
  ArrowRight
} from 'lucide-react'
import { analyticsStore, initializeSampleData } from '@/lib/admin/storage'
import { Analytics } from '@/lib/types/admin'
import Link from 'next/link'

interface StatCardProps {
  title: string
  value: string | number
  change?: number
  changeType?: 'increase' | 'decrease' | 'neutral'
  icon: React.ComponentType<{ className?: string }>
  color: string
}

function StatCard({ title, value, change, changeType, icon: Icon, color }: StatCardProps) {
  const getChangeColor = () => {
    if (changeType === 'increase') return 'text-green-600'
    if (changeType === 'decrease') return 'text-red-600'
    return 'text-muted-foreground'
  }

  const getChangeIcon = () => {
    if (changeType === 'increase') return '↗'
    if (changeType === 'decrease') return '↘'
    return '→'
  }

  return (
    <CardContainer className="relative overflow-hidden">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          <p className="text-2xl font-bold text-foreground mt-1">{value}</p>
          {change !== undefined && (
            <p className={`text-sm mt-1 ${getChangeColor()}`}>
              {getChangeIcon()} {Math.abs(change)}% 较上月
            </p>
          )}
        </div>
        <div className={`p-3 rounded-full ${color}`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
      </div>
    </CardContainer>
  )
}

function QuickActions() {
  const actions = [
    {
      title: '新建预约',
      description: '为客户创建新的预约',
      icon: CalendarPlus,
      href: '/admin/appointments/new',
      color: 'bg-blue-500'
    },
    {
      title: '添加客户',
      description: '录入新客户信息',
      icon: UserPlus,
      href: '/admin/customers/new',
      color: 'bg-green-500'
    },
    {
      title: '查看统计',
      description: '查看详细数据报告',
      icon: BarChart3,
      href: '/admin/analytics',
      color: 'bg-purple-500'
    }
  ]

  return (
    <CardContainer title="快速操作" description="常用功能快捷入口">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {actions.map((action) => {
          const Icon = action.icon
          return (
            <Link
              key={action.title}
              href={action.href}
              className="p-4 border border-border rounded-lg hover:bg-accent transition-colors group"
            >
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-md ${action.color}`}>
                  <Icon className="h-5 w-5 text-white" />
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-foreground group-hover:text-accent-foreground">
                    {action.title}
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    {action.description}
                  </p>
                </div>
                <ArrowRight className="h-4 w-4 text-muted-foreground group-hover:text-accent-foreground" />
              </div>
            </Link>
          )
        })}
      </div>
    </CardContainer>
  )
}

function RecentAppointments() {
  // 模拟最近预约数据
  const recentAppointments = [
    {
      id: '1',
      customerName: '张先生',
      service: '经典理发',
      time: '14:30',
      status: 'confirmed' as const
    },
    {
      id: '2',
      customerName: '李女士',
      service: '洗剪吹套餐',
      time: '15:00',
      status: 'pending' as const
    },
    {
      id: '3',
      customerName: '王先生',
      service: '胡须修剪',
      time: '15:30',
      status: 'confirmed' as const
    }
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'confirmed':
        return <Badge variant="success" size="sm">已确认</Badge>
      case 'pending':
        return <Badge variant="warning" size="sm">待确认</Badge>
      case 'completed':
        return <Badge variant="default" size="sm">已完成</Badge>
      case 'cancelled':
        return <Badge variant="destructive" size="sm">已取消</Badge>
      default:
        return <Badge variant="outline" size="sm">{status}</Badge>
    }
  }

  return (
    <CardContainer 
      title="今日预约" 
      description="今天的预约安排"
      action={
        <Link href="/admin/appointments">
          <Button variant="outline" size="sm">
            查看全部
          </Button>
        </Link>
      }
    >
      <div className="space-y-3">
        {recentAppointments.map((appointment) => (
          <div key={appointment.id} className="flex items-center justify-between p-3 border border-border rounded-lg">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                <Clock className="h-5 w-5 text-primary" />
              </div>
              <div>
                <p className="font-medium text-foreground">{appointment.customerName}</p>
                <p className="text-sm text-muted-foreground">{appointment.service}</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm font-medium text-foreground">{appointment.time}</p>
              {getStatusBadge(appointment.status)}
            </div>
          </div>
        ))}
        
        {recentAppointments.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <Calendar className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p>今日暂无预约</p>
          </div>
        )}
      </div>
    </CardContainer>
  )
}

export default function AdminDashboard() {
  const [analytics, setAnalytics] = useState<Analytics | null>(null)

  useEffect(() => {
    // 初始化示例数据
    initializeSampleData()

    // 加载统计数据
    const data = analyticsStore.getAnalytics()
    setAnalytics(data)
  }, [])

  const stats = [
    {
      title: '今日预约',
      value: analytics?.appointments.todayTotal || 0,
      change: 12,
      changeType: 'increase' as const,
      icon: Calendar,
      color: 'bg-blue-500'
    },
    {
      title: '总客户数',
      value: analytics?.customers.total || 0,
      change: 8,
      changeType: 'increase' as const,
      icon: Users,
      color: 'bg-green-500'
    },
    {
      title: '本月营收',
      value: '¥12,580',
      change: 15,
      changeType: 'increase' as const,
      icon: DollarSign,
      color: 'bg-yellow-500'
    },
    {
      title: '服务完成率',
      value: '95%',
      change: 2,
      changeType: 'increase' as const,
      icon: TrendingUp,
      color: 'bg-purple-500'
    }
  ]

  return (
    <AdminLayout>
      <PageContainer>
        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat) => (
            <StatCard key={stat.title} {...stat} />
          ))}
        </div>

        {/* 主要内容区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 左侧：今日预约 */}
          <div className="lg:col-span-2">
            <RecentAppointments />
          </div>

          {/* 右侧：快速操作 */}
          <div>
            <QuickActions />
          </div>
        </div>

        {/* 欢迎信息 */}
        <CardContainer>
          <div className="text-center py-8">
            <div className="text-6xl mb-4">✂️</div>
            <h2 className="text-2xl font-bold text-foreground mb-2">
              欢迎使用 Tony's Barbershop 管理系统
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              这里是您的管理后台首页。您可以查看今日预约、管理客户信息、设置服务项目，以及查看营业数据统计。
              使用左侧导航菜单快速访问各个功能模块。
            </p>
          </div>
        </CardContainer>
      </PageContainer>
    </AdminLayout>
  )
}
