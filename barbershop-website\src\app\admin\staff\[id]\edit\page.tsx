"use client"

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { AdminLayout, PageContainer, CardContainer } from '@/components/admin/layout/admin-layout'
import { StaffForm } from '@/components/admin/staff/staff-form'
import { Edit } from 'lucide-react'
import { staffStore } from '@/lib/admin/storage'
import { Staff } from '@/lib/types/admin'

export default function EditStaffPage() {
  const params = useParams()
  const [staff, setStaff] = useState<Staff | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (params.id) {
      loadStaff(params.id as string)
    }
  }, [params.id])

  const loadStaff = (id: string) => {
    setLoading(true)
    try {
      const data = staffStore.getById(id)
      setStaff(data || null)
    } catch (error) {
      console.error('Failed to load staff:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <AdminLayout>
        <PageContainer>
          <div className="flex items-center justify-center py-12">
            <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin" />
            <span className="ml-2">加载中...</span>
          </div>
        </PageContainer>
      </AdminLayout>
    )
  }

  if (!staff) {
    return (
      <AdminLayout>
        <PageContainer>
          <div className="text-center py-12">
            <p className="text-muted-foreground">员工不存在</p>
          </div>
        </PageContainer>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <PageContainer
        title="编辑员工"
        description={`编辑员工信息 - ${staff.name}`}
      >
        <CardContainer
          title="编辑员工信息"
          description="修改员工的基本信息、专业技能和工作时间"
        >
          <StaffForm staff={staff} />
        </CardContainer>
      </PageContainer>
    </AdminLayout>
  )
}
