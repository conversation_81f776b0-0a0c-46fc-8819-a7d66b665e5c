"use client"

import { useState, useEffect } from 'react'
import { AdminLayout, PageContainer, CardContainer } from '@/components/admin/layout/admin-layout'
import { Button } from '@/components/ui/button'
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Users,
  Calendar,
  Scissors,
  UserCheck,
  Download,
  RefreshCw
} from 'lucide-react'
import {
  appointmentStore,
  customerStore,
  serviceStore,
  staffStore
} from '@/lib/admin/storage'

export default function AnalyticsPage() {
  const [loading, setLoading] = useState(true)
  const [dateRange, setDateRange] = useState('thisMonth')
  const [analytics, setAnalytics] = useState<{
    overview: {
      totalRevenue: number
      totalAppointments: number
      completedAppointments: number
      newCustomers: number
      activeStaff: number
      revenueGrowth: number
      appointmentGrowth: number
    }
    services: Array<{
      id: string
      name: string
      count: number
      revenue: number
      percentage: number
    }>
    staff: Array<{
      id: string
      name: string
      appointments: number
      revenue: number
      rating: number
    }>
    trends: {
      daily: Array<{
        date: string
        revenue: number
        appointments: number
      }>
    }
    customers: {
      total: number
      new: number
      returning: number
    }
  } | null>(null)

  useEffect(() => {
    loadAnalytics()
  }, [dateRange])

  const loadAnalytics = () => {
    setLoading(true)
    try {
      // 获取基础数据
      const appointments = appointmentStore.getAll()
      const customers = customerStore.getAll()
      const services = serviceStore.getAll()
      const staff = staffStore.getAll()

      // 计算时间范围
      const now = new Date()
      let startDate: Date
      let endDate = new Date(now)

      switch (dateRange) {
        case 'today':
          startDate = new Date(now)
          startDate.setHours(0, 0, 0, 0)
          endDate.setHours(23, 59, 59, 999)
          break
        case 'thisWeek':
          startDate = new Date(now)
          startDate.setDate(now.getDate() - now.getDay())
          startDate.setHours(0, 0, 0, 0)
          break
        case 'thisMonth':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1)
          break
        case 'lastMonth':
          startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1)
          endDate = new Date(now.getFullYear(), now.getMonth(), 0)
          endDate.setHours(23, 59, 59, 999)
          break
        case 'thisYear':
          startDate = new Date(now.getFullYear(), 0, 1)
          break
        default:
          startDate = new Date(now.getFullYear(), now.getMonth(), 1)
      }

      // 过滤时间范围内的预约
      const filteredAppointments = appointments.filter(apt => {
        const aptDate = new Date(apt.date)
        return aptDate >= startDate && aptDate <= endDate
      })

      const completedAppointments = filteredAppointments.filter(apt => apt.status === 'completed')

      // 计算收入
      const totalRevenue = completedAppointments.reduce((sum, apt) => {
        return sum + apt.services.reduce((serviceSum, service) => serviceSum + service.price, 0)
      }, 0)

      // 计算上期数据用于对比
      const prevStartDate = new Date(startDate)
      const prevEndDate = new Date(endDate)
      const timeDiff = endDate.getTime() - startDate.getTime()
      prevStartDate.setTime(startDate.getTime() - timeDiff)
      prevEndDate.setTime(endDate.getTime() - timeDiff)

      const prevAppointments = appointments.filter(apt => {
        const aptDate = new Date(apt.date)
        return aptDate >= prevStartDate && aptDate <= prevEndDate && apt.status === 'completed'
      })

      const prevRevenue = prevAppointments.reduce((sum, apt) => {
        return sum + apt.services.reduce((serviceSum, service) => serviceSum + service.price, 0)
      }, 0)

      // 服务统计
      const serviceStats = services.map(service => {
        const serviceAppointments = completedAppointments.filter(apt => 
          apt.services.some(s => s.serviceId === service.id)
        )
        const revenue = serviceAppointments.reduce((sum, apt) => {
          const serviceInApt = apt.services.find(s => s.serviceId === service.id)
          return sum + (serviceInApt?.price || 0)
        }, 0)

        return {
          id: service.id,
          name: service.name,
          count: serviceAppointments.length,
          revenue,
          percentage: totalRevenue > 0 ? (revenue / totalRevenue) * 100 : 0
        }
      }).sort((a, b) => b.revenue - a.revenue)

      // 员工统计
      const staffStats = staff.map(staffMember => {
        const staffAppointments = completedAppointments.filter(apt => apt.staffId === staffMember.id)
        const revenue = staffAppointments.reduce((sum, apt) => {
          return sum + apt.services.reduce((serviceSum, service) => serviceSum + service.price, 0)
        }, 0)

        return {
          id: staffMember.id,
          name: staffMember.name,
          appointments: staffAppointments.length,
          revenue,
          rating: staffMember.rating
        }
      }).sort((a, b) => b.revenue - a.revenue)

      // 客户统计
      const newCustomers = customers.filter(customer => {
        const customerDate = new Date(customer.createdAt)
        return customerDate >= startDate && customerDate <= endDate
      })

      // 每日收入趋势（最近7天）
      const dailyRevenue = []
      for (let i = 6; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        date.setHours(0, 0, 0, 0)
        
        const nextDate = new Date(date)
        nextDate.setDate(date.getDate() + 1)
        
        const dayAppointments = appointments.filter(apt => {
          const aptDate = new Date(apt.date)
          return aptDate >= date && aptDate < nextDate && apt.status === 'completed'
        })
        
        const dayRevenue = dayAppointments.reduce((sum, apt) => {
          return sum + apt.services.reduce((serviceSum, service) => serviceSum + service.price, 0)
        }, 0)
        
        dailyRevenue.push({
          date: date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }),
          revenue: dayRevenue,
          appointments: dayAppointments.length
        })
      }

      setAnalytics({
        overview: {
          totalRevenue,
          totalAppointments: filteredAppointments.length,
          completedAppointments: completedAppointments.length,
          newCustomers: newCustomers.length,
          activeStaff: staff.filter(s => s.isActive).length,
          revenueGrowth: prevRevenue > 0 ? ((totalRevenue - prevRevenue) / prevRevenue) * 100 : 0,
          appointmentGrowth: prevAppointments.length > 0 ? 
            ((completedAppointments.length - prevAppointments.length) / prevAppointments.length) * 100 : 0
        },
        services: serviceStats,
        staff: staffStats,
        trends: {
          daily: dailyRevenue
        },
        customers: {
          total: customers.length,
          new: newCustomers.length,
          returning: completedAppointments.filter(apt => {
            const customerAppointments = appointments.filter(a => 
              a.customerId === apt.customerId && a.status === 'completed'
            )
            return customerAppointments.length > 1
          }).length
        }
      })
    } catch (error) {
      console.error('Failed to load analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  const exportData = () => {
    // 导出数据功能
    const data = {
      analytics,
      exportDate: new Date().toISOString(),
      dateRange
    }
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `analytics-${dateRange}-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  if (loading) {
    return (
      <AdminLayout>
        <PageContainer>
          <div className="flex items-center justify-center py-12">
            <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin" />
            <span className="ml-2">加载中...</span>
          </div>
        </PageContainer>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <PageContainer
        title="数据统计"
        description="营业数据分析和业务洞察"
        action={
          <div className="flex space-x-2">
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="px-3 py-2 border border-border rounded-md bg-background text-sm"
            >
              <option value="today">今天</option>
              <option value="thisWeek">本周</option>
              <option value="thisMonth">本月</option>
              <option value="lastMonth">上月</option>
              <option value="thisYear">今年</option>
            </select>
            <Button variant="outline" onClick={loadAnalytics}>
              <RefreshCw className="h-4 w-4 mr-2" />
              刷新
            </Button>
            <Button variant="outline" onClick={exportData}>
              <Download className="h-4 w-4 mr-2" />
              导出
            </Button>
          </div>
        }
      >
        {analytics && (
          <div className="space-y-6">
            {/* 概览统计 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-card border border-border rounded-lg p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">总收入</p>
                    <p className="text-2xl font-bold text-primary">¥{analytics.overview.totalRevenue}</p>
                  </div>
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                    <DollarSign className="h-6 w-6 text-green-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center">
                  {analytics.overview.revenueGrowth >= 0 ? (
                    <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                  )}
                  <span className={`text-sm ${analytics.overview.revenueGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {Math.abs(analytics.overview.revenueGrowth).toFixed(1)}%
                  </span>
                  <span className="text-sm text-muted-foreground ml-1">vs 上期</span>
                </div>
              </div>

              <div className="bg-card border border-border rounded-lg p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">完成预约</p>
                    <p className="text-2xl font-bold text-blue-600">{analytics.overview.completedAppointments}</p>
                  </div>
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <Calendar className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
                <div className="mt-4 flex items-center">
                  {analytics.overview.appointmentGrowth >= 0 ? (
                    <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                  )}
                  <span className={`text-sm ${analytics.overview.appointmentGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {Math.abs(analytics.overview.appointmentGrowth).toFixed(1)}%
                  </span>
                  <span className="text-sm text-muted-foreground ml-1">vs 上期</span>
                </div>
              </div>

              <div className="bg-card border border-border rounded-lg p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">新客户</p>
                    <p className="text-2xl font-bold text-orange-600">{analytics.customers.new}</p>
                  </div>
                  <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
                    <Users className="h-6 w-6 text-orange-600" />
                  </div>
                </div>
                <div className="mt-4">
                  <span className="text-sm text-muted-foreground">
                    总客户: {analytics.customers.total}
                  </span>
                </div>
              </div>

              <div className="bg-card border border-border rounded-lg p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">活跃员工</p>
                    <p className="text-2xl font-bold text-purple-600">{analytics.overview.activeStaff}</p>
                  </div>
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                    <UserCheck className="h-6 w-6 text-purple-600" />
                  </div>
                </div>
                <div className="mt-4">
                  <span className="text-sm text-muted-foreground">
                    平均评分: {analytics.staff.length > 0 ? 
                      (analytics.staff.reduce((sum: number, s: any) => sum + s.rating, 0) / analytics.staff.length).toFixed(1) : 
                      '0.0'
                    }
                  </span>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 收入趋势 */}
              <CardContainer title="收入趋势">
                <div className="space-y-4">
                  <div className="h-64 flex items-end justify-between space-x-2">
                    {analytics.trends.daily.map((day, index: number) => (
                      <div key={index} className="flex-1 flex flex-col items-center">
                        <div
                          className="w-full bg-primary/20 rounded-t-md flex items-end justify-center relative"
                          style={{
                            height: `${Math.max((day.revenue / Math.max(...analytics.trends.daily.map(d => d.revenue))) * 200, 4)}px`
                          }}
                        >
                          <div className="absolute -top-6 text-xs font-medium text-primary">
                            ¥{day.revenue}
                          </div>
                        </div>
                        <div className="text-xs text-muted-foreground mt-2">{day.date}</div>
                        <div className="text-xs text-muted-foreground">{day.appointments}单</div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContainer>

              {/* 热门服务 */}
              <CardContainer title="热门服务">
                <div className="space-y-3">
                  {analytics.services.slice(0, 5).map((service, index: number) => (
                    <div key={service.id} className="flex items-center justify-between p-3 bg-muted/20 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                          <span className="text-sm font-bold text-primary">{index + 1}</span>
                        </div>
                        <div>
                          <p className="font-medium">{service.name}</p>
                          <p className="text-sm text-muted-foreground">{service.count} 次服务</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-primary">¥{service.revenue}</p>
                        <p className="text-sm text-muted-foreground">{service.percentage.toFixed(1)}%</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContainer>
            </div>

            {/* 员工表现 */}
            <CardContainer title="员工表现">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {analytics.staff.map((staffMember) => (
                  <div key={staffMember.id} className="p-4 border border-border rounded-lg">
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                        <span className="font-semibold text-primary">
                          {staffMember.name.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium">{staffMember.name}</p>
                        <div className="flex items-center space-x-1">
                          <span className="text-sm text-yellow-500">★</span>
                          <span className="text-sm text-muted-foreground">{staffMember.rating.toFixed(1)}</span>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">预约数量</span>
                        <span className="font-medium">{staffMember.appointments}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">创造收入</span>
                        <span className="font-medium text-primary">¥{staffMember.revenue}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContainer>
          </div>
        )}
      </PageContainer>
    </AdminLayout>
  )
}
