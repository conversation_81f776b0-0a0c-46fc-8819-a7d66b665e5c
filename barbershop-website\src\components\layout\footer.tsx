import Link from "next/link"

export function Footer() {
  return (
    <footer className="bg-primary text-primary-foreground">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <span className="text-2xl text-accent">✂️</span>
              <span className="text-xl font-bold">Classic Cuts</span>
            </div>
            <p className="text-sm text-primary-foreground/90">
              专业的男士理发服务，传统工艺与现代风格的完美结合。
            </p>
            <div className="flex space-x-4">
              <Link href="#" className="text-primary-foreground/90 hover:text-accent transition-colors">
                <span className="text-xl">📘</span>
                <span className="sr-only">Facebook</span>
              </Link>
              <Link href="#" className="text-primary-foreground/90 hover:text-accent transition-colors">
                <span className="text-xl">📷</span>
                <span className="sr-only">Instagram</span>
              </Link>
              <Link href="#" className="text-primary-foreground/90 hover:text-accent transition-colors">
                <span className="text-xl">🐦</span>
                <span className="sr-only">Twitter</span>
              </Link>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">快速链接</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/" className="text-sm text-primary-foreground/80 hover:text-accent transition-colors">
                  首页
                </Link>
              </li>
              <li>
                <Link href="/services" className="text-sm text-primary-foreground/80 hover:text-accent transition-colors">
                  服务项目
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-sm text-primary-foreground/80 hover:text-accent transition-colors">
                  关于我们
                </Link>
              </li>
              <li>
                <Link href="/gallery" className="text-sm text-primary-foreground/80 hover:text-accent transition-colors">
                  作品展示
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-sm text-primary-foreground/80 hover:text-accent transition-colors">
                  联系我们
                </Link>
              </li>
            </ul>
          </div>

          {/* Services */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">服务项目</h3>
            <ul className="space-y-2">
              <li className="text-sm text-primary-foreground/80">经典理发</li>
              <li className="text-sm text-primary-foreground/80">胡须修剪</li>
              <li className="text-sm text-primary-foreground/80">头发造型</li>
              <li className="text-sm text-primary-foreground/80">洗发护理</li>
              <li className="text-sm text-primary-foreground/80">面部护理</li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">联系信息</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <span className="text-accent">📍</span>
                <span className="text-sm text-primary-foreground/80">
                  123 Main Street, City, State 12345
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-accent">📞</span>
                <span className="text-sm text-primary-foreground/80">
                  (555) 123-4567
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-accent">📧</span>
                <span className="text-sm text-primary-foreground/80">
                  <EMAIL>
                </span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-accent">🕐</span>
                <div className="text-sm text-primary-foreground/80">
                  <div>周一-周五: 9:00 AM - 8:00 PM</div>
                  <div>周六: 8:00 AM - 6:00 PM</div>
                  <div>周日: 10:00 AM - 4:00 PM</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-8 pt-8 border-t border-primary-foreground/20">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-primary-foreground/80">
              © 2024 Classic Cuts Barbershop. All rights reserved.
            </p>
            <div className="flex space-x-4 mt-4 md:mt-0">
              <Link href="/privacy" className="text-sm text-primary-foreground/80 hover:text-accent transition-colors">
                隐私政策
              </Link>
              <Link href="/terms" className="text-sm text-primary-foreground/80 hover:text-accent transition-colors">
                服务条款
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
