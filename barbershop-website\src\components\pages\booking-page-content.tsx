"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { FadeIn, StaggeredFadeIn, SlideIn } from "@/components/animations/fade-in"
import { FloatingElement } from "@/components/animations/page-transition"
import { appointmentStore, customerStore, serviceStore, staffStore } from "@/lib/admin/storage"
import { Service, Staff } from "@/lib/types/admin"
// 使用标准HTML表单元素，确保可访问性

interface BookingData {
  service: string
  barber: string
  date: string
  time: string
  name: string
  phone: string
  email: string
  notes: string
}

interface BookingErrors {
  service?: string
  barber?: string
  date?: string
  time?: string
  name?: string
  phone?: string
  email?: string
}

// 时间段选项保持不变，因为这是通用的

const timeSlots = [
  { value: "", label: "请选择时间" },
  { value: "09:00", label: "09:00" },
  { value: "09:30", label: "09:30" },
  { value: "10:00", label: "10:00" },
  { value: "10:30", label: "10:30" },
  { value: "11:00", label: "11:00" },
  { value: "11:30", label: "11:30" },
  { value: "12:00", label: "12:00" },
  { value: "12:30", label: "12:30" },
  { value: "13:00", label: "13:00" },
  { value: "13:30", label: "13:30" },
  { value: "14:00", label: "14:00" },
  { value: "14:30", label: "14:30" },
  { value: "15:00", label: "15:00" },
  { value: "15:30", label: "15:30" },
  { value: "16:00", label: "16:00" },
  { value: "16:30", label: "16:30" },
  { value: "17:00", label: "17:00" },
  { value: "17:30", label: "17:30" },
  { value: "18:00", label: "18:00" },
  { value: "18:30", label: "18:30" },
  { value: "19:00", label: "19:00" },
  { value: "19:30", label: "19:30" },
  { value: "20:00", label: "20:00" }
]

export function BookingPageContent() {
  const [bookingData, setBookingData] = useState<BookingData>({
    service: "",
    barber: "",
    date: "",
    time: "",
    name: "",
    phone: "",
    email: "",
    notes: ""
  })

  const [errors, setErrors] = useState<BookingErrors>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<"idle" | "success" | "error">("idle")

  // 从后台数据加载服务和员工信息
  const [services, setServices] = useState<Service[]>([])
  const [staff, setStaff] = useState<Staff[]>([])

  useEffect(() => {
    loadData()
  }, [])

  const loadData = () => {
    try {
      const activeServices = serviceStore.getAll().filter(s => s.isActive)
      const activeStaff = staffStore.getAll().filter(s => s.isActive)
      setServices(activeServices)
      setStaff(activeStaff)
    } catch (error) {
      console.error('Failed to load data:', error)
    }
  }

  const validateForm = (): boolean => {
    const newErrors: BookingErrors = {}

    if (!bookingData.service) {
      newErrors.service = "请选择服务类型"
    } else if (!services.find(s => s.id === bookingData.service)) {
      newErrors.service = "选择的服务不存在"
    }

    if (!bookingData.barber) {
      newErrors.barber = "请选择理发师"
    } else if (!staff.find(s => s.id === bookingData.barber)) {
      newErrors.barber = "选择的理发师不存在"
    }

    if (!bookingData.date) {
      newErrors.date = "请选择预约日期"
    } else {
      const selectedDate = new Date(bookingData.date)
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      if (selectedDate < today) {
        newErrors.date = "预约日期不能早于今天"
      }
    }

    if (!bookingData.time) {
      newErrors.time = "请选择预约时间"
    }

    if (!bookingData.name.trim()) {
      newErrors.name = "请输入您的姓名"
    }

    if (!bookingData.phone.trim()) {
      newErrors.phone = "请输入您的电话号码"
    } else if (!/^1[3-9]\d{9}$/.test(bookingData.phone.replace(/\s|-/g, ""))) {
      newErrors.phone = "请输入有效的手机号码"
    }

    if (bookingData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(bookingData.email)) {
      newErrors.email = "请输入有效的邮箱地址"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    setSubmitStatus("idle")

    try {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 查找选中的服务和员工
      const selectedService = services.find(s => s.id === bookingData.service)
      const selectedStaff = staff.find(s => s.id === bookingData.barber)

      if (!selectedService || !selectedStaff) {
        throw new Error('服务或员工信息不存在')
      }

      // 检查是否已存在客户，如果不存在则创建
      let existingCustomer = customerStore.getAll().find(c => c.phone === bookingData.phone)
      if (!existingCustomer) {
        existingCustomer = customerStore.create({
          name: bookingData.name,
          phone: bookingData.phone,
          email: bookingData.email || undefined,
          preferences: [],
          totalVisits: 0,
          totalSpent: 0
        })
      }

      // 计算结束时间
      const [startHour, startMinute] = bookingData.time.split(':').map(Number)
      const startDate = new Date()
      startDate.setHours(startHour, startMinute, 0, 0)
      const endDate = new Date(startDate.getTime() + selectedService.duration * 60000)
      const endTime = `${endDate.getHours().toString().padStart(2, '0')}:${endDate.getMinutes().toString().padStart(2, '0')}`

      // 创建预约记录
      const appointmentData = {
        customerId: existingCustomer.id,
        customerName: bookingData.name,
        customerPhone: bookingData.phone,
        staffId: bookingData.barber,
        staffName: selectedStaff.name,
        serviceIds: [bookingData.service],
        services: [{
          serviceId: selectedService.id,
          serviceName: selectedService.name,
          duration: selectedService.duration,
          price: selectedService.price
        }],
        date: bookingData.date,
        startTime: bookingData.time,
        endTime: endTime,
        duration: selectedService.duration,
        totalPrice: selectedService.price,
        status: 'pending' as const,
        notes: bookingData.notes || undefined
      }

      // 保存预约到localStorage
      const appointment = appointmentStore.create(appointmentData)
      console.log("预约创建成功:", appointment)

      setSubmitStatus("success")
      setBookingData({
        service: "",
        barber: "",
        date: "",
        time: "",
        name: "",
        phone: "",
        email: "",
        notes: ""
      })
      setErrors({})
    } catch (error) {
      console.error("预约提交失败:", error)
      setSubmitStatus("error")
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleInputChange = (field: keyof BookingData, value: string) => {
    setBookingData(prev => ({ ...prev, [field]: value }))
    if (errors[field as keyof BookingErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  // Get minimum date (today)
  const getMinDate = () => {
    const today = new Date()
    return today.toISOString().split('T')[0]
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-primary to-primary/80 text-primary-foreground">
        <FloatingElement delay={0}>
          <div className="container mx-auto px-4 text-center">
            <div className="max-w-3xl mx-auto">
              <FadeIn delay={0.2}>
                <h1 className="text-4xl md:text-5xl font-bold mb-6">
                  在线预约
                </h1>
              </FadeIn>
              <FadeIn delay={0.4}>
                <p className="text-xl mb-8 text-primary-foreground/90">
                  选择您喜欢的服务和理发师，预约您的专属时间。我们承诺为您提供最专业的服务体验。
                </p>
              </FadeIn>
              <div className="flex flex-wrap justify-center gap-6 text-sm">
                <StaggeredFadeIn delay={0.6}>
                  <div className="flex items-center space-x-2">
                    <span className="text-accent text-lg">⏰</span>
                    <span>灵活预约</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-accent text-lg">✂️</span>
                    <span>专业服务</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-accent text-lg">💯</span>
                    <span>满意保证</span>
                  </div>
                </StaggeredFadeIn>
              </div>
            </div>
          </div>
        </FloatingElement>
      </section>

      {/* Booking Form */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Booking Form */}
              <div className="lg:col-span-2">
                <SlideIn direction="left">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-2xl">预约信息</CardTitle>
                      <p className="text-muted-foreground">
                        请填写以下信息完成预约
                      </p>
                    </CardHeader>
                    <CardContent>
                      <form onSubmit={handleSubmit} className="space-y-6">
                        {/* Service Selection */}
                        <div>
                          <label htmlFor="service" className="block text-sm font-medium text-gray-700 mb-2">
                            选择服务 *
                          </label>
                          <select
                            id="service"
                            name="service"
                            value={bookingData.service}
                            onChange={(e) => handleInputChange("service", e.target.value)}
                            required
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                          >
                            <option value="">请选择服务</option>
                            {services.map((service) => (
                              <option key={service.id} value={service.id}>
                                {service.name} - ¥{service.price}
                              </option>
                            ))}
                          </select>
                          {errors.service && (
                            <p className="mt-1 text-sm text-red-600">{errors.service}</p>
                          )}
                        </div>

                        {/* Barber Selection */}
                        <div>
                          <label htmlFor="barber" className="block text-sm font-medium text-gray-700 mb-2">
                            选择理发师 *
                          </label>
                          <select
                            id="barber"
                            name="barber"
                            value={bookingData.barber}
                            onChange={(e) => handleInputChange("barber", e.target.value)}
                            required
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                          >
                            <option value="">请选择理发师</option>
                            {staff.map((barber) => (
                              <option key={barber.id} value={barber.id}>
                                {barber.name}
                              </option>
                            ))}
                          </select>
                          {errors.barber && (
                            <p className="mt-1 text-sm text-red-600">{errors.barber}</p>
                          )}
                        </div>

                        {/* Date and Time */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-2">
                              预约日期 *
                            </label>
                            <input
                              type="date"
                              id="date"
                              name="date"
                              value={bookingData.date}
                              onChange={(e) => handleInputChange("date", e.target.value)}
                              min={getMinDate()}
                              required
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                            />
                            {errors.date && (
                              <p className="mt-1 text-sm text-red-600">{errors.date}</p>
                            )}
                          </div>
                          <div>
                            <label htmlFor="time" className="block text-sm font-medium text-gray-700 mb-2">
                              预约时间 *
                            </label>
                            <select
                              id="time"
                              name="time"
                              value={bookingData.time}
                              onChange={(e) => handleInputChange("time", e.target.value)}
                              required
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                            >
                              <option value="">请选择时间</option>
                              {timeSlots.map((slot) => (
                                <option key={slot.value} value={slot.value}>
                                  {slot.label}
                                </option>
                              ))}
                            </select>
                            {errors.time && (
                              <p className="mt-1 text-sm text-red-600">{errors.time}</p>
                            )}
                          </div>
                        </div>

                        {/* Personal Information */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                              姓名 *
                            </label>
                            <input
                              type="text"
                              id="name"
                              name="name"
                              value={bookingData.name}
                              onChange={(e) => handleInputChange("name", e.target.value)}
                              placeholder="请输入您的姓名"
                              required
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                            />
                            {errors.name && (
                              <p className="mt-1 text-sm text-red-600">{errors.name}</p>
                            )}
                          </div>
                          <div>
                            <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                              电话 *
                            </label>
                            <input
                              type="tel"
                              id="phone"
                              name="phone"
                              value={bookingData.phone}
                              onChange={(e) => handleInputChange("phone", e.target.value)}
                              placeholder="请输入您的电话号码"
                              required
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                            />
                            {errors.phone && (
                              <p className="mt-1 text-sm text-red-600">{errors.phone}</p>
                            )}
                          </div>
                        </div>

                        <div>
                          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                            邮箱（可选）
                          </label>
                          <input
                            type="email"
                            id="email"
                            name="email"
                            value={bookingData.email}
                            onChange={(e) => handleInputChange("email", e.target.value)}
                            placeholder="请输入您的邮箱地址"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                          />
                          {errors.email && (
                            <p className="mt-1 text-sm text-red-600">{errors.email}</p>
                          )}
                        </div>

                        <div>
                          <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
                            备注（可选）
                          </label>
                          <textarea
                            id="notes"
                            name="notes"
                            value={bookingData.notes}
                            onChange={(e) => handleInputChange("notes", e.target.value)}
                            placeholder="如有特殊要求或备注，请在此说明..."
                            rows={3}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                          />
                        </div>

                        {submitStatus === "success" && (
                          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                            <div className="flex items-center space-x-2">
                              <span className="text-green-600">✓</span>
                              <span className="text-green-800">预约提交成功！我们会尽快确认您的预约。</span>
                            </div>
                          </div>
                        )}

                        {submitStatus === "error" && (
                          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                            <div className="flex items-center space-x-2">
                              <span className="text-red-600">✗</span>
                              <span className="text-red-800">预约提交失败，请稍后重试或电话预约。</span>
                            </div>
                          </div>
                        )}

                        <Button
                          type="submit"
                          className="w-full"
                          disabled={isSubmitting}
                          size="lg"
                        >
                          {isSubmitting ? "提交中..." : "确认预约"}
                        </Button>
                      </form>
                    </CardContent>
                  </Card>
                </SlideIn>
              </div>

              {/* Booking Info */}
              <div className="space-y-6">
                <SlideIn direction="right">
                  {/* Booking Tips */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-xl">预约须知</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-start space-x-3">
                        <span className="text-lg">⏰</span>
                        <div>
                          <h4 className="font-medium">预约时间</h4>
                          <p className="text-sm text-muted-foreground">
                            请提前至少2小时预约，我们会在1小时内确认您的预约
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-start space-x-3">
                        <span className="text-lg">📞</span>
                        <div>
                          <h4 className="font-medium">确认方式</h4>
                          <p className="text-sm text-muted-foreground">
                            我们会通过电话或短信确认您的预约信息
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-start space-x-3">
                        <span className="text-lg">🔄</span>
                        <div>
                          <h4 className="font-medium">取消政策</h4>
                          <p className="text-sm text-muted-foreground">
                            如需取消或更改，请提前2小时联系我们
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-start space-x-3">
                        <span className="text-lg">💳</span>
                        <div>
                          <h4 className="font-medium">支付方式</h4>
                          <p className="text-sm text-muted-foreground">
                            支持现金、刷卡、微信、支付宝等多种支付方式
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Contact Info */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-xl">联系我们</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex items-center space-x-3">
                        <span className="text-lg">📞</span>
                        <div>
                          <p className="font-medium">电话预约</p>
                          <p className="text-sm text-muted-foreground">010-8888-8888</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-3">
                        <span className="text-lg">📍</span>
                        <div>
                          <p className="font-medium">店铺地址</p>
                          <p className="text-sm text-muted-foreground">
                            北京市朝阳区三里屯太古里南区 S8-32号
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-3">
                        <span className="text-lg">🕒</span>
                        <div>
                          <p className="font-medium">营业时间</p>
                          <p className="text-sm text-muted-foreground">
                            周一至周日 10:00-21:00
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Emergency Contact */}
                  <Card className="bg-accent/10">
                    <CardContent className="pt-6">
                      <div className="text-center">
                        <h4 className="font-medium mb-2">紧急预约</h4>
                        <p className="text-sm text-muted-foreground mb-4">
                          如需当天预约或有紧急需求，请直接致电
                        </p>
                        <Button asChild variant="outline" className="w-full">
                          <a href="tel:+8610-8888-8888">
                            📞 立即致电
                          </a>
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </SlideIn>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
