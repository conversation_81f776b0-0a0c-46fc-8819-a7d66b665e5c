"use client"

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { AdminLayout, PageContainer, CardContainer } from '@/components/admin/layout/admin-layout'
import { AppointmentForm } from '@/components/admin/appointments/appointment-form'
import { Edit } from 'lucide-react'
import { appointmentStore } from '@/lib/admin/storage'
import { Appointment } from '@/lib/types/admin'

export default function EditAppointmentPage() {
  const params = useParams()
  const [appointment, setAppointment] = useState<Appointment | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (params.id) {
      loadAppointment(params.id as string)
    }
  }, [params.id])

  const loadAppointment = (id: string) => {
    setLoading(true)
    try {
      const data = appointmentStore.getById(id)
      setAppointment(data)
    } catch (error) {
      console.error('Failed to load appointment:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <AdminLayout>
        <PageContainer>
          <div className="flex items-center justify-center py-12">
            <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin" />
            <span className="ml-2">加载中...</span>
          </div>
        </PageContainer>
      </AdminLayout>
    )
  }

  if (!appointment) {
    return (
      <AdminLayout>
        <PageContainer>
          <div className="text-center py-12">
            <p className="text-muted-foreground">预约不存在</p>
          </div>
        </PageContainer>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <PageContainer
        title="编辑预约"
        description={`编辑预约信息 - ${appointment.customerName}`}
        breadcrumb={[
          { label: '预约管理', href: '/admin/appointments' },
          { label: '预约详情', href: `/admin/appointments/${appointment.id}` },
          { label: '编辑预约' }
        ]}
      >
        <CardContainer
          title="编辑预约信息"
          description="修改预约的详细信息"
          icon={Edit}
        >
          <AppointmentForm appointment={appointment} />
        </CardContainer>
      </PageContainer>
    </AdminLayout>
  )
}
