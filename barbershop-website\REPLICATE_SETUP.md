# Tony's Barbershop - Replicate API 图片生成指南

## 🔧 配置 Replicate API

### 1. 获取 API 密钥
1. 访问 [Replicate.com](https://replicate.com)
2. 注册账户并登录
3. 前往 [API Tokens 页面](https://replicate.com/account/api-tokens)
4. 创建新的 API Token
5. 复制生成的 token (格式: `r8_...`)

### 2. 配置环境变量
在项目根目录创建 `.env.local` 文件：
```bash
REPLICATE_API_TOKEN=r8_your_token_here
```

### 3. 安装依赖
```bash
npm install replicate
```

## 🎨 生成专业理发店图片

### 使用自动化脚本
```bash
# 设置环境变量
export REPLICATE_API_TOKEN="r8_your_token_here"

# 运行图片生成脚本
node scripts/generate-images.js
```

### 手动生成单张图片
```bash
# 生成 Hero 背景图
node scripts/generate-single-image.js hero

# 生成团队成员头像
node scripts/generate-single-image.js team

# 生成 Gallery 图片
node scripts/generate-single-image.js gallery
```

## 📋 需要生成的图片清单

### 主要图片 (6张)
- ✅ `hero-barbershop.jpg` (1920x1080) - Hero背景
- ✅ `about-story.jpg` (800x600) - 理发店历史
- ✅ `about-barbershop.jpg` (800x600) - 团队工作环境
- ✅ `team-member-1.jpg` (400x400) - 李师傅
- ✅ `team-member-2.jpg` (400x400) - 王师傅  
- ✅ `team-member-3.jpg` (400x400) - 张师傅

### Gallery 图片 (15张)
#### 理发作品 (6张)
- ✅ `gallery-haircut-1.jpg` - 经典商务短发
- ✅ `gallery-haircut-2.jpg` - 时尚渐变发型
- ✅ `gallery-haircut-3.jpg` - 个性创意发型
- ✅ `gallery-haircut-4.jpg` - 经典油头造型
- ✅ `gallery-haircut-5.jpg` - 青年时尚发型
- ✅ `gallery-haircut-6.jpg` - 婚礼造型

#### 胡须造型 (3张)
- ✅ `gallery-beard-1.jpg` - 精致胡须造型
- ✅ `gallery-beard-2.jpg` - 胡须精细修剪
- ✅ `gallery-beard-3.jpg` - 经典胡须搭配

#### 造型设计 (3张)
- ✅ `gallery-styling-1.jpg` - 复古油头造型
- ✅ `gallery-styling-2.jpg` - 特殊场合造型
- ✅ `gallery-styling-3.jpg` - 现代产品造型

#### 店内环境 (3张)
- ✅ `gallery-interior-1.jpg` - 等候区域
- ✅ `gallery-interior-2.jpg` - 专业设备
- ✅ `gallery-interior-3.jpg` - 店内环境

## 🎯 图片风格要求

### 整体风格
- **主题**: 现代中式理发店
- **色调**: 温暖色调 (金色、棕色、黑色)
- **氛围**: 专业、舒适、高端
- **质量**: 高分辨率、专业摄影级别

### 具体要求
1. **Hero背景**: 现代理发店内部全景，无人物
2. **About图片**: 展示传承历史和团队协作
3. **团队头像**: 专业理发师肖像，友好表情
4. **Gallery作品**: 展示理发技艺和店内环境

## 🚀 快速开始

### 方法一: 使用现有占位符 (推荐)
当前网站已配置高质量SVG占位符，可直接运行：
```bash
npm run dev
```

### 方法二: 生成真实图片
1. 配置 Replicate API Token
2. 运行生成脚本
3. 重启开发服务器

## 📝 注意事项

1. **API 费用**: Replicate API 按使用量计费
2. **生成时间**: 每张图片约需 30-60 秒
3. **质量控制**: 可能需要多次生成以获得最佳效果
4. **文件大小**: 生成的图片会自动优化为网页使用

## 🔍 故障排除

### 常见问题
1. **401 错误**: 检查 API Token 是否正确设置
2. **生成失败**: 检查网络连接和 API 配额
3. **图片质量**: 调整 prompt 参数重新生成

### 联系支持
如遇问题，请查看：
- [Replicate 文档](https://replicate.com/docs)
- [API 参考](https://replicate.com/docs/reference/http)
