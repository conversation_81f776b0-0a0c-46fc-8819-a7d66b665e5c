"use client"

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import { AdminLayout, PageContainer, CardContainer } from '@/components/admin/layout/admin-layout'
import { Badge } from '@/components/admin/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Scissors, 
  DollarSign, 
  Clock,
  Tag,
  Edit,
  Calendar,
  TrendingUp,
  Users,
  BarChart3
} from 'lucide-react'
import { serviceStore, categoryStore, appointmentStore } from '@/lib/admin/storage'
import { Service, ServiceCategory, Appointment } from '@/lib/types/admin'

export default function ServiceDetailPage() {
  const params = useParams()
  const [service, setService] = useState<Service | null>(null)
  const [category, setCategory] = useState<ServiceCategory | null>(null)
  const [appointments, setAppointments] = useState<Appointment[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (params.id) {
      loadServiceData(params.id as string)
    }
  }, [params.id])

  const loadServiceData = (id: string) => {
    setLoading(true)
    try {
      const serviceData = serviceStore.getById(id)
      setService(serviceData || null)

      // 获取分类信息
      if (serviceData && serviceData.category) {
        const categoryData = categoryStore.getById(serviceData.category)
        setCategory(categoryData || null)
      }

      // 获取相关预约记录
      const allAppointments = appointmentStore.getAll()
      const serviceAppointments = allAppointments.filter(apt => 
        apt.services.some(s => s.serviceId === id)
      )
      setAppointments(serviceAppointments)
    } catch (error) {
      console.error('Failed to load service data:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes}分钟`
    }
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`
  }

  const getServiceStats = () => {
    const completedAppointments = appointments.filter(apt => apt.status === 'completed')
    const totalRevenue = completedAppointments.reduce((sum, apt) => {
      const serviceInAppointment = apt.services.find(s => s.serviceId === service?.id)
      return sum + (serviceInAppointment?.price || 0)
    }, 0)

    const thisMonth = new Date()
    thisMonth.setDate(1)
    const thisMonthAppointments = completedAppointments.filter(apt => 
      new Date(apt.date) >= thisMonth
    )

    return {
      totalBookings: appointments.length,
      completedBookings: completedAppointments.length,
      totalRevenue,
      thisMonthBookings: thisMonthAppointments.length,
      avgRating: 4.8 // 模拟评分
    }
  }

  const getRecentBookings = () => {
    return appointments
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, 5)
  }

  if (loading) {
    return (
      <AdminLayout>
        <PageContainer>
          <div className="flex items-center justify-center py-12">
            <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin" />
            <span className="ml-2">加载中...</span>
          </div>
        </PageContainer>
      </AdminLayout>
    )
  }

  if (!service) {
    return (
      <AdminLayout>
        <PageContainer>
          <div className="text-center py-12">
            <p className="text-muted-foreground">服务不存在</p>
          </div>
        </PageContainer>
      </AdminLayout>
    )
  }

  const stats = getServiceStats()
  const recentBookings = getRecentBookings()

  return (
    <AdminLayout>
      <PageContainer
        title={service.name}
        description="服务详细信息和统计数据"
        action={
          <Link href={`/admin/services/${service.id}/edit`}>
            <Button>
              <Edit className="h-4 w-4 mr-2" />
              编辑服务
            </Button>
          </Link>
        }
      >
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 服务基本信息 */}
          <div className="lg:col-span-1">
            <CardContainer title="基本信息">
              <div className="space-y-6">
                {/* 服务图标和状态 */}
                <div className="text-center">
                  <div className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Scissors className="h-10 w-10 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold">{service.name}</h3>
                  <div className="flex justify-center mt-2">
                    <Badge variant={service.isActive ? 'success' : 'destructive'} size="sm">
                      {service.isActive ? '启用中' : '已停用'}
                    </Badge>
                  </div>
                </div>

                {/* 服务详情 */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">分类</span>
                    <Badge variant="outline" size="sm" className="flex items-center">
                      <Tag className="h-3 w-3 mr-1" />
                      {category?.name || '未分类'}
                    </Badge>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">价格</span>
                    <span className="text-lg font-semibold text-green-600">¥{service.price}</span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">时长</span>
                    <div className="flex items-center space-x-1">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{formatDuration(service.duration)}</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">创建时间</span>
                    <span className="text-sm">
                      {new Date(service.createdAt).toLocaleDateString('zh-CN')}
                    </span>
                  </div>
                </div>

                {/* 服务描述 */}
                {service.description && (
                  <div className="pt-4 border-t">
                    <h4 className="font-medium mb-2">服务描述</h4>
                    <p className="text-sm text-muted-foreground">{service.description}</p>
                  </div>
                )}
              </div>
            </CardContainer>

            {/* 统计数据 */}
            <CardContainer title="统计数据" className="mt-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">{stats.totalBookings}</div>
                  <div className="text-sm text-muted-foreground">总预约</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{stats.completedBookings}</div>
                  <div className="text-sm text-muted-foreground">已完成</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">¥{stats.totalRevenue}</div>
                  <div className="text-sm text-muted-foreground">总收入</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{stats.thisMonthBookings}</div>
                  <div className="text-sm text-muted-foreground">本月预约</div>
                </div>
              </div>
            </CardContainer>
          </div>

          {/* 预约记录 */}
          <div className="lg:col-span-2">
            <CardContainer
              title="预约记录"
              action={
                <Link href={`/admin/appointments?serviceId=${service.id}`}>
                  <Button size="sm" variant="outline">
                    查看全部
                  </Button>
                </Link>
              }
            >
              <div className="space-y-4">
                {recentBookings.length > 0 ? (
                  recentBookings.map(appointment => (
                    <div key={appointment.id} className="border border-border rounded-lg p-4 hover:bg-accent/5 transition-colors">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <span className="font-medium">{appointment.customerName}</span>
                            <Badge 
                              variant={
                                appointment.status === 'completed' ? 'success' :
                                appointment.status === 'confirmed' ? 'default' :
                                appointment.status === 'pending' ? 'warning' : 'destructive'
                              } 
                              size="sm"
                            >
                              {appointment.status === 'pending' && '待确认'}
                              {appointment.status === 'confirmed' && '已确认'}
                              {appointment.status === 'in_progress' && '进行中'}
                              {appointment.status === 'completed' && '已完成'}
                              {appointment.status === 'cancelled' && '已取消'}
                              {appointment.status === 'no_show' && '未到店'}
                            </Badge>
                          </div>
                          
                          <div className="space-y-1 text-sm text-muted-foreground">
                            <div>
                              预约时间：{new Date(appointment.date).toLocaleDateString('zh-CN')} {appointment.startTime}
                            </div>
                            <div>
                              理发师：{appointment.staffName}
                            </div>
                            {appointment.notes && (
                              <div>
                                备注：{appointment.notes}
                              </div>
                            )}
                          </div>
                        </div>
                        
                        <div className="text-right">
                          <div className="text-lg font-semibold text-primary">
                            ¥{appointment.services.find(s => s.serviceId === service.id)?.price || service.price}
                          </div>
                          <div className="text-sm text-muted-foreground">{formatDuration(service.duration)}</div>
                        </div>
                      </div>
                      
                      <div className="flex justify-end mt-3">
                        <Link href={`/admin/appointments/${appointment.id}`}>
                          <Button variant="outline" size="sm">
                            查看详情
                          </Button>
                        </Link>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <Calendar className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                    <p className="text-muted-foreground">暂无预约记录</p>
                  </div>
                )}
              </div>
            </CardContainer>

            {/* 性能指标 */}
            <CardContainer title="性能指标" className="mt-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-muted/20 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{stats.avgRating}</div>
                  <div className="text-sm text-muted-foreground">平均评分</div>
                  <div className="flex justify-center mt-1">
                    {'★'.repeat(Math.floor(stats.avgRating))}
                    {'☆'.repeat(5 - Math.floor(stats.avgRating))}
                  </div>
                </div>
                
                <div className="text-center p-4 bg-muted/20 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {stats.completedBookings > 0 ? Math.round((stats.completedBookings / stats.totalBookings) * 100) : 0}%
                  </div>
                  <div className="text-sm text-muted-foreground">完成率</div>
                </div>
                
                <div className="text-center p-4 bg-muted/20 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">
                    ¥{stats.completedBookings > 0 ? Math.round(stats.totalRevenue / stats.completedBookings) : 0}
                  </div>
                  <div className="text-sm text-muted-foreground">平均客单价</div>
                </div>
              </div>
            </CardContainer>
          </div>
        </div>
      </PageContainer>
    </AdminLayout>
  )
}
