"use client"

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import { AdminLayout, PageContainer, CardContainer } from '@/components/admin/layout/admin-layout'
import { Badge } from '@/components/admin/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  User, 
  Phone, 
  Mail, 
  Calendar,
  DollarSign,
  Edit,
  Star,
  Clock,
  MapPin,
  Heart,
  TrendingUp,
  Plus
} from 'lucide-react'
import { customerStore, appointmentStore, serviceStore } from '@/lib/admin/storage'
import { Customer, Appointment } from '@/lib/types/admin'

export default function CustomerDetailPage() {
  const params = useParams()
  const [customer, setCustomer] = useState<Customer | null>(null)
  const [appointments, setAppointments] = useState<Appointment[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (params.id) {
      loadCustomerData(params.id as string)
    }
  }, [params.id])

  const loadCustomerData = (id: string) => {
    setLoading(true)
    try {
      const customerData = customerStore.getById(id)
      setCustomer(customerData || null)

      // 获取客户的预约记录
      const allAppointments = appointmentStore.getAll()
      const customerAppointments = allAppointments
        .filter(apt => apt.customerId === id)
        .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      
      setAppointments(customerAppointments)
    } catch (error) {
      console.error('Failed to load customer data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getCustomerLevel = (totalSpent: number) => {
    if (totalSpent >= 1000) return { level: 'VIP', color: 'bg-yellow-100 text-yellow-800', icon: '👑' }
    if (totalSpent >= 500) return { level: '金牌', color: 'bg-orange-100 text-orange-800', icon: '🥇' }
    if (totalSpent >= 200) return { level: '银牌', color: 'bg-gray-100 text-gray-800', icon: '🥈' }
    return { level: '普通', color: 'bg-blue-100 text-blue-800', icon: '👤' }
  }

  const getStatusBadge = (status: string) => {
    const statusMap = {
      pending: { label: '待确认', variant: 'warning' as const },
      confirmed: { label: '已确认', variant: 'success' as const },
      in_progress: { label: '进行中', variant: 'default' as const },
      completed: { label: '已完成', variant: 'default' as const },
      cancelled: { label: '已取消', variant: 'destructive' as const },
      no_show: { label: '未到店', variant: 'destructive' as const }
    }
    
    const config = statusMap[status as keyof typeof statusMap] || statusMap.pending
    return <Badge variant={config.variant} size="sm">{config.label}</Badge>
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    })
  }

  const getServiceStats = () => {
    const serviceCount: Record<string, number> = {}
    appointments.forEach(apt => {
      apt.services.forEach(service => {
        serviceCount[service.serviceName] = (serviceCount[service.serviceName] || 0) + 1
      })
    })
    
    return Object.entries(serviceCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
  }

  const getMonthlySpending = () => {
    const monthlyData: Record<string, number> = {}
    appointments
      .filter(apt => apt.status === 'completed')
      .forEach(apt => {
        const month = new Date(apt.date).toLocaleDateString('zh-CN', { year: 'numeric', month: 'long' })
        monthlyData[month] = (monthlyData[month] || 0) + apt.totalPrice
      })
    
    return Object.entries(monthlyData)
      .sort(([a], [b]) => new Date(a).getTime() - new Date(b).getTime())
      .slice(-6)
  }

  if (loading) {
    return (
      <AdminLayout>
        <PageContainer>
          <div className="flex items-center justify-center py-12">
            <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin" />
            <span className="ml-2">加载中...</span>
          </div>
        </PageContainer>
      </AdminLayout>
    )
  }

  if (!customer) {
    return (
      <AdminLayout>
        <PageContainer>
          <div className="text-center py-12">
            <p className="text-muted-foreground">客户不存在</p>
          </div>
        </PageContainer>
      </AdminLayout>
    )
  }

  const level = getCustomerLevel(customer.totalSpent)
  const serviceStats = getServiceStats()
  const monthlySpending = getMonthlySpending()

  return (
    <AdminLayout>
      <PageContainer
        title={customer.name}
        description="客户详细信息和消费记录"
        action={
          <div className="flex space-x-2">
            <Link href={`/admin/appointments/new?customerId=${customer.id}`}>
              <Button variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                新建预约
              </Button>
            </Link>
            <Link href={`/admin/customers/${customer.id}/edit`}>
              <Button>
                <Edit className="h-4 w-4 mr-2" />
                编辑信息
              </Button>
            </Link>
          </div>
        }
      >
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 客户基本信息 */}
          <div className="lg:col-span-1">
            <CardContainer title="基本信息">
              <div className="space-y-6">
                {/* 头像和等级 */}
                <div className="text-center">
                  <div className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-3xl">{level.icon}</span>
                  </div>
                  <h3 className="text-xl font-semibold">{customer.name}</h3>
                  <Badge className={level.color} size="sm">
                    {level.level}客户
                  </Badge>
                </div>

                {/* 联系信息 */}
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span>{customer.phone}</span>
                  </div>
                  {customer.email && (
                    <div className="flex items-center space-x-3">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span>{customer.email}</span>
                    </div>
                  )}
                  <div className="flex items-center space-x-3">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span>注册于 {formatDate(customer.createdAt)}</span>
                  </div>
                </div>

                {/* 统计数据 */}
                <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-primary">{customer.totalVisits}</div>
                    <div className="text-sm text-muted-foreground">到店次数</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">¥{customer.totalSpent}</div>
                    <div className="text-sm text-muted-foreground">累计消费</div>
                  </div>
                </div>

                {/* 服务偏好 */}
                <div>
                  <h4 className="font-medium mb-3 flex items-center">
                    <Heart className="h-4 w-4 mr-2 text-red-500" />
                    服务偏好
                  </h4>
                  <div className="space-y-2">
                    {customer.preferences.length > 0 ? (
                      customer.preferences.map((pref, index) => {
                        const service = serviceStore.getById(pref.serviceId)
                        return (
                          <Badge key={index} variant="outline" size="sm">
                            {service?.name || '未知服务'}
                          </Badge>
                        )
                      })
                    ) : (
                      <p className="text-sm text-muted-foreground">暂无偏好记录</p>
                    )}
                  </div>
                </div>
              </div>
            </CardContainer>

            {/* 服务统计 */}
            <CardContainer title="服务偏好统计" className="mt-6">
              <div className="space-y-3">
                {serviceStats.length > 0 ? (
                  serviceStats.map(([service, count]) => (
                    <div key={service} className="flex items-center justify-between">
                      <span className="text-sm">{service}</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-16 h-2 bg-muted rounded-full overflow-hidden">
                          <div 
                            className="h-full bg-primary rounded-full"
                            style={{ width: `${(count / Math.max(...serviceStats.map(([,c]) => c))) * 100}%` }}
                          />
                        </div>
                        <span className="text-sm font-medium">{count}次</span>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-muted-foreground">暂无服务记录</p>
                )}
              </div>
            </CardContainer>
          </div>

          {/* 预约记录 */}
          <div className="lg:col-span-2">
            <CardContainer
              title="预约记录"
              action={
                <Link href={`/admin/appointments/new?customerId=${customer.id}`}>
                  <Button size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    新建预约
                  </Button>
                </Link>
              }
            >
              <div className="space-y-4">
                {appointments.length > 0 ? (
                  appointments.slice(0, 10).map(appointment => (
                    <div key={appointment.id} className="border border-border rounded-lg p-4 hover:bg-accent/5 transition-colors">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <span className="font-medium">{formatDate(appointment.date)}</span>
                            <span className="text-sm text-muted-foreground">{appointment.startTime}</span>
                            {getStatusBadge(appointment.status)}
                          </div>
                          
                          <div className="space-y-1">
                            <div className="text-sm">
                              <span className="text-muted-foreground">服务项目：</span>
                              {appointment.services.map(s => s.serviceName).join(', ')}
                            </div>
                            <div className="text-sm">
                              <span className="text-muted-foreground">理发师：</span>
                              {appointment.staffName}
                            </div>
                            {appointment.notes && (
                              <div className="text-sm">
                                <span className="text-muted-foreground">备注：</span>
                                {appointment.notes}
                              </div>
                            )}
                          </div>
                        </div>
                        
                        <div className="text-right">
                          <div className="text-lg font-semibold text-primary">¥{appointment.totalPrice}</div>
                          <div className="text-sm text-muted-foreground">{appointment.duration}分钟</div>
                        </div>
                      </div>
                      
                      <div className="flex justify-end mt-3">
                        <Link href={`/admin/appointments/${appointment.id}`}>
                          <Button variant="outline" size="sm">
                            查看详情
                          </Button>
                        </Link>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <Calendar className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                    <p className="text-muted-foreground">暂无预约记录</p>
                    <Link href={`/admin/appointments/new?customerId=${customer.id}`}>
                      <Button className="mt-2" size="sm">
                        <Plus className="h-4 w-4 mr-2" />
                        创建首次预约
                      </Button>
                    </Link>
                  </div>
                )}
                
                {appointments.length > 10 && (
                  <div className="text-center pt-4 border-t">
                    <Link href={`/admin/appointments?customerId=${customer.id}`}>
                      <Button variant="outline" size="sm">
                        查看全部 {appointments.length} 条记录
                      </Button>
                    </Link>
                  </div>
                )}
              </div>
            </CardContainer>

            {/* 消费趋势 */}
            {monthlySpending.length > 0 && (
              <CardContainer title="消费趋势" className="mt-6">
                <div className="space-y-3">
                  {monthlySpending.map(([month, amount]) => (
                    <div key={month} className="flex items-center justify-between">
                      <span className="text-sm">{month}</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-24 h-2 bg-muted rounded-full overflow-hidden">
                          <div 
                            className="h-full bg-green-500 rounded-full"
                            style={{ 
                              width: `${(amount / Math.max(...monthlySpending.map(([,a]) => a))) * 100}%` 
                            }}
                          />
                        </div>
                        <span className="text-sm font-medium">¥{amount}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContainer>
            )}
          </div>
        </div>
      </PageContainer>
    </AdminLayout>
  )
}
