/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10 22a2 2 0 0 1-2-2", key: "i7yj1i" }],
  ["path", { d: "M14 2a2 2 0 0 1 2 2", key: "170a0m" }],
  ["path", { d: "M16 22h-2", key: "18d249" }],
  ["path", { d: "M2 10V8", key: "7yj4fe" }],
  ["path", { d: "M2 4a2 2 0 0 1 2-2", key: "ddgnws" }],
  ["path", { d: "M20 8a2 2 0 0 1 2 2", key: "1770vt" }],
  ["path", { d: "M22 14v2", key: "iot8ja" }],
  ["path", { d: "M22 20a2 2 0 0 1-2 2", key: "qj8q6g" }],
  ["path", { d: "M4 16a2 2 0 0 1-2-2", key: "1dnafg" }],
  [
    "path",
    { d: "M8 10a2 2 0 0 1 2-2h5a1 1 0 0 1 1 1v5a2 2 0 0 1-2 2H9a1 1 0 0 1-1-1z", key: "ci6f0b" }
  ],
  ["path", { d: "M8 2h2", key: "1gmkwm" }]
];
const SquaresIntersect = createLucideIcon("squares-intersect", __iconNode);

export { __iconNode, SquaresIntersect as default };
//# sourceMappingURL=squares-intersect.js.map
