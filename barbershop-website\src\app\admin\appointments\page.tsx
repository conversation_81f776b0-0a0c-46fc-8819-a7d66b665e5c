"use client"

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { AdminLayout, PageContainer } from '@/components/admin/layout/admin-layout'
import { DataTable } from '@/components/admin/ui/data-table'
import { Badge } from '@/components/admin/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Plus, 
  Calendar, 
  Clock, 
  User, 
  Phone,
  MoreHorizontal,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  Eye
} from 'lucide-react'
import { appointmentStore } from '@/lib/admin/storage'
import { Appointment, AppointmentStatus } from '@/lib/types/admin'

export default function AppointmentsPage() {
  const [appointments, setAppointments] = useState<Appointment[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([])
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })

  useEffect(() => {
    loadAppointments()
  }, [pagination.current, pagination.pageSize])

  const loadAppointments = () => {
    setLoading(true)
    try {
      const result = appointmentStore.getPaginated({
        page: pagination.current,
        limit: pagination.pageSize,
        sortBy: 'date',
        sortOrder: 'desc'
      })
      
      setAppointments(result.data)
      setPagination(prev => ({
        ...prev,
        total: result.total
      }))
    } catch (error) {
      console.error('Failed to load appointments:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (searchValue: string) => {
    setLoading(true)
    try {
      const result = appointmentStore.getPaginated({
        page: 1,
        limit: pagination.pageSize,
        search: searchValue,
        sortBy: 'date',
        sortOrder: 'desc'
      })
      
      setAppointments(result.data)
      setPagination(prev => ({
        ...prev,
        current: 1,
        total: result.total
      }))
    } catch (error) {
      console.error('Failed to search appointments:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleStatusChange = (appointmentId: string, newStatus: AppointmentStatus) => {
    try {
      appointmentStore.update(appointmentId, { status: newStatus })
      loadAppointments()
    } catch (error) {
      console.error('Failed to update appointment status:', error)
    }
  }

  const handleDelete = (appointmentId: string) => {
    if (confirm('确定要删除这个预约吗？')) {
      try {
        appointmentStore.delete(appointmentId)
        loadAppointments()
      } catch (error) {
        console.error('Failed to delete appointment:', error)
      }
    }
  }

  const getStatusBadge = (status: AppointmentStatus) => {
    const statusConfig = {
      pending: { variant: 'warning' as const, label: '待确认' },
      confirmed: { variant: 'success' as const, label: '已确认' },
      in_progress: { variant: 'default' as const, label: '进行中' },
      completed: { variant: 'default' as const, label: '已完成' },
      cancelled: { variant: 'destructive' as const, label: '已取消' },
      no_show: { variant: 'destructive' as const, label: '未到店' }
    }

    const config = statusConfig[status]
    return <Badge variant={config.variant} size="sm">{config.label}</Badge>
  }

  const formatTime = (date: string, startTime: string) => {
    return `${date} ${startTime}`
  }

  const formatServices = (services: Appointment['services']) => {
    return services.map(service => service.serviceName).join(', ')
  }

  const columns = [
    {
      key: 'customerName',
      title: '客户姓名',
      render: (value: string, record: Appointment) => (
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
            <User className="h-4 w-4 text-primary" />
          </div>
          <div>
            <p className="font-medium">{value}</p>
            <p className="text-xs text-muted-foreground">{record.customerPhone}</p>
          </div>
        </div>
      )
    },
    {
      key: 'date',
      title: '预约时间',
      render: (value: string, record: Appointment) => (
        <div className="flex items-center space-x-2">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          <div>
            <p className="font-medium">{formatTime(value, record.startTime)}</p>
            <p className="text-xs text-muted-foreground">
              时长: {record.duration}分钟
            </p>
          </div>
        </div>
      ),
      sortable: true
    },
    {
      key: 'staffName',
      title: '理发师',
      render: (value: string) => (
        <div className="flex items-center space-x-2">
          <div className="w-6 h-6 bg-accent/20 rounded-full flex items-center justify-center">
            <span className="text-xs font-medium">✂️</span>
          </div>
          <span>{value}</span>
        </div>
      )
    },
    {
      key: 'services',
      title: '服务项目',
      render: (value: Appointment['services']) => (
        <div>
          <p className="font-medium">{formatServices(value)}</p>
          <p className="text-xs text-muted-foreground">
            ¥{value.reduce((sum, service) => sum + service.price, 0)}
          </p>
        </div>
      )
    },
    {
      key: 'status',
      title: '状态',
      render: (value: AppointmentStatus) => getStatusBadge(value)
    }
  ]

  const actionMenuItems = (record: Appointment) => [
    {
      label: '查看详情',
      icon: Eye,
      onClick: () => window.open(`/admin/appointments/${record.id}`, '_blank')
    },
    {
      label: '编辑',
      icon: Edit,
      onClick: () => window.open(`/admin/appointments/${record.id}/edit`, '_blank')
    },
    ...(record.status === 'pending' ? [
      {
        label: '确认预约',
        icon: CheckCircle,
        onClick: () => handleStatusChange(record.id, 'confirmed')
      }
    ] : []),
    ...(record.status !== 'cancelled' && record.status !== 'completed' ? [
      {
        label: '取消预约',
        icon: XCircle,
        onClick: () => handleStatusChange(record.id, 'cancelled')
      }
    ] : []),
    {
      label: '删除',
      icon: Trash2,
      onClick: () => handleDelete(record.id),
      danger: true
    }
  ]

  return (
    <AdminLayout>
      <PageContainer
        title="预约管理"
        description="查看和管理客户预约信息"
        action={
          <div className="flex space-x-2">
            <Link href="/admin/appointments/calendar">
              <Button variant="outline">
                <Calendar className="h-4 w-4 mr-2" />
                日历视图
              </Button>
            </Link>
            <Link href="/admin/appointments/new">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                新建预约
              </Button>
            </Link>
          </div>
        }
      >
        <div className="bg-card border border-border rounded-lg">
          <DataTable
            data={appointments}
            columns={columns}
            loading={loading}
            searchable
            searchPlaceholder="搜索客户姓名、电话或服务..."
            onSearch={handleSearch}
            selectable
            selectedRowKeys={selectedRowKeys}
            onSelectChange={setSelectedRowKeys}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              onChange: (page, pageSize) => {
                setPagination(prev => ({
                  ...prev,
                  current: page,
                  pageSize
                }))
              }
            }}
            actions={{
              title: '操作',
              render: (record: Appointment) => (
                <div className="flex items-center space-x-1">
                  <Link href={`/admin/appointments/${record.id}`}>
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </Link>
                  <Link href={`/admin/appointments/${record.id}/edit`}>
                    <Button variant="ghost" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                  </Link>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => handleDelete(record.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              )
            }}
          />
        </div>

        {/* 批量操作 */}
        {selectedRowKeys.length > 0 && (
          <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-card border border-border rounded-lg shadow-lg p-4">
            <div className="flex items-center space-x-4">
              <span className="text-sm text-muted-foreground">
                已选择 {selectedRowKeys.length} 项
              </span>
              <div className="flex space-x-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => {
                    selectedRowKeys.forEach(id => handleStatusChange(id, 'confirmed'))
                    setSelectedRowKeys([])
                  }}
                >
                  批量确认
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => {
                    selectedRowKeys.forEach(id => handleStatusChange(id, 'cancelled'))
                    setSelectedRowKeys([])
                  }}
                >
                  批量取消
                </Button>
                <Button 
                  variant="destructive" 
                  size="sm"
                  onClick={() => {
                    if (confirm(`确定要删除选中的 ${selectedRowKeys.length} 个预约吗？`)) {
                      appointmentStore.deleteMany(selectedRowKeys)
                      setSelectedRowKeys([])
                      loadAppointments()
                    }
                  }}
                >
                  批量删除
                </Button>
              </div>
            </div>
          </div>
        )}
      </PageContainer>
    </AdminLayout>
  )
}
