"use client"

import { <PERSON><PERSON><PERSON><PERSON>out, PageContainer, CardContainer } from '@/components/admin/layout/admin-layout'
import { AppointmentForm } from '@/components/admin/appointments/appointment-form'
import { CalendarPlus } from 'lucide-react'

export default function NewAppointmentPage() {
  return (
    <AdminLayout>
      <PageContainer
        title="新建预约"
        description="为客户创建新的预约"
      >
        <CardContainer
          title="预约信息"
          description="请填写完整的预约信息"
        >
          <AppointmentForm />
        </CardContainer>
      </PageContainer>
    </AdminLayout>
  )
}
