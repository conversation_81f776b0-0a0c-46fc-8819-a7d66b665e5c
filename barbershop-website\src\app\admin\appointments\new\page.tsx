"use client"

import { Ad<PERSON>Layout, PageContainer, CardContainer } from '@/components/admin/layout/admin-layout'
import { AppointmentForm } from '@/components/admin/appointments/appointment-form'
import { CalendarPlus } from 'lucide-react'

export default function NewAppointmentPage() {
  return (
    <AdminLayout>
      <PageContainer
        title="新建预约"
        description="为客户创建新的预约"
        breadcrumb={[
          { label: '预约管理', href: '/admin/appointments' },
          { label: '新建预约' }
        ]}
      >
        <CardContainer
          title="预约信息"
          description="请填写完整的预约信息"
          icon={CalendarPlus}
        >
          <AppointmentForm />
        </CardContainer>
      </PageContainer>
    </AdminLayout>
  )
}
