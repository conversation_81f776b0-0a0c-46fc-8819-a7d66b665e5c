"use client"

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { AdminLayout, PageContainer } from '@/components/admin/layout/admin-layout'
import { DataTable } from '@/components/admin/ui/data-table'
import { Badge } from '@/components/admin/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Plus, 
  Scissors,
  Clock,
  DollarSign,
  Edit,
  Trash2,
  Eye,
  ToggleLeft,
  ToggleRight,
  Tag
} from 'lucide-react'
import { serviceStore, categoryStore } from '@/lib/admin/storage'
import { Service, ServiceCategory } from '@/lib/types/admin'

export default function ServicesPage() {
  const [services, setServices] = useState<Service[]>([])
  const [categories, setCategories] = useState<ServiceCategory[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([])
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })

  useEffect(() => {
    loadData()
  }, [pagination.current, pagination.pageSize])

  const loadData = () => {
    setLoading(true)
    try {
      const result = serviceStore.getPaginated({
        page: pagination.current,
        limit: pagination.pageSize,
        sortBy: 'price',
        sortOrder: 'desc'
      })
      
      setServices(result.data)
      setPagination(prev => ({
        ...prev,
        total: result.total
      }))

      // 加载分类
      const allCategories = categoryStore.getAll()
      setCategories(allCategories)
    } catch (error) {
      console.error('Failed to load services:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (searchValue: string) => {
    setLoading(true)
    try {
      const result = serviceStore.getPaginated({
        page: 1,
        limit: pagination.pageSize,
        search: searchValue,
        sortBy: 'price',
        sortOrder: 'desc'
      })
      
      setServices(result.data)
      setPagination(prev => ({
        ...prev,
        current: 1,
        total: result.total
      }))
    } catch (error) {
      console.error('Failed to search services:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleToggleStatus = (serviceId: string, currentStatus: boolean) => {
    try {
      serviceStore.update(serviceId, { isActive: !currentStatus })
      loadData()
    } catch (error) {
      console.error('Failed to toggle service status:', error)
    }
  }

  const handleDelete = (serviceId: string) => {
    if (confirm('确定要删除这个服务项目吗？删除后相关预约记录可能受到影响。')) {
      try {
        serviceStore.delete(serviceId)
        loadData()
      } catch (error) {
        console.error('Failed to delete service:', error)
      }
    }
  }

  const getCategoryName = (categoryId: string) => {
    const category = categories.find(c => c.id === categoryId)
    return category?.name || '未分类'
  }

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes}分钟`
    }
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`
  }

  const columns = [
    {
      key: 'name',
      title: '服务信息',
      render: (value: string, record: Service) => (
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
            <Scissors className="h-5 w-5 text-primary" />
          </div>
          <div>
            <div className="flex items-center space-x-2">
              <p className="font-medium">{value}</p>
              {!record.isActive && (
                <Badge variant="destructive" size="sm">
                  已停用
                </Badge>
              )}
            </div>
            <p className="text-sm text-muted-foreground line-clamp-1">
              {record.description || '暂无描述'}
            </p>
          </div>
        </div>
      )
    },
    {
      key: 'categoryId',
      title: '分类',
      render: (value: string) => (
        <Badge variant="outline" size="sm" className="flex items-center w-fit">
          <Tag className="h-3 w-3 mr-1" />
          {getCategoryName(value)}
        </Badge>
      )
    },
    {
      key: 'price',
      title: '价格',
      render: (value: number) => (
        <div className="text-center">
          <p className="text-lg font-semibold text-green-600">¥{value}</p>
        </div>
      ),
      sortable: true
    },
    {
      key: 'duration',
      title: '时长',
      render: (value: number) => (
        <div className="flex items-center space-x-2">
          <Clock className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">{formatDuration(value)}</span>
        </div>
      ),
      sortable: true
    },
    {
      key: 'isActive',
      title: '状态',
      render: (value: boolean, record: Service) => (
        <div className="flex items-center space-x-2">
          <button
            onClick={() => handleToggleStatus(record.id, value)}
            className="flex items-center space-x-1 hover:opacity-75 transition-opacity"
          >
            {value ? (
              <>
                <ToggleRight className="h-5 w-5 text-green-500" />
                <span className="text-sm text-green-600">启用</span>
              </>
            ) : (
              <>
                <ToggleLeft className="h-5 w-5 text-gray-400" />
                <span className="text-sm text-gray-500">停用</span>
              </>
            )}
          </button>
        </div>
      )
    }
  ]

  const activeServices = services.filter(s => s.isActive)
  // 移除未使用的变量
  const avgPrice = services.length > 0 ? Math.round(services.reduce((sum, s) => sum + s.price, 0) / services.length) : 0

  return (
    <AdminLayout>
      <PageContainer
        title="服务管理"
        description="管理理发店的服务项目、价格和时长设置"
        action={
          <div className="flex space-x-2">
            <Link href="/admin/services/categories">
              <Button variant="outline">
                <Tag className="h-4 w-4 mr-2" />
                分类管理
              </Button>
            </Link>
            <Link href="/admin/services/new">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                新增服务
              </Button>
            </Link>
          </div>
        }
      >
        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-card border border-border rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-primary">{services.length}</div>
            <div className="text-sm text-muted-foreground">总服务数</div>
          </div>
          <div className="bg-card border border-border rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{activeServices.length}</div>
            <div className="text-sm text-muted-foreground">启用服务</div>
          </div>
          <div className="bg-card border border-border rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">¥{avgPrice}</div>
            <div className="text-sm text-muted-foreground">平均价格</div>
          </div>
          <div className="bg-card border border-border rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{categories.length}</div>
            <div className="text-sm text-muted-foreground">服务分类</div>
          </div>
        </div>

        <div className="bg-card border border-border rounded-lg">
          <DataTable
            data={services}
            columns={columns}
            loading={loading}
            searchable
            searchPlaceholder="搜索服务名称或描述..."
            onSearch={handleSearch}
            selectable
            selectedRowKeys={selectedRowKeys}
            onSelectChange={setSelectedRowKeys}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              onChange: (page, pageSize) => {
                setPagination(prev => ({
                  ...prev,
                  current: page,
                  pageSize
                }))
              }
            }}
            actions={{
              title: '操作',
              render: (record: Service) => (
                <div className="flex items-center space-x-1">
                  <Link href={`/admin/services/${record.id}`}>
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </Link>
                  <Link href={`/admin/services/${record.id}/edit`}>
                    <Button variant="ghost" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                  </Link>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => handleDelete(record.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              )
            }}
          />
        </div>

        {/* 批量操作 */}
        {selectedRowKeys.length > 0 && (
          <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-card border border-border rounded-lg shadow-lg p-4">
            <div className="flex items-center space-x-4">
              <span className="text-sm text-muted-foreground">
                已选择 {selectedRowKeys.length} 个服务
              </span>
              <div className="flex space-x-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => {
                    selectedRowKeys.forEach(id => {
                      const service = services.find(s => s.id === id)
                      if (service) {
                        serviceStore.update(id, { isActive: true })
                      }
                    })
                    setSelectedRowKeys([])
                    loadData()
                  }}
                >
                  批量启用
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => {
                    selectedRowKeys.forEach(id => {
                      const service = services.find(s => s.id === id)
                      if (service) {
                        serviceStore.update(id, { isActive: false })
                      }
                    })
                    setSelectedRowKeys([])
                    loadData()
                  }}
                >
                  批量停用
                </Button>
                <Button
                  variant="secondary"
                  className="bg-red-600 hover:bg-red-700 text-white"
                  size="sm"
                  onClick={() => {
                    if (confirm(`确定要删除选中的 ${selectedRowKeys.length} 个服务吗？`)) {
                      serviceStore.deleteMany(selectedRowKeys)
                      setSelectedRowKeys([])
                      loadData()
                    }
                  }}
                >
                  批量删除
                </Button>
              </div>
            </div>
          </div>
        )}
      </PageContainer>
    </AdminLayout>
  )
}
