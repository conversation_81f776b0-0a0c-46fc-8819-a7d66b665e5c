"use client"

import { Metadata } from "next"
import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"

interface BookingData {
  service: string
  barber: string
  date: string
  time: string
  duration: number
  price: number
  customerName: string
  customerPhone: string
  customerEmail: string
  notes: string
}

interface BookingErrors {
  service?: string
  barber?: string
  date?: string
  time?: string
  customerName?: string
  customerPhone?: string
  customerEmail?: string
}

const services = [
  { id: "classic-cut", name: "经典理发", duration: 30, price: 88, description: "传统理发技艺，简洁大方" },
  { id: "modern-cut", name: "时尚造型", duration: 45, price: 128, description: "现代流行发型设计" },
  { id: "beard-trim", name: "胡须修剪", duration: 20, price: 58, description: "专业胡须造型修剪" },
  { id: "beard-style", name: "胡须造型", duration: 35, price: 98, description: "根据脸型设计胡须" },
  { id: "hair-wash", name: "洗发护理", duration: 25, price: 48, description: "深层清洁头皮护理" },
  { id: "styling", name: "造型设计", duration: 40, price: 108, description: "特殊场合造型设计" },
  { id: "facial", name: "面部护理", duration: 50, price: 168, description: "男士专业面部护理" },
  { id: "combo-basic", name: "基础套餐", duration: 60, price: 158, description: "理发 + 洗发 + 造型" },
  { id: "combo-premium", name: "豪华套餐", duration: 90, price: 268, description: "理发 + 胡须 + 洗发 + 面部护理" }
]

const barbers = [
  { id: "zhang", name: "张师傅", specialty: "经典理发", experience: "15年经验", avatar: "👨‍🦲" },
  { id: "li", name: "李师傅", specialty: "时尚造型", experience: "12年经验", avatar: "👨‍🦱" },
  { id: "wang", name: "王师傅", specialty: "胡须造型", experience: "10年经验", avatar: "🧔‍♂️" },
  { id: "chen", name: "陈师傅", specialty: "面部护理", experience: "8年经验", avatar: "👨‍💼" }
]

const timeSlots = [
  "09:00", "09:30", "10:00", "10:30", "11:00", "11:30",
  "12:00", "12:30", "13:00", "13:30", "14:00", "14:30",
  "15:00", "15:30", "16:00", "16:30", "17:00", "17:30",
  "18:00", "18:30", "19:00", "19:30", "20:00"
]

export default function BookingPage() {
  const [currentStep, setCurrentStep] = useState(1)
  const [bookingData, setBookingData] = useState<BookingData>({
    service: "",
    barber: "",
    date: "",
    time: "",
    duration: 0,
    price: 0,
    customerName: "",
    customerPhone: "",
    customerEmail: "",
    notes: ""
  })
  
  const [errors, setErrors] = useState<BookingErrors>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [bookingConfirmed, setBookingConfirmed] = useState(false)

  const validateStep = (step: number): boolean => {
    const newErrors: BookingErrors = {}

    if (step === 1) {
      if (!bookingData.service) newErrors.service = "请选择服务项目"
      if (!bookingData.barber) newErrors.barber = "请选择理发师"
    }

    if (step === 2) {
      if (!bookingData.date) newErrors.date = "请选择预约日期"
      if (!bookingData.time) newErrors.time = "请选择预约时间"
    }

    if (step === 3) {
      if (!bookingData.customerName.trim()) newErrors.customerName = "请输入您的姓名"
      if (!bookingData.customerPhone.trim()) {
        newErrors.customerPhone = "请输入联系电话"
      } else if (!/^1[3-9]\d{9}$/.test(bookingData.customerPhone.replace(/\s|-/g, ""))) {
        newErrors.customerPhone = "请输入有效的手机号码"
      }
      if (!bookingData.customerEmail.trim()) {
        newErrors.customerEmail = "请输入邮箱地址"
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(bookingData.customerEmail)) {
        newErrors.customerEmail = "请输入有效的邮箱地址"
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleServiceSelect = (serviceId: string) => {
    const service = services.find(s => s.id === serviceId)
    if (service) {
      setBookingData(prev => ({
        ...prev,
        service: serviceId,
        duration: service.duration,
        price: service.price
      }))
      if (errors.service) {
        setErrors(prev => ({ ...prev, service: undefined }))
      }
    }
  }

  const handleBarberSelect = (barberId: string) => {
    setBookingData(prev => ({ ...prev, barber: barberId }))
    if (errors.barber) {
      setErrors(prev => ({ ...prev, barber: undefined }))
    }
  }

  const handleInputChange = (field: keyof BookingData, value: string) => {
    setBookingData(prev => ({ ...prev, [field]: value }))
    if (errors[field as keyof BookingErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => prev + 1)
    }
  }

  const prevStep = () => {
    setCurrentStep(prev => prev - 1)
  }

  const handleSubmit = async () => {
    if (!validateStep(3)) return

    setIsSubmitting(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // In a real application, you would send the booking data to your backend
      console.log("Booking submitted:", bookingData)
      
      setBookingConfirmed(true)
      setCurrentStep(4)
    } catch (error) {
      console.error("Booking failed:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const getMinDate = () => {
    const today = new Date()
    return today.toISOString().split('T')[0]
  }

  const getMaxDate = () => {
    const maxDate = new Date()
    maxDate.setDate(maxDate.getDate() + 30) // Allow booking up to 30 days in advance
    return maxDate.toISOString().split('T')[0]
  }

  const selectedService = services.find(s => s.id === bookingData.service)
  const selectedBarber = barbers.find(b => b.id === bookingData.barber)

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-primary to-primary/80 text-primary-foreground">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-3xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              在线预约
            </h1>
            <p className="text-xl mb-8 text-primary-foreground/90">
              选择您喜欢的服务和理发师，预约您的专属时间。我们将为您提供最专业的理发体验。
            </p>
            <div className="flex flex-wrap justify-center gap-6 text-sm">
              <div className="flex items-center space-x-2">
                <span className="text-accent text-lg">📅</span>
                <span>灵活预约</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-accent text-lg">✂️</span>
                <span>专业服务</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-accent text-lg">⭐</span>
                <span>品质保证</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Progress Steps */}
      <section className="py-8 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="flex justify-center">
            <div className="flex items-center space-x-4">
              {[1, 2, 3, 4].map((step) => (
                <div key={step} className="flex items-center">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-semibold ${
                    step <= currentStep 
                      ? "bg-primary text-primary-foreground" 
                      : "bg-muted text-muted-foreground"
                  }`}>
                    {step < currentStep ? "✓" : step}
                  </div>
                  {step < 4 && (
                    <div className={`w-12 h-1 mx-2 ${
                      step < currentStep ? "bg-primary" : "bg-muted"
                    }`} />
                  )}
                </div>
              ))}
            </div>
          </div>
          <div className="flex justify-center mt-4">
            <div className="flex space-x-8 text-sm text-muted-foreground">
              <span className={currentStep >= 1 ? "text-primary font-medium" : ""}>选择服务</span>
              <span className={currentStep >= 2 ? "text-primary font-medium" : ""}>选择时间</span>
              <span className={currentStep >= 3 ? "text-primary font-medium" : ""}>填写信息</span>
              <span className={currentStep >= 4 ? "text-primary font-medium" : ""}>预约确认</span>
            </div>
          </div>
        </div>
      </section>

      {/* Booking Form */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {/* Step 1: Service Selection */}
            {currentStep === 1 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl flex items-center space-x-2">
                    <span>✂️</span>
                    <span>选择服务和理发师</span>
                  </CardTitle>
                  <p className="text-muted-foreground">
                    请选择您需要的服务项目和喜欢的理发师
                  </p>
                </CardHeader>
                <CardContent className="space-y-8">
                  {/* Service Selection */}
                  <div>
                    <h3 className="text-lg font-semibold mb-4">服务项目</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {services.map((service) => (
                        <div
                          key={service.id}
                          onClick={() => handleServiceSelect(service.id)}
                          className={`p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md ${
                            bookingData.service === service.id
                              ? "border-primary bg-primary/5"
                              : "border-input hover:border-primary/50"
                          }`}
                        >
                          <h4 className="font-semibold mb-1">{service.name}</h4>
                          <p className="text-sm text-muted-foreground mb-2">{service.description}</p>
                          <div className="flex justify-between items-center text-sm">
                            <span className="text-muted-foreground">{service.duration}分钟</span>
                            <span className="font-semibold text-primary">¥{service.price}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                    {errors.service && (
                      <p className="text-red-500 text-sm mt-2">{errors.service}</p>
                    )}
                  </div>

                  {/* Barber Selection */}
                  <div>
                    <h3 className="text-lg font-semibold mb-4">选择理发师</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      {barbers.map((barber) => (
                        <div
                          key={barber.id}
                          onClick={() => handleBarberSelect(barber.id)}
                          className={`p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md text-center ${
                            bookingData.barber === barber.id
                              ? "border-primary bg-primary/5"
                              : "border-input hover:border-primary/50"
                          }`}
                        >
                          <div className="text-4xl mb-2">{barber.avatar}</div>
                          <h4 className="font-semibold mb-1">{barber.name}</h4>
                          <p className="text-sm text-muted-foreground mb-1">{barber.specialty}</p>
                          <p className="text-xs text-muted-foreground">{barber.experience}</p>
                        </div>
                      ))}
                    </div>
                    {errors.barber && (
                      <p className="text-red-500 text-sm mt-2">{errors.barber}</p>
                    )}
                  </div>

                  <div className="flex justify-end">
                    <Button onClick={nextStep} className="px-8">
                      下一步 →
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Step 2: Date and Time Selection */}
            {currentStep === 2 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl flex items-center space-x-2">
                    <span>📅</span>
                    <span>选择预约时间</span>
                  </CardTitle>
                  <p className="text-muted-foreground">
                    请选择您方便的日期和时间
                  </p>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Selected Service Summary */}
                  {selectedService && selectedBarber && (
                    <div className="bg-muted/50 p-4 rounded-lg">
                      <h4 className="font-semibold mb-2">已选择的服务</h4>
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="font-medium">{selectedService.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {selectedBarber.name} • {selectedService.duration}分钟
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-primary">¥{selectedService.price}</p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Date Selection */}
                  <div>
                    <label htmlFor="date" className="block text-sm font-medium mb-2">
                      预约日期 *
                    </label>
                    <input
                      type="date"
                      id="date"
                      value={bookingData.date}
                      onChange={(e) => handleInputChange("date", e.target.value)}
                      min={getMinDate()}
                      max={getMaxDate()}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary ${
                        errors.date ? "border-red-500" : "border-input"
                      }`}
                    />
                    {errors.date && (
                      <p className="text-red-500 text-sm mt-1">{errors.date}</p>
                    )}
                  </div>

                  {/* Time Selection */}
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      预约时间 *
                    </label>
                    <div className="grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-2">
                      {timeSlots.map((time) => (
                        <button
                          key={time}
                          type="button"
                          onClick={() => handleInputChange("time", time)}
                          className={`p-2 text-sm border rounded-md transition-all hover:shadow-sm ${
                            bookingData.time === time
                              ? "border-primary bg-primary text-primary-foreground"
                              : "border-input hover:border-primary/50"
                          }`}
                        >
                          {time}
                        </button>
                      ))}
                    </div>
                    {errors.time && (
                      <p className="text-red-500 text-sm mt-1">{errors.time}</p>
                    )}
                  </div>

                  <div className="flex justify-between">
                    <Button variant="outline" onClick={prevStep}>
                      ← 上一步
                    </Button>
                    <Button onClick={nextStep} className="px-8">
                      下一步 →
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Step 3: Customer Information */}
            {currentStep === 3 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl flex items-center space-x-2">
                    <span>👤</span>
                    <span>填写个人信息</span>
                  </CardTitle>
                  <p className="text-muted-foreground">
                    请填写您的联系信息，我们会在预约前与您确认
                  </p>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Booking Summary */}
                  <div className="bg-muted/50 p-4 rounded-lg">
                    <h4 className="font-semibold mb-3">预约详情</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>服务项目:</span>
                        <span className="font-medium">{selectedService?.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>理发师:</span>
                        <span className="font-medium">{selectedBarber?.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>预约时间:</span>
                        <span className="font-medium">{bookingData.date} {bookingData.time}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>服务时长:</span>
                        <span className="font-medium">{bookingData.duration}分钟</span>
                      </div>
                      <div className="flex justify-between border-t pt-2 mt-2">
                        <span className="font-semibold">总价:</span>
                        <span className="font-semibold text-primary">¥{bookingData.price}</span>
                      </div>
                    </div>
                  </div>

                  {/* Customer Information Form */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="customerName" className="block text-sm font-medium mb-2">
                        姓名 *
                      </label>
                      <input
                        type="text"
                        id="customerName"
                        value={bookingData.customerName}
                        onChange={(e) => handleInputChange("customerName", e.target.value)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary ${
                          errors.customerName ? "border-red-500" : "border-input"
                        }`}
                        placeholder="请输入您的姓名"
                      />
                      {errors.customerName && (
                        <p className="text-red-500 text-sm mt-1">{errors.customerName}</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="customerPhone" className="block text-sm font-medium mb-2">
                        联系电话 *
                      </label>
                      <input
                        type="tel"
                        id="customerPhone"
                        value={bookingData.customerPhone}
                        onChange={(e) => handleInputChange("customerPhone", e.target.value)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary ${
                          errors.customerPhone ? "border-red-500" : "border-input"
                        }`}
                        placeholder="请输入您的手机号码"
                      />
                      {errors.customerPhone && (
                        <p className="text-red-500 text-sm mt-1">{errors.customerPhone}</p>
                      )}
                    </div>
                  </div>

                  <div>
                    <label htmlFor="customerEmail" className="block text-sm font-medium mb-2">
                      邮箱地址 *
                    </label>
                    <input
                      type="email"
                      id="customerEmail"
                      value={bookingData.customerEmail}
                      onChange={(e) => handleInputChange("customerEmail", e.target.value)}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary ${
                        errors.customerEmail ? "border-red-500" : "border-input"
                      }`}
                      placeholder="请输入您的邮箱地址"
                    />
                    {errors.customerEmail && (
                      <p className="text-red-500 text-sm mt-1">{errors.customerEmail}</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="notes" className="block text-sm font-medium mb-2">
                      备注信息 (可选)
                    </label>
                    <textarea
                      id="notes"
                      rows={3}
                      value={bookingData.notes}
                      onChange={(e) => handleInputChange("notes", e.target.value)}
                      className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary border-input resize-none"
                      placeholder="如有特殊要求或备注，请在此填写..."
                    />
                  </div>

                  <div className="flex justify-between">
                    <Button variant="outline" onClick={prevStep}>
                      ← 上一步
                    </Button>
                    <Button 
                      onClick={handleSubmit} 
                      disabled={isSubmitting}
                      className="px-8"
                    >
                      {isSubmitting ? (
                        <span className="flex items-center space-x-2">
                          <span>⏳</span>
                          <span>提交中...</span>
                        </span>
                      ) : (
                        <span className="flex items-center space-x-2">
                          <span>✅</span>
                          <span>确认预约</span>
                        </span>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Step 4: Confirmation */}
            {currentStep === 4 && bookingConfirmed && (
              <Card>
                <CardHeader className="text-center">
                  <div className="text-6xl mb-4">🎉</div>
                  <CardTitle className="text-2xl text-green-600">
                    预约成功！
                  </CardTitle>
                  <p className="text-muted-foreground">
                    您的预约已成功提交，我们会在24小时内与您联系确认
                  </p>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Booking Details */}
                  <div className="bg-green-50 border border-green-200 p-6 rounded-lg">
                    <h4 className="font-semibold mb-4 text-green-800">预约详情</h4>
                    <div className="space-y-3 text-sm">
                      <div className="flex justify-between">
                        <span className="text-green-700">预约编号:</span>
                        <span className="font-medium">BK{Date.now().toString().slice(-6)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-green-700">客户姓名:</span>
                        <span className="font-medium">{bookingData.customerName}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-green-700">联系电话:</span>
                        <span className="font-medium">{bookingData.customerPhone}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-green-700">服务项目:</span>
                        <span className="font-medium">{selectedService?.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-green-700">理发师:</span>
                        <span className="font-medium">{selectedBarber?.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-green-700">预约时间:</span>
                        <span className="font-medium">{bookingData.date} {bookingData.time}</span>
                      </div>
                      <div className="flex justify-between border-t border-green-200 pt-3 mt-3">
                        <span className="font-semibold text-green-800">总价:</span>
                        <span className="font-semibold text-green-800">¥{bookingData.price}</span>
                      </div>
                    </div>
                  </div>

                  {/* Next Steps */}
                  <div className="space-y-4">
                    <h4 className="font-semibold">接下来的步骤:</h4>
                    <div className="space-y-3 text-sm">
                      <div className="flex items-start space-x-3">
                        <span className="text-primary">1.</span>
                        <span>我们会在24小时内通过电话与您联系确认预约</span>
                      </div>
                      <div className="flex items-start space-x-3">
                        <span className="text-primary">2.</span>
                        <span>请在预约时间前10分钟到店</span>
                      </div>
                      <div className="flex items-start space-x-3">
                        <span className="text-primary">3.</span>
                        <span>如需取消或修改预约，请提前24小时联系我们</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Button asChild className="px-8">
                      <a href="/">返回首页</a>
                    </Button>
                    <Button asChild variant="outline" className="px-8">
                      <a href="/contact">联系我们</a>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </section>
    </div>
  )
}
