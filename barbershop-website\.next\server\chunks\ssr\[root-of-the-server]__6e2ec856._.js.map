{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/components/ui/button.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: \"default\" | \"outline\" | \"secondary\" | \"ghost\" | \"link\" | \"gradient\" | \"shine\"\n  size?: \"default\" | \"sm\" | \"lg\" | \"xl\" | \"icon\"\n  asChild?: boolean\n  loading?: boolean\n  icon?: React.ReactNode\n  rightIcon?: React.ReactNode\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className = \"\", variant = \"default\", size = \"default\", asChild = false, loading = false, icon, rightIcon, children, disabled, ...props }, ref) => {\n    const baseClasses = \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden group\"\n\n    const variantClasses = {\n      default: \"bg-primary text-primary-foreground hover:bg-primary/90 hover:shadow-lg hover:scale-105 active:scale-95\",\n      outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-primary/50 hover:shadow-md hover:scale-105 active:scale-95\",\n      secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:shadow-md hover:scale-105 active:scale-95\",\n      ghost: \"hover:bg-accent hover:text-accent-foreground hover:scale-105 active:scale-95\",\n      link: \"text-primary underline-offset-4 hover:underline hover:scale-105 active:scale-95\",\n      gradient: \"bg-gradient-to-r from-primary to-accent text-primary-foreground hover:from-primary/90 hover:to-accent/90 hover:shadow-lg hover:scale-105 active:scale-95\",\n      shine: \"bg-primary text-primary-foreground hover:shadow-lg hover:scale-105 active:scale-95 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-700\",\n    }\n\n    const sizeClasses = {\n      default: \"h-10 px-4 py-2\",\n      sm: \"h-9 rounded-md px-3\",\n      lg: \"h-11 rounded-md px-8\",\n      xl: \"h-12 rounded-lg px-10 text-base\",\n      icon: \"h-10 w-10\",\n    }\n\n    const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`.trim()\n\n    if (asChild && React.isValidElement(children)) {\n      return React.cloneElement(children, {\n        className: classes,\n        ref,\n        ...props,\n      })\n    }\n\n    return (\n      <button\n        className={classes}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading ? (\n          <>\n            <div className=\"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2\" />\n            加载中...\n          </>\n        ) : (\n          <>\n            {icon && <span className=\"mr-2 transition-transform group-hover:scale-110\">{icon}</span>}\n            <span className=\"transition-transform group-hover:translate-x-0.5\">{children}</span>\n            {rightIcon && <span className=\"ml-2 transition-transform group-hover:scale-110 group-hover:translate-x-0.5\">{rightIcon}</span>}\n          </>\n        )}\n\n        {/* Ripple effect */}\n        <span className=\"absolute inset-0 overflow-hidden rounded-md\">\n          <span className=\"absolute inset-0 bg-white/20 scale-0 group-active:scale-100 transition-transform duration-300 rounded-full\" />\n        </span>\n      </button>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAaA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,YAAY,EAAE,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,UAAU,KAAK,EAAE,UAAU,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC3I,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,SAAS;QACT,WAAW;QACX,OAAO;QACP,MAAM;QACN,UAAU;QACV,OAAO;IACT;IAEA,MAAM,cAAc;QAClB,SAAS;QACT,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,MAAM,UAAU,GAAG,YAAY,CAAC,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC,IAAI;IAElG,IAAI,yBAAW,CAAA,GAAA,qMAAA,CAAA,iBAAoB,AAAD,EAAE,WAAW;QAC7C,qBAAO,CAAA,GAAA,qMAAA,CAAA,eAAkB,AAAD,EAAE,UAAU;YAClC,WAAW;YACX;YACA,GAAG,KAAK;QACV;IACF;IAEA,qBACE,8OAAC;QACC,WAAW;QACX,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,wBACC;;kCACE,8OAAC;wBAAI,WAAU;;;;;;oBAAwF;;6CAIzG;;oBACG,sBAAQ,8OAAC;wBAAK,WAAU;kCAAmD;;;;;;kCAC5E,8OAAC;wBAAK,WAAU;kCAAoD;;;;;;oBACnE,2BAAa,8OAAC;wBAAK,WAAU;kCAA+E;;;;;;;;0BAKjH,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAK,WAAU;;;;;;;;;;;;;;;;;AAIxB;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 139, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/components/ui/card.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  hover?: boolean\n  interactive?: boolean\n  gradient?: boolean\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, hover = false, interactive = false, gradient = false, ...props }, ref) => {\n    const baseClasses = \"rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-300\"\n    const hoverClasses = hover ? \"hover:shadow-lg hover:scale-105 hover:-translate-y-1\" : \"\"\n    const interactiveClasses = interactive ? \"cursor-pointer hover:shadow-xl hover:scale-105 hover:-translate-y-2 active:scale-95 group\" : \"\"\n    const gradientClasses = gradient ? \"bg-gradient-to-br from-card to-card/80 border-primary/20\" : \"\"\n\n    return (\n      <div\n        ref={ref}\n        className={cn(baseClasses, hoverClasses, interactiveClasses, gradientClasses, className)}\n        {...props}\n      />\n    )\n  }\n)\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AAEA;AAJA;;;;AAYA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,QAAQ,KAAK,EAAE,cAAc,KAAK,EAAE,WAAW,KAAK,EAAE,GAAG,OAAO,EAAE;IAC9E,MAAM,cAAc;IACpB,MAAM,eAAe,QAAQ,yDAAyD;IACtF,MAAM,qBAAqB,cAAc,8FAA8F;IACvI,MAAM,kBAAkB,WAAW,6DAA6D;IAEhG,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa,cAAc,oBAAoB,iBAAiB;QAC7E,GAAG,KAAK;;;;;;AAGf;AAEF,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/components/animations/fade-in.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect, useRef, useState } from \"react\"\n\ninterface FadeInProps {\n  children: React.ReactNode\n  delay?: number\n  duration?: number\n  direction?: \"up\" | \"down\" | \"left\" | \"right\" | \"none\"\n  distance?: number\n  className?: string\n  threshold?: number\n}\n\nexport function FadeIn({\n  children,\n  delay = 0,\n  duration = 600,\n  direction = \"up\",\n  distance = 30,\n  className = \"\",\n  threshold = 0.1\n}: FadeInProps) {\n  const [isVisible, setIsVisible] = useState(false)\n  const elementRef = useRef<HTMLDivElement>(null)\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setTimeout(() => {\n            setIsVisible(true)\n          }, delay)\n        }\n      },\n      {\n        threshold,\n        rootMargin: \"0px 0px -50px 0px\"\n      }\n    )\n\n    const currentElement = elementRef.current\n    if (currentElement) {\n      observer.observe(currentElement)\n    }\n\n    return () => {\n      if (currentElement) {\n        observer.unobserve(currentElement)\n      }\n    }\n  }, [delay, threshold])\n\n  const getTransform = () => {\n    if (isVisible) return \"translate3d(0, 0, 0)\"\n    \n    switch (direction) {\n      case \"up\":\n        return `translate3d(0, ${distance}px, 0)`\n      case \"down\":\n        return `translate3d(0, -${distance}px, 0)`\n      case \"left\":\n        return `translate3d(${distance}px, 0, 0)`\n      case \"right\":\n        return `translate3d(-${distance}px, 0, 0)`\n      default:\n        return \"translate3d(0, 0, 0)\"\n    }\n  }\n\n  return (\n    <div\n      ref={elementRef}\n      className={className}\n      style={{\n        opacity: isVisible ? 1 : 0,\n        transform: getTransform(),\n        transition: `opacity ${duration}ms ease-out, transform ${duration}ms ease-out`,\n        willChange: \"opacity, transform\"\n      }}\n    >\n      {children}\n    </div>\n  )\n}\n\ninterface StaggeredFadeInProps {\n  children: React.ReactNode[]\n  delay?: number\n  staggerDelay?: number\n  duration?: number\n  direction?: \"up\" | \"down\" | \"left\" | \"right\" | \"none\"\n  distance?: number\n  className?: string\n}\n\nexport function StaggeredFadeIn({\n  children,\n  delay = 0,\n  staggerDelay = 100,\n  duration = 600,\n  direction = \"up\",\n  distance = 30,\n  className = \"\"\n}: StaggeredFadeInProps) {\n  return (\n    <>\n      {children.map((child, index) => (\n        <FadeIn\n          key={index}\n          delay={delay + index * staggerDelay}\n          duration={duration}\n          direction={direction}\n          distance={distance}\n          className={className}\n        >\n          {child}\n        </FadeIn>\n      ))}\n    </>\n  )\n}\n\ninterface ScaleInProps {\n  children: React.ReactNode\n  delay?: number\n  duration?: number\n  scale?: number\n  className?: string\n  threshold?: number\n}\n\nexport function ScaleIn({\n  children,\n  delay = 0,\n  duration = 600,\n  scale = 0.8,\n  className = \"\",\n  threshold = 0.1\n}: ScaleInProps) {\n  const [isVisible, setIsVisible] = useState(false)\n  const elementRef = useRef<HTMLDivElement>(null)\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setTimeout(() => {\n            setIsVisible(true)\n          }, delay)\n        }\n      },\n      {\n        threshold,\n        rootMargin: \"0px 0px -50px 0px\"\n      }\n    )\n\n    const currentElement = elementRef.current\n    if (currentElement) {\n      observer.observe(currentElement)\n    }\n\n    return () => {\n      if (currentElement) {\n        observer.unobserve(currentElement)\n      }\n    }\n  }, [delay, threshold])\n\n  return (\n    <div\n      ref={elementRef}\n      className={className}\n      style={{\n        opacity: isVisible ? 1 : 0,\n        transform: isVisible ? \"scale(1)\" : `scale(${scale})`,\n        transition: `opacity ${duration}ms ease-out, transform ${duration}ms ease-out`,\n        willChange: \"opacity, transform\"\n      }}\n    >\n      {children}\n    </div>\n  )\n}\n\ninterface SlideInProps {\n  children: React.ReactNode\n  delay?: number\n  duration?: number\n  direction?: \"left\" | \"right\"\n  distance?: number\n  className?: string\n  threshold?: number\n}\n\nexport function SlideIn({\n  children,\n  delay = 0,\n  duration = 800,\n  direction = \"left\",\n  distance = 100,\n  className = \"\",\n  threshold = 0.1\n}: SlideInProps) {\n  const [isVisible, setIsVisible] = useState(false)\n  const elementRef = useRef<HTMLDivElement>(null)\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setTimeout(() => {\n            setIsVisible(true)\n          }, delay)\n        }\n      },\n      {\n        threshold,\n        rootMargin: \"0px 0px -50px 0px\"\n      }\n    )\n\n    const currentElement = elementRef.current\n    if (currentElement) {\n      observer.observe(currentElement)\n    }\n\n    return () => {\n      if (currentElement) {\n        observer.unobserve(currentElement)\n      }\n    }\n  }, [delay, threshold])\n\n  const getTransform = () => {\n    if (isVisible) return \"translateX(0)\"\n    return direction === \"left\" ? `translateX(-${distance}px)` : `translateX(${distance}px)`\n  }\n\n  return (\n    <div\n      ref={elementRef}\n      className={className}\n      style={{\n        opacity: isVisible ? 1 : 0,\n        transform: getTransform(),\n        transition: `opacity ${duration}ms ease-out, transform ${duration}ms ease-out`,\n        willChange: \"opacity, transform\"\n      }}\n    >\n      {children}\n    </div>\n  )\n}\n\ninterface CountUpProps {\n  end: number\n  start?: number\n  duration?: number\n  delay?: number\n  suffix?: string\n  prefix?: string\n  className?: string\n}\n\nexport function CountUp({\n  end,\n  start = 0,\n  duration = 2000,\n  delay = 0,\n  suffix = \"\",\n  prefix = \"\",\n  className = \"\"\n}: CountUpProps) {\n  const [count, setCount] = useState(start)\n  const [isVisible, setIsVisible] = useState(false)\n  const elementRef = useRef<HTMLDivElement>(null)\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting && !isVisible) {\n          setIsVisible(true)\n          setTimeout(() => {\n            const startTime = Date.now()\n            const startValue = start\n            const endValue = end\n            const totalDuration = duration\n\n            const updateCount = () => {\n              const elapsed = Date.now() - startTime\n              const progress = Math.min(elapsed / totalDuration, 1)\n              \n              // Easing function for smooth animation\n              const easeOutQuart = 1 - Math.pow(1 - progress, 4)\n              const currentValue = Math.round(startValue + (endValue - startValue) * easeOutQuart)\n              \n              setCount(currentValue)\n\n              if (progress < 1) {\n                requestAnimationFrame(updateCount)\n              }\n            }\n\n            requestAnimationFrame(updateCount)\n          }, delay)\n        }\n      },\n      {\n        threshold: 0.5\n      }\n    )\n\n    const currentElement = elementRef.current\n    if (currentElement) {\n      observer.observe(currentElement)\n    }\n\n    return () => {\n      if (currentElement) {\n        observer.unobserve(currentElement)\n      }\n    }\n  }, [start, end, duration, delay, isVisible])\n\n  return (\n    <span ref={elementRef} className={className}>\n      {prefix}{count.toLocaleString()}{suffix}\n    </span>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AAFA;;;AAcO,SAAS,OAAO,EACrB,QAAQ,EACR,QAAQ,CAAC,EACT,WAAW,GAAG,EACd,YAAY,IAAI,EAChB,WAAW,EAAE,EACb,YAAY,EAAE,EACd,YAAY,GAAG,EACH;IACZ,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC,CAAC,MAAM;YACN,IAAI,MAAM,cAAc,EAAE;gBACxB,WAAW;oBACT,aAAa;gBACf,GAAG;YACL;QACF,GACA;YACE;YACA,YAAY;QACd;QAGF,MAAM,iBAAiB,WAAW,OAAO;QACzC,IAAI,gBAAgB;YAClB,SAAS,OAAO,CAAC;QACnB;QAEA,OAAO;YACL,IAAI,gBAAgB;gBAClB,SAAS,SAAS,CAAC;YACrB;QACF;IACF,GAAG;QAAC;QAAO;KAAU;IAErB,MAAM,eAAe;QACnB,IAAI,WAAW,OAAO;QAEtB,OAAQ;YACN,KAAK;gBACH,OAAO,CAAC,eAAe,EAAE,SAAS,MAAM,CAAC;YAC3C,KAAK;gBACH,OAAO,CAAC,gBAAgB,EAAE,SAAS,MAAM,CAAC;YAC5C,KAAK;gBACH,OAAO,CAAC,YAAY,EAAE,SAAS,SAAS,CAAC;YAC3C,KAAK;gBACH,OAAO,CAAC,aAAa,EAAE,SAAS,SAAS,CAAC;YAC5C;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW;QACX,OAAO;YACL,SAAS,YAAY,IAAI;YACzB,WAAW;YACX,YAAY,CAAC,QAAQ,EAAE,SAAS,uBAAuB,EAAE,SAAS,WAAW,CAAC;YAC9E,YAAY;QACd;kBAEC;;;;;;AAGP;AAYO,SAAS,gBAAgB,EAC9B,QAAQ,EACR,QAAQ,CAAC,EACT,eAAe,GAAG,EAClB,WAAW,GAAG,EACd,YAAY,IAAI,EAChB,WAAW,EAAE,EACb,YAAY,EAAE,EACO;IACrB,qBACE;kBACG,SAAS,GAAG,CAAC,CAAC,OAAO,sBACpB,8OAAC;gBAEC,OAAO,QAAQ,QAAQ;gBACvB,UAAU;gBACV,WAAW;gBACX,UAAU;gBACV,WAAW;0BAEV;eAPI;;;;;;AAYf;AAWO,SAAS,QAAQ,EACtB,QAAQ,EACR,QAAQ,CAAC,EACT,WAAW,GAAG,EACd,QAAQ,GAAG,EACX,YAAY,EAAE,EACd,YAAY,GAAG,EACF;IACb,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC,CAAC,MAAM;YACN,IAAI,MAAM,cAAc,EAAE;gBACxB,WAAW;oBACT,aAAa;gBACf,GAAG;YACL;QACF,GACA;YACE;YACA,YAAY;QACd;QAGF,MAAM,iBAAiB,WAAW,OAAO;QACzC,IAAI,gBAAgB;YAClB,SAAS,OAAO,CAAC;QACnB;QAEA,OAAO;YACL,IAAI,gBAAgB;gBAClB,SAAS,SAAS,CAAC;YACrB;QACF;IACF,GAAG;QAAC;QAAO;KAAU;IAErB,qBACE,8OAAC;QACC,KAAK;QACL,WAAW;QACX,OAAO;YACL,SAAS,YAAY,IAAI;YACzB,WAAW,YAAY,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YACrD,YAAY,CAAC,QAAQ,EAAE,SAAS,uBAAuB,EAAE,SAAS,WAAW,CAAC;YAC9E,YAAY;QACd;kBAEC;;;;;;AAGP;AAYO,SAAS,QAAQ,EACtB,QAAQ,EACR,QAAQ,CAAC,EACT,WAAW,GAAG,EACd,YAAY,MAAM,EAClB,WAAW,GAAG,EACd,YAAY,EAAE,EACd,YAAY,GAAG,EACF;IACb,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC,CAAC,MAAM;YACN,IAAI,MAAM,cAAc,EAAE;gBACxB,WAAW;oBACT,aAAa;gBACf,GAAG;YACL;QACF,GACA;YACE;YACA,YAAY;QACd;QAGF,MAAM,iBAAiB,WAAW,OAAO;QACzC,IAAI,gBAAgB;YAClB,SAAS,OAAO,CAAC;QACnB;QAEA,OAAO;YACL,IAAI,gBAAgB;gBAClB,SAAS,SAAS,CAAC;YACrB;QACF;IACF,GAAG;QAAC;QAAO;KAAU;IAErB,MAAM,eAAe;QACnB,IAAI,WAAW,OAAO;QACtB,OAAO,cAAc,SAAS,CAAC,YAAY,EAAE,SAAS,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,SAAS,GAAG,CAAC;IAC1F;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW;QACX,OAAO;YACL,SAAS,YAAY,IAAI;YACzB,WAAW;YACX,YAAY,CAAC,QAAQ,EAAE,SAAS,uBAAuB,EAAE,SAAS,WAAW,CAAC;YAC9E,YAAY;QACd;kBAEC;;;;;;AAGP;AAYO,SAAS,QAAQ,EACtB,GAAG,EACH,QAAQ,CAAC,EACT,WAAW,IAAI,EACf,QAAQ,CAAC,EACT,SAAS,EAAE,EACX,SAAS,EAAE,EACX,YAAY,EAAE,EACD;IACb,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC,CAAC,MAAM;YACN,IAAI,MAAM,cAAc,IAAI,CAAC,WAAW;gBACtC,aAAa;gBACb,WAAW;oBACT,MAAM,YAAY,KAAK,GAAG;oBAC1B,MAAM,aAAa;oBACnB,MAAM,WAAW;oBACjB,MAAM,gBAAgB;oBAEtB,MAAM,cAAc;wBAClB,MAAM,UAAU,KAAK,GAAG,KAAK;wBAC7B,MAAM,WAAW,KAAK,GAAG,CAAC,UAAU,eAAe;wBAEnD,uCAAuC;wBACvC,MAAM,eAAe,IAAI,KAAK,GAAG,CAAC,IAAI,UAAU;wBAChD,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa,CAAC,WAAW,UAAU,IAAI;wBAEvE,SAAS;wBAET,IAAI,WAAW,GAAG;4BAChB,sBAAsB;wBACxB;oBACF;oBAEA,sBAAsB;gBACxB,GAAG;YACL;QACF,GACA;YACE,WAAW;QACb;QAGF,MAAM,iBAAiB,WAAW,OAAO;QACzC,IAAI,gBAAgB;YAClB,SAAS,OAAO,CAAC;QACnB;QAEA,OAAO;YACL,IAAI,gBAAgB;gBAClB,SAAS,SAAS,CAAC;YACrB;QACF;IACF,GAAG;QAAC;QAAO;QAAK;QAAU;QAAO;KAAU;IAE3C,qBACE,8OAAC;QAAK,KAAK;QAAY,WAAW;;YAC/B;YAAQ,MAAM,cAAc;YAAI;;;;;;;AAGvC", "debugId": null}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/components/animations/page-transition.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect, useState } from \"react\"\nimport { usePathname } from \"next/navigation\"\n\ninterface PageTransitionProps {\n  children: React.ReactNode\n}\n\nexport function PageTransition({ children }: PageTransitionProps) {\n  const pathname = usePathname()\n  const [isLoading, setIsLoading] = useState(false)\n  const [displayChildren, setDisplayChildren] = useState(children)\n\n  useEffect(() => {\n    setIsLoading(true)\n    \n    const timer = setTimeout(() => {\n      setDisplayChildren(children)\n      setIsLoading(false)\n    }, 300)\n\n    return () => clearTimeout(timer)\n  }, [pathname, children])\n\n  return (\n    <div className=\"relative\">\n      {/* Loading overlay */}\n      <div\n        className={`fixed inset-0 z-50 bg-background transition-opacity duration-300 ${\n          isLoading ? \"opacity-100\" : \"opacity-0 pointer-events-none\"\n        }`}\n      >\n        <div className=\"flex items-center justify-center h-full\">\n          <div className=\"flex flex-col items-center space-y-4\">\n            {/* Animated logo/spinner */}\n            <div className=\"relative\">\n              <div className=\"w-16 h-16 border-4 border-primary/20 rounded-full\"></div>\n              <div className=\"absolute inset-0 w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin\"></div>\n            </div>\n            <div className=\"text-lg font-medium text-muted-foreground\">\n              ✂️ Classic Cuts\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Page content */}\n      <div\n        className={`transition-opacity duration-500 ${\n          isLoading ? \"opacity-0\" : \"opacity-100\"\n        }`}\n      >\n        {displayChildren}\n      </div>\n    </div>\n  )\n}\n\ninterface SmoothScrollProps {\n  children: React.ReactNode\n}\n\nexport function SmoothScroll({ children }: SmoothScrollProps) {\n  useEffect(() => {\n    // Add smooth scrolling behavior\n    document.documentElement.style.scrollBehavior = \"smooth\"\n    \n    return () => {\n      document.documentElement.style.scrollBehavior = \"auto\"\n    }\n  }, [])\n\n  return <>{children}</>\n}\n\ninterface ParallaxProps {\n  children: React.ReactNode\n  speed?: number\n  className?: string\n}\n\nexport function Parallax({ children, speed = 0.5, className = \"\" }: ParallaxProps) {\n  const [offset, setOffset] = useState(0)\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setOffset(window.pageYOffset * speed)\n    }\n\n    window.addEventListener(\"scroll\", handleScroll, { passive: true })\n    return () => window.removeEventListener(\"scroll\", handleScroll)\n  }, [speed])\n\n  return (\n    <div\n      className={className}\n      style={{\n        transform: `translateY(${offset}px)`,\n        willChange: \"transform\"\n      }}\n    >\n      {children}\n    </div>\n  )\n}\n\ninterface FloatingElementProps {\n  children: React.ReactNode\n  amplitude?: number\n  duration?: number\n  delay?: number\n  className?: string\n}\n\nexport function FloatingElement({\n  children,\n  amplitude = 10,\n  duration = 3000,\n  delay = 0,\n  className = \"\"\n}: FloatingElementProps) {\n  return (\n    <div\n      className={className}\n      style={{\n        animation: `float ${duration}ms ease-in-out infinite`,\n        animationDelay: `${delay}ms`,\n        animationFillMode: \"both\"\n      }}\n    >\n      {children}\n      <style jsx>{`\n        @keyframes float {\n          0%, 100% {\n            transform: translateY(0px);\n          }\n          50% {\n            transform: translateY(-${amplitude}px);\n          }\n        }\n      `}</style>\n    </div>\n  )\n}\n\ninterface PulseProps {\n  children: React.ReactNode\n  scale?: number\n  duration?: number\n  className?: string\n}\n\nexport function Pulse({ children, scale = 1.05, duration = 2000, className = \"\" }: PulseProps) {\n  return (\n    <div\n      className={className}\n      style={{\n        animation: `pulse ${duration}ms ease-in-out infinite`\n      }}\n    >\n      {children}\n      <style jsx>{`\n        @keyframes pulse {\n          0%, 100% {\n            transform: scale(1);\n          }\n          50% {\n            transform: scale(${scale});\n          }\n        }\n      `}</style>\n    </div>\n  )\n}\n\ninterface TypewriterProps {\n  text: string\n  speed?: number\n  delay?: number\n  className?: string\n  onComplete?: () => void\n}\n\nexport function Typewriter({\n  text,\n  speed = 50,\n  delay = 0,\n  className = \"\",\n  onComplete\n}: TypewriterProps) {\n  const [displayText, setDisplayText] = useState(\"\")\n  const [currentIndex, setCurrentIndex] = useState(0)\n  const [isStarted, setIsStarted] = useState(false)\n\n  useEffect(() => {\n    const startTimer = setTimeout(() => {\n      setIsStarted(true)\n    }, delay)\n\n    return () => clearTimeout(startTimer)\n  }, [delay])\n\n  useEffect(() => {\n    if (!isStarted) return\n\n    if (currentIndex < text.length) {\n      const timer = setTimeout(() => {\n        setDisplayText(prev => prev + text[currentIndex])\n        setCurrentIndex(prev => prev + 1)\n      }, speed)\n\n      return () => clearTimeout(timer)\n    } else if (onComplete) {\n      onComplete()\n    }\n  }, [currentIndex, text, speed, isStarted, onComplete])\n\n  return (\n    <span className={className}>\n      {displayText}\n      <span className=\"animate-pulse\">|</span>\n    </span>\n  )\n}\n\ninterface RevealProps {\n  children: React.ReactNode\n  direction?: \"horizontal\" | \"vertical\"\n  duration?: number\n  delay?: number\n  className?: string\n}\n\nexport function Reveal({\n  children,\n  direction = \"horizontal\",\n  duration = 800,\n  delay = 0,\n  className = \"\"\n}: RevealProps) {\n  const [isVisible, setIsVisible] = useState(false)\n\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setIsVisible(true)\n    }, delay)\n\n    return () => clearTimeout(timer)\n  }, [delay])\n\n  return (\n    <div className={`relative overflow-hidden ${className}`}>\n      <div\n        className={`transition-transform duration-${duration} ease-out ${\n          isVisible ? \"translate-x-0 translate-y-0\" : \n          direction === \"horizontal\" ? \"translate-x-full\" : \"translate-y-full\"\n        }`}\n      >\n        {children}\n      </div>\n      <div\n        className={`absolute inset-0 bg-primary transition-transform duration-${duration} ease-out ${\n          isVisible ? \n          (direction === \"horizontal\" ? \"translate-x-full\" : \"translate-y-full\") :\n          \"translate-x-0 translate-y-0\"\n        }`}\n        style={{ transitionDelay: `${delay}ms` }}\n      />\n    </div>\n  )\n}\n\ninterface MagneticProps {\n  children: React.ReactNode\n  strength?: number\n  className?: string\n}\n\nexport function Magnetic({ children, strength = 0.3, className = \"\" }: MagneticProps) {\n  const [position, setPosition] = useState({ x: 0, y: 0 })\n\n  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {\n    const rect = e.currentTarget.getBoundingClientRect()\n    const centerX = rect.left + rect.width / 2\n    const centerY = rect.top + rect.height / 2\n    \n    const deltaX = (e.clientX - centerX) * strength\n    const deltaY = (e.clientY - centerY) * strength\n    \n    setPosition({ x: deltaX, y: deltaY })\n  }\n\n  const handleMouseLeave = () => {\n    setPosition({ x: 0, y: 0 })\n  }\n\n  return (\n    <div\n      className={className}\n      onMouseMove={handleMouseMove}\n      onMouseLeave={handleMouseLeave}\n      style={{\n        transform: `translate(${position.x}px, ${position.y}px)`,\n        transition: \"transform 0.3s ease-out\"\n      }}\n    >\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAEA;AACA;AAHA;;;;;AASO,SAAS,eAAe,EAAE,QAAQ,EAAuB;IAC9D,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;QAEb,MAAM,QAAQ,WAAW;YACvB,mBAAmB;YACnB,aAAa;QACf,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAU;KAAS;IAEvB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,WAAW,CAAC,iEAAiE,EAC3E,YAAY,gBAAgB,iCAC5B;0BAEF,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;0CAEjB,8OAAC;gCAAI,WAAU;0CAA4C;;;;;;;;;;;;;;;;;;;;;;0BAQjE,8OAAC;gBACC,WAAW,CAAC,gCAAgC,EAC1C,YAAY,cAAc,eAC1B;0BAED;;;;;;;;;;;;AAIT;AAMO,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gCAAgC;QAChC,SAAS,eAAe,CAAC,KAAK,CAAC,cAAc,GAAG;QAEhD,OAAO;YACL,SAAS,eAAe,CAAC,KAAK,CAAC,cAAc,GAAG;QAClD;IACF,GAAG,EAAE;IAEL,qBAAO;kBAAG;;AACZ;AAQO,SAAS,SAAS,EAAE,QAAQ,EAAE,QAAQ,GAAG,EAAE,YAAY,EAAE,EAAiB;IAC/E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,UAAU,OAAO,WAAW,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU,cAAc;YAAE,SAAS;QAAK;QAChE,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG;QAAC;KAAM;IAEV,qBACE,8OAAC;QACC,WAAW;QACX,OAAO;YACL,WAAW,CAAC,WAAW,EAAE,OAAO,GAAG,CAAC;YACpC,YAAY;QACd;kBAEC;;;;;;AAGP;AAUO,SAAS,gBAAgB,EAC9B,QAAQ,EACR,YAAY,EAAE,EACd,WAAW,IAAI,EACf,QAAQ,CAAC,EACT,YAAY,EAAE,EACO;IACrB,qBACE,8OAAC;QAEC,OAAO;YACL,WAAW,CAAC,MAAM,EAAE,SAAS,uBAAuB,CAAC;YACrD,gBAAgB,GAAG,MAAM,EAAE,CAAC;YAC5B,mBAAmB;QACrB;;;;;oBAS+B;;;oBAdpB;;YAOV;;;;oBAO8B;;sGAAA;;;;;;;;AAMrC;AASO,SAAS,MAAM,EAAE,QAAQ,EAAE,QAAQ,IAAI,EAAE,WAAW,IAAI,EAAE,YAAY,EAAE,EAAc;IAC3F,qBACE,8OAAC;QAEC,OAAO;YACL,WAAW,CAAC,MAAM,EAAE,SAAS,uBAAuB,CAAC;QACvD;;;;;oBASyB;;;oBAZd;;YAKV;;;;oBAOwB;;2FAAA;;;;;;;;AAM/B;AAUO,SAAS,WAAW,EACzB,IAAI,EACJ,QAAQ,EAAE,EACV,QAAQ,CAAC,EACT,YAAY,EAAE,EACd,UAAU,EACM;IAChB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,WAAW;YAC5B,aAAa;QACf,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAM;IAEV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW;QAEhB,IAAI,eAAe,KAAK,MAAM,EAAE;YAC9B,MAAM,QAAQ,WAAW;gBACvB,eAAe,CAAA,OAAQ,OAAO,IAAI,CAAC,aAAa;gBAChD,gBAAgB,CAAA,OAAQ,OAAO;YACjC,GAAG;YAEH,OAAO,IAAM,aAAa;QAC5B,OAAO,IAAI,YAAY;YACrB;QACF;IACF,GAAG;QAAC;QAAc;QAAM;QAAO;QAAW;KAAW;IAErD,qBACE,8OAAC;QAAK,WAAW;;YACd;0BACD,8OAAC;gBAAK,WAAU;0BAAgB;;;;;;;;;;;;AAGtC;AAUO,SAAS,OAAO,EACrB,QAAQ,EACR,YAAY,YAAY,EACxB,WAAW,GAAG,EACd,QAAQ,CAAC,EACT,YAAY,EAAE,EACF;IACZ,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW;YACvB,aAAa;QACf,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAM;IAEV,qBACE,8OAAC;QAAI,WAAW,CAAC,yBAAyB,EAAE,WAAW;;0BACrD,8OAAC;gBACC,WAAW,CAAC,8BAA8B,EAAE,SAAS,UAAU,EAC7D,YAAY,gCACZ,cAAc,eAAe,qBAAqB,oBAClD;0BAED;;;;;;0BAEH,8OAAC;gBACC,WAAW,CAAC,0DAA0D,EAAE,SAAS,UAAU,EACzF,YACC,cAAc,eAAe,qBAAqB,qBACnD,+BACA;gBACF,OAAO;oBAAE,iBAAiB,GAAG,MAAM,EAAE,CAAC;gBAAC;;;;;;;;;;;;AAI/C;AAQO,SAAS,SAAS,EAAE,QAAQ,EAAE,WAAW,GAAG,EAAE,YAAY,EAAE,EAAiB;IAClF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAEtD,MAAM,kBAAkB,CAAC;QACvB,MAAM,OAAO,EAAE,aAAa,CAAC,qBAAqB;QAClD,MAAM,UAAU,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG;QACzC,MAAM,UAAU,KAAK,GAAG,GAAG,KAAK,MAAM,GAAG;QAEzC,MAAM,SAAS,CAAC,EAAE,OAAO,GAAG,OAAO,IAAI;QACvC,MAAM,SAAS,CAAC,EAAE,OAAO,GAAG,OAAO,IAAI;QAEvC,YAAY;YAAE,GAAG;YAAQ,GAAG;QAAO;IACrC;IAEA,MAAM,mBAAmB;QACvB,YAAY;YAAE,GAAG;YAAG,GAAG;QAAE;IAC3B;IAEA,qBACE,8OAAC;QACC,WAAW;QACX,aAAa;QACb,cAAc;QACd,OAAO;YACL,WAAW,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,GAAG,CAAC;YACxD,YAAY;QACd;kBAEC;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 803, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/components/pages/services-page-content.tsx"], "sourcesContent": ["\"use client\"\n\nimport Image from \"next/image\"\nimport Link from \"next/link\"\nimport { Button } from \"@/components/ui/button\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { FadeIn, StaggeredFadeIn, ScaleIn } from \"@/components/animations/fade-in\"\nimport { FloatingElement } from \"@/components/animations/page-transition\"\n\nconst services = [\n  {\n    id: \"haircuts\",\n    icon: \"✂️\",\n    title: \"经典理发\",\n    description: \"专业理发师为您提供精准的剪发服务，打造适合您的完美发型\",\n    basePrice: \"¥80-120\",\n    duration: \"45分钟\",\n    styles: [\n      { name: \"商务短发\", price: \"¥80\", description: \"简洁专业，适合商务场合\" },\n      { name: \"时尚层次\", price: \"¥90\", description: \"现代层次感，展现个性魅力\" },\n      { name: \"经典油头\", price: \"¥100\", description: \"复古绅士风格，永不过时\" },\n      { name: \"渐变短发\", price: \"¥110\", description: \"精细渐变技术，层次分明\" },\n      { name: \"个性造型\", price: \"¥120\", description: \"根据脸型定制，独一无二\" }\n    ]\n  },\n  {\n    id: \"beard\",\n    icon: \"🧔\",\n    title: \"胡须造型\",\n    description: \"精细的胡须修剪和造型服务，让您的胡须成为魅力加分项\",\n    basePrice: \"¥50-80\",\n    duration: \"30分钟\",\n    styles: [\n      { name: \"胡须修剪\", price: \"¥50\", description: \"基础修剪，保持整洁\" },\n      { name: \"胡须造型\", price: \"¥60\", description: \"根据脸型设计胡须形状\" },\n      { name: \"胡须护理\", price: \"¥70\", description: \"深度清洁和滋养护理\" },\n      { name: \"完整胡须服务\", price: \"¥80\", description: \"修剪+造型+护理全套服务\" }\n    ]\n  },\n  {\n    id: \"styling\",\n    icon: \"💇‍♂️\",\n    title: \"头发造型\",\n    description: \"专业造型师为您打造时尚发型，适合各种场合和个人风格\",\n    basePrice: \"¥60-100\",\n    duration: \"40分钟\",\n    styles: [\n      { name: \"日常造型\", price: \"¥60\", description: \"简单自然，适合日常生活\" },\n      { name: \"商务造型\", price: \"¥70\", description: \"专业正式，商务场合首选\" },\n      { name: \"时尚造型\", price: \"¥80\", description: \"潮流前沿，展现个性\" },\n      { name: \"特殊场合造型\", price: \"¥100\", description: \"婚礼、聚会等特殊场合\" }\n    ]\n  },\n  {\n    id: \"wash\",\n    icon: \"🚿\",\n    title: \"洗发护理\",\n    description: \"深度清洁和专业护理，让您的头发健康有光泽\",\n    basePrice: \"¥40-80\",\n    duration: \"30分钟\",\n    styles: [\n      { name: \"基础洗发\", price: \"¥40\", description: \"温和清洁，去除油脂污垢\" },\n      { name: \"深层清洁\", price: \"¥50\", description: \"深度清洁，去除顽固污垢\" },\n      { name: \"滋养护理\", price: \"¥60\", description: \"营养滋养，修复受损发质\" },\n      { name: \"头皮按摩护理\", price: \"¥80\", description: \"专业按摩+深度护理\" }\n    ]\n  },\n  {\n    id: \"facial\",\n    icon: \"🧴\",\n    title: \"面部护理\",\n    description: \"专业男士面部护理服务，让您容光焕发\",\n    basePrice: \"¥80-150\",\n    duration: \"60分钟\",\n    styles: [\n      { name: \"基础面部清洁\", price: \"¥80\", description: \"深度清洁，去除黑头\" },\n      { name: \"保湿护理\", price: \"¥100\", description: \"补水保湿，改善肌肤状态\" },\n      { name: \"抗衰老护理\", price: \"¥120\", description: \"紧致肌肤，延缓衰老\" },\n      { name: \"VIP全套护理\", price: \"¥150\", description: \"清洁+保湿+按摩+面膜\" }\n    ]\n  }\n]\n\nconst features = [\n  {\n    icon: \"👨‍💼\",\n    title: \"专业理发师\",\n    description: \"15年以上经验的资深理发师团队\"\n  },\n  {\n    icon: \"🏆\",\n    title: \"品质保证\",\n    description: \"100%满意保证，不满意免费重做\"\n  },\n  {\n    icon: \"🕐\",\n    title: \"准时服务\",\n    description: \"严格按预约时间，绝不让您久等\"\n  },\n  {\n    icon: \"💎\",\n    title: \"优质产品\",\n    description: \"使用国际知名品牌专业产品\"\n  }\n]\n\nexport function ServicesPageContent() {\n  return (\n    <div className=\"min-h-screen\">\n      {/* Hero Section */}\n      <section className=\"relative py-20 bg-gradient-to-r from-primary to-primary/80 text-primary-foreground\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <div className=\"max-w-3xl mx-auto\">\n            <FadeIn>\n              <h1 className=\"text-4xl md:text-5xl font-bold mb-6\">\n                专业理发服务\n              </h1>\n            </FadeIn>\n            <FadeIn delay={0.2}>\n              <p className=\"text-xl mb-8 text-primary-foreground/90\">\n                探索我们全方位的专业理发服务，每一项服务都体现我们对品质的追求\n              </p>\n            </FadeIn>\n            <FadeIn delay={0.4}>\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <Button size=\"lg\" variant=\"secondary\" asChild>\n                  <Link href=\"/booking\">立即预约</Link>\n                </Button>\n                <Button size=\"lg\" variant=\"outline\" asChild>\n                  <Link href=\"/gallery\">查看作品</Link>\n                </Button>\n              </div>\n            </FadeIn>\n          </div>\n        </div>\n        <FloatingElement className=\"absolute top-10 right-10 opacity-20\">\n          <span className=\"text-6xl\">✂️</span>\n        </FloatingElement>\n      </section>\n\n      {/* Services Grid */}\n      <section className=\"py-20\">\n        <div className=\"container mx-auto px-4\">\n          <FadeIn>\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">我们的服务</h2>\n              <p className=\"text-xl text-muted-foreground max-w-2xl mx-auto\">\n                从经典理发到现代造型，我们提供全方位的男士美容服务\n              </p>\n            </div>\n          </FadeIn>\n\n          <StaggeredFadeIn className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n            {services.map((service) => (\n              <ScaleIn key={service.id}>\n                <Card className=\"h-full hover:shadow-lg transition-shadow\">\n                  <CardHeader>\n                    <div className=\"flex items-center space-x-4\">\n                      <div className=\"p-3 bg-primary/10 rounded-full\">\n                        <span className=\"text-2xl\">{service.icon}</span>\n                      </div>\n                      <div>\n                        <CardTitle className=\"text-xl\">{service.title}</CardTitle>\n                        <div className=\"flex items-center space-x-4 text-sm text-muted-foreground\">\n                          <span>{service.basePrice}</span>\n                          <span>•</span>\n                          <span>{service.duration}</span>\n                        </div>\n                      </div>\n                    </div>\n                    <CardDescription className=\"text-base\">\n                      {service.description}\n                    </CardDescription>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-3\">\n                      {service.styles.map((style, index) => (\n                        <div key={index} className=\"flex justify-between items-start p-3 bg-muted/50 rounded-lg\">\n                          <div className=\"flex-1\">\n                            <div className=\"font-medium\">{style.name}</div>\n                            <div className=\"text-sm text-muted-foreground\">{style.description}</div>\n                          </div>\n                          <div className=\"font-bold text-primary ml-4\">{style.price}</div>\n                        </div>\n                      ))}\n                    </div>\n                    <div className=\"mt-6\">\n                      <Button className=\"w-full\" asChild>\n                        <Link href=\"/booking\">预约此服务</Link>\n                      </Button>\n                    </div>\n                  </CardContent>\n                </Card>\n              </ScaleIn>\n            ))}\n          </StaggeredFadeIn>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-20 bg-muted/50\">\n        <div className=\"container mx-auto px-4\">\n          <FadeIn>\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">为什么选择我们</h2>\n              <p className=\"text-xl text-muted-foreground max-w-2xl mx-auto\">\n                我们致力于为每位客户提供最优质的服务体验\n              </p>\n            </div>\n          </FadeIn>\n\n          <StaggeredFadeIn className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {features.map((feature, index) => (\n              <ScaleIn key={index}>\n                <Card className=\"text-center h-full\">\n                  <CardContent className=\"pt-6\">\n                    <div className=\"p-4 bg-primary/10 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center\">\n                      <span className=\"text-2xl\">{feature.icon}</span>\n                    </div>\n                    <h3 className=\"text-lg font-semibold mb-2\">{feature.title}</h3>\n                    <p className=\"text-muted-foreground\">{feature.description}</p>\n                  </CardContent>\n                </Card>\n              </ScaleIn>\n            ))}\n          </StaggeredFadeIn>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-20 bg-primary text-primary-foreground\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <FadeIn>\n            <div className=\"max-w-3xl mx-auto\">\n              <h2 className=\"text-3xl md:text-4xl font-bold mb-6\">\n                准备好体验专业服务了吗？\n              </h2>\n              <p className=\"text-xl mb-8 text-primary-foreground/90\">\n                立即预约，让我们的专业团队为您提供最优质的服务\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <Button size=\"lg\" variant=\"secondary\" asChild>\n                  <Link href=\"/booking\">立即预约</Link>\n                </Button>\n                <Button size=\"lg\" variant=\"outline\" asChild>\n                  <Link href=\"/contact\">联系我们</Link>\n                </Button>\n              </div>\n            </div>\n          </FadeIn>\n        </div>\n      </section>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAPA;;;;;;;AASA,MAAM,WAAW;IACf;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,WAAW;QACX,UAAU;QACV,QAAQ;YACN;gBAAE,MAAM;gBAAQ,OAAO;gBAAO,aAAa;YAAc;YACzD;gBAAE,MAAM;gBAAQ,OAAO;gBAAO,aAAa;YAAe;YAC1D;gBAAE,MAAM;gBAAQ,OAAO;gBAAQ,aAAa;YAAc;YAC1D;gBAAE,MAAM;gBAAQ,OAAO;gBAAQ,aAAa;YAAc;YAC1D;gBAAE,MAAM;gBAAQ,OAAO;gBAAQ,aAAa;YAAc;SAC3D;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,WAAW;QACX,UAAU;QACV,QAAQ;YACN;gBAAE,MAAM;gBAAQ,OAAO;gBAAO,aAAa;YAAY;YACvD;gBAAE,MAAM;gBAAQ,OAAO;gBAAO,aAAa;YAAa;YACxD;gBAAE,MAAM;gBAAQ,OAAO;gBAAO,aAAa;YAAY;YACvD;gBAAE,MAAM;gBAAU,OAAO;gBAAO,aAAa;YAAe;SAC7D;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,WAAW;QACX,UAAU;QACV,QAAQ;YACN;gBAAE,MAAM;gBAAQ,OAAO;gBAAO,aAAa;YAAc;YACzD;gBAAE,MAAM;gBAAQ,OAAO;gBAAO,aAAa;YAAc;YACzD;gBAAE,MAAM;gBAAQ,OAAO;gBAAO,aAAa;YAAY;YACvD;gBAAE,MAAM;gBAAU,OAAO;gBAAQ,aAAa;YAAa;SAC5D;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,WAAW;QACX,UAAU;QACV,QAAQ;YACN;gBAAE,MAAM;gBAAQ,OAAO;gBAAO,aAAa;YAAc;YACzD;gBAAE,MAAM;gBAAQ,OAAO;gBAAO,aAAa;YAAc;YACzD;gBAAE,MAAM;gBAAQ,OAAO;gBAAO,aAAa;YAAc;YACzD;gBAAE,MAAM;gBAAU,OAAO;gBAAO,aAAa;YAAY;SAC1D;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,aAAa;QACb,WAAW;QACX,UAAU;QACV,QAAQ;YACN;gBAAE,MAAM;gBAAU,OAAO;gBAAO,aAAa;YAAY;YACzD;gBAAE,MAAM;gBAAQ,OAAO;gBAAQ,aAAa;YAAc;YAC1D;gBAAE,MAAM;gBAAS,OAAO;gBAAQ,aAAa;YAAY;YACzD;gBAAE,MAAM;gBAAW,OAAO;gBAAQ,aAAa;YAAc;SAC9D;IACH;CACD;AAED,MAAM,WAAW;IACf;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8IAAA,CAAA,SAAM;8CACL,cAAA,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;8CAItD,8OAAC,8IAAA,CAAA,SAAM;oCAAC,OAAO;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAA0C;;;;;;;;;;;8CAIzD,8OAAC,8IAAA,CAAA,SAAM;oCAAC,OAAO;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,SAAQ;gDAAY,OAAO;0DAC3C,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAW;;;;;;;;;;;0DAExB,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,SAAQ;gDAAU,OAAO;0DACzC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMhC,8OAAC,sJAAA,CAAA,kBAAe;wBAAC,WAAU;kCACzB,cAAA,8OAAC;4BAAK,WAAU;sCAAW;;;;;;;;;;;;;;;;;0BAK/B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,8IAAA,CAAA,SAAM;sCACL,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,8OAAC;wCAAE,WAAU;kDAAkD;;;;;;;;;;;;;;;;;sCAMnE,8OAAC,8IAAA,CAAA,kBAAe;4BAAC,WAAU;sCACxB,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,8IAAA,CAAA,UAAO;8CACN,cAAA,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAK,WAAU;8EAAY,QAAQ,IAAI;;;;;;;;;;;0EAE1C,8OAAC;;kFACC,8OAAC,gIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAW,QAAQ,KAAK;;;;;;kFAC7C,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;0FAAM,QAAQ,SAAS;;;;;;0FACxB,8OAAC;0FAAK;;;;;;0FACN,8OAAC;0FAAM,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;kEAI7B,8OAAC,gIAAA,CAAA,kBAAe;wDAAC,WAAU;kEACxB,QAAQ,WAAW;;;;;;;;;;;;0DAGxB,8OAAC,gIAAA,CAAA,cAAW;;kEACV,8OAAC;wDAAI,WAAU;kEACZ,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC1B,8OAAC;gEAAgB,WAAU;;kFACzB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;0FAAe,MAAM,IAAI;;;;;;0FACxC,8OAAC;gFAAI,WAAU;0FAAiC,MAAM,WAAW;;;;;;;;;;;;kFAEnE,8OAAC;wEAAI,WAAU;kFAA+B,MAAM,KAAK;;;;;;;+DALjD;;;;;;;;;;kEASd,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DAAC,WAAU;4DAAS,OAAO;sEAChC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAK;0EAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAlClB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;0BA8ChC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,8IAAA,CAAA,SAAM;sCACL,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,8OAAC;wCAAE,WAAU;kDAAkD;;;;;;;;;;;;;;;;;sCAMnE,8OAAC,8IAAA,CAAA,kBAAe;4BAAC,WAAU;sCACxB,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,8IAAA,CAAA,UAAO;8CACN,cAAA,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAY,QAAQ,IAAI;;;;;;;;;;;8DAE1C,8OAAC;oDAAG,WAAU;8DAA8B,QAAQ,KAAK;;;;;;8DACzD,8OAAC;oDAAE,WAAU;8DAAyB,QAAQ,WAAW;;;;;;;;;;;;;;;;;mCAPjD;;;;;;;;;;;;;;;;;;;;;0BAiBtB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,8IAAA,CAAA,SAAM;kCACL,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAGpD,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;8CAGvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,SAAQ;4CAAY,OAAO;sDAC3C,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAW;;;;;;;;;;;sDAExB,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,SAAQ;4CAAU,OAAO;sDACzC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxC", "debugId": null}}]}