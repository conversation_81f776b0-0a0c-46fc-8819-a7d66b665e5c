#!/usr/bin/env node

/**
 * <PERSON>'s Barbershop - 图片生成脚本
 * 使用 Replicate API 生成专业理发店图片
 * 
 * 使用方法:
 * 1. 设置环境变量: export REPLICATE_API_TOKEN="your_token_here"
 * 2. 安装依赖: npm install replicate
 * 3. 运行脚本: node scripts/generate-images.js
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

// 检查是否有 Replicate API Token
const REPLICATE_API_TOKEN = process.env.REPLICATE_API_TOKEN;

if (!REPLICATE_API_TOKEN) {
  console.error('❌ 请设置 REPLICATE_API_TOKEN 环境变量');
  console.log('💡 获取API Token: https://replicate.com/account/api-tokens');
  console.log('💡 设置方法: export REPLICATE_API_TOKEN="r8_..."');
  process.exit(1);
}

// 图片生成配置
const imageConfigs = [
  {
    filename: 'hero-barbershop.jpg',
    size: { width: 1920, height: 1080 },
    prompt: 'Modern Chinese barbershop interior, professional and elegant atmosphere, warm golden lighting, traditional wooden furniture mixed with contemporary design, comfortable leather barber chairs, vintage mirrors with ornate frames, professional barber tools displayed on shelves, warm brown and gold color scheme, cozy and inviting atmosphere, high-end professional barbershop, cinematic lighting, ultra-realistic, 8K resolution',
    description: 'Hero背景 - 现代理发店内部全景'
  },
  {
    filename: 'about-story.jpg',
    size: { width: 800, height: 600 },
    prompt: 'Traditional Chinese barbershop from the past, vintage atmosphere, old-style barber chairs, classic mirrors, historical ambiance, sepia tones, nostalgic feeling, traditional barber tools, warm lighting, storytelling mood, heritage and tradition, professional photography',
    description: 'About页面 - 理发店历史故事'
  },
  {
    filename: 'about-barbershop.jpg',
    size: { width: 800, height: 600 },
    prompt: 'Professional Chinese barbers working in modern barbershop, team collaboration, skilled craftsmen at work, professional atmosphere, modern equipment, warm lighting, teamwork, expertise, high-quality service, professional photography',
    description: 'About页面 - 团队工作环境'
  },
  {
    filename: 'team-member-1.jpg',
    size: { width: 400, height: 400 },
    prompt: 'Professional Chinese male barber portrait, 40s, experienced master barber, confident smile, professional uniform, warm lighting, studio portrait, high quality, professional headshot',
    description: '团队成员 - 首席理发师李师傅'
  },
  {
    filename: 'team-member-2.jpg',
    size: { width: 400, height: 400 },
    prompt: 'Professional Chinese male barber portrait, 35s, skilled senior barber, friendly expression, modern style, professional uniform, warm lighting, studio portrait, high quality, professional headshot',
    description: '团队成员 - 高级理发师王师傅'
  },
  {
    filename: 'team-member-3.jpg',
    size: { width: 400, height: 400 },
    prompt: 'Professional Chinese male stylist portrait, 28s, young creative stylist, energetic smile, trendy appearance, professional uniform, warm lighting, studio portrait, high quality, professional headshot',
    description: '团队成员 - 造型师张师傅'
  }
];

// Gallery 图片配置
const galleryConfigs = [
  // 理发作品 (6张)
  {
    filename: 'gallery-haircut-1.jpg',
    size: { width: 400, height: 300 },
    prompt: 'Professional business haircut result, clean and sharp, modern short hairstyle, well-groomed appearance, professional photography, before and after style',
    description: 'Gallery - 经典商务短发'
  },
  {
    filename: 'gallery-haircut-2.jpg',
    size: { width: 400, height: 300 },
    prompt: 'Modern fade haircut, gradient technique, layered styling, contemporary mens hairstyle, professional barber work, high quality result',
    description: 'Gallery - 时尚渐变发型'
  },
  {
    filename: 'gallery-haircut-3.jpg',
    size: { width: 400, height: 300 },
    prompt: 'Creative unique hairstyle, artistic hair design, innovative cutting technique, personalized style, modern barbering art',
    description: 'Gallery - 个性创意发型'
  },
  {
    filename: 'gallery-haircut-4.jpg',
    size: { width: 400, height: 300 },
    prompt: 'Classic pompadour hairstyle, vintage inspired, well-styled hair, traditional barbering technique, elegant appearance',
    description: 'Gallery - 经典油头造型'
  },
  {
    filename: 'gallery-haircut-5.jpg',
    size: { width: 400, height: 300 },
    prompt: 'Trendy youth hairstyle, energetic and stylish, modern cutting technique, youthful appearance, contemporary style',
    description: 'Gallery - 青年时尚发型'
  },
  {
    filename: 'gallery-haircut-6.jpg',
    size: { width: 400, height: 300 },
    prompt: 'Wedding groom hairstyle, special occasion styling, elegant and refined, perfect for important events, professional grooming',
    description: 'Gallery - 婚礼造型'
  },
  // 胡须造型 (3张)
  {
    filename: 'gallery-beard-1.jpg',
    size: { width: 400, height: 300 },
    prompt: 'Professional beard trimming result, well-groomed facial hair, precise lines, masculine appearance, expert barbering',
    description: 'Gallery - 精致胡须造型'
  },
  {
    filename: 'gallery-beard-2.jpg',
    size: { width: 400, height: 300 },
    prompt: 'Detailed beard styling, artistic facial hair design, precise trimming technique, professional grooming result',
    description: 'Gallery - 胡须精细修剪'
  },
  {
    filename: 'gallery-beard-3.jpg',
    size: { width: 400, height: 300 },
    prompt: 'Classic mustache and beard combination, traditional styling, well-maintained facial hair, professional appearance',
    description: 'Gallery - 经典胡须搭配'
  },
  // 造型设计 (3张)
  {
    filename: 'gallery-styling-1.jpg',
    size: { width: 400, height: 300 },
    prompt: 'Vintage pompadour styling, classic retro look, gentleman style, traditional barbering art, elegant appearance',
    description: 'Gallery - 复古油头造型'
  },
  {
    filename: 'gallery-styling-2.jpg',
    size: { width: 400, height: 300 },
    prompt: 'Special occasion hair styling, formal event preparation, elegant grooming, professional styling service',
    description: 'Gallery - 特殊场合造型'
  },
  {
    filename: 'gallery-styling-3.jpg',
    size: { width: 400, height: 300 },
    prompt: 'Modern hair styling with products, contemporary look, professional styling technique, trendy appearance',
    description: 'Gallery - 现代产品造型'
  },
  // 店内环境 (3张)
  {
    filename: 'gallery-interior-1.jpg',
    size: { width: 400, height: 300 },
    prompt: 'Comfortable barbershop waiting area, cozy seating, warm atmosphere, welcoming environment, professional interior design',
    description: 'Gallery - 等候区域'
  },
  {
    filename: 'gallery-interior-2.jpg',
    size: { width: 400, height: 300 },
    prompt: 'Professional barbershop equipment, modern tools, high-quality instruments, organized workspace, professional setup',
    description: 'Gallery - 专业设备'
  },
  {
    filename: 'gallery-interior-3.jpg',
    size: { width: 400, height: 300 },
    prompt: 'Elegant barbershop interior design, comfortable atmosphere, professional environment, modern Chinese barbershop style',
    description: 'Gallery - 店内环境'
  }
];

// 合并所有配置
const allConfigs = [...imageConfigs, ...galleryConfigs];

// 下载图片函数
function downloadImage(url, filepath) {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(filepath);
    https.get(url, (response) => {
      response.pipe(file);
      file.on('finish', () => {
        file.close();
        resolve();
      });
    }).on('error', (err) => {
      fs.unlink(filepath, () => {}); // 删除失败的文件
      reject(err);
    });
  });
}

// 生成图片函数
async function generateImage(config) {
  try {
    console.log(`🎨 正在生成: ${config.description}`);
    
    // 这里应该调用 Replicate API
    // 由于需要实际的 API 调用，这里提供示例代码
    
    const response = await fetch('https://api.replicate.com/v1/predictions', {
      method: 'POST',
      headers: {
        'Authorization': `Token ${REPLICATE_API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        version: "39ed52f2a78e934b3ba6e2a89f5b1c712de7dfea535525255b1aa35c5565e08b",
        input: {
          prompt: config.prompt,
          negative_prompt: "blurry, low quality, distorted, cartoon, anime, text, watermark, logo",
          width: config.size.width,
          height: config.size.height,
          num_inference_steps: 50,
          guidance_scale: 7.5,
          scheduler: "K_EULER"
        }
      })
    });

    const prediction = await response.json();
    
    if (response.status !== 201) {
      throw new Error(`API Error: ${prediction.detail}`);
    }

    // 等待生成完成
    let result = prediction;
    while (result.status === "starting" || result.status === "processing") {
      await new Promise(resolve => setTimeout(resolve, 1000));
      const statusResponse = await fetch(`https://api.replicate.com/v1/predictions/${result.id}`, {
        headers: {
          'Authorization': `Token ${REPLICATE_API_TOKEN}`,
        },
      });
      result = await statusResponse.json();
    }

    if (result.status === "succeeded" && result.output && result.output.length > 0) {
      const imageUrl = result.output[0];
      const filepath = path.join(__dirname, '..', 'public', config.filename);
      
      await downloadImage(imageUrl, filepath);
      console.log(`✅ 已保存: ${config.filename}`);
      return true;
    } else {
      throw new Error(`生成失败: ${result.error || '未知错误'}`);
    }
  } catch (error) {
    console.error(`❌ 生成失败 ${config.filename}:`, error.message);
    return false;
  }
}

// 主函数
async function main() {
  console.log('🚀 开始生成 Tony\'s Barbershop 图片...\n');
  
  // 确保 public 目录存在
  const publicDir = path.join(__dirname, '..', 'public');
  if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir, { recursive: true });
  }

  let successCount = 0;
  let totalCount = allConfigs.length;

  for (const config of allConfigs) {
    const success = await generateImage(config);
    if (success) successCount++;
    
    // 添加延迟避免API限制
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  console.log(`\n🎉 图片生成完成!`);
  console.log(`✅ 成功: ${successCount}/${totalCount}`);
  console.log(`❌ 失败: ${totalCount - successCount}/${totalCount}`);
  
  if (successCount === totalCount) {
    console.log('\n🎯 所有图片生成成功! 现在可以运行网站查看效果。');
  } else {
    console.log('\n⚠️  部分图片生成失败，请检查API配置和网络连接。');
  }
}

// 运行脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { imageConfigs, galleryConfigs, generateImage };
