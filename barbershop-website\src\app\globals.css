@import "tailwindcss";

:root {
  /* Barbershop Color Palette - Improved Contrast */
  --background: #ffffff;
  --foreground: #0f0f0f;
  --primary: #000000;
  --primary-foreground: #ffffff;
  --secondary: #dc2626;
  --secondary-foreground: #ffffff;
  --accent: #d4af37;
  --accent-foreground: #000000;
  --muted: #f8f9fa;
  --muted-foreground: #6b7280;
  --border: #e5e7eb;
  --card: #ffffff;
  --card-foreground: #0f0f0f;
  --destructive: #dc2626;
  --destructive-foreground: #ffffff;
  --ring: #d4af37;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-border: var(--border);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-ring: var(--ring);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  font-feature-settings: "rlig" 1, "calt" 1;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #fafafa;
    --primary: #ffffff;
    --primary-foreground: #0a0a0a;
    --secondary: #ef4444;
    --secondary-foreground: #fafafa;
    --accent: #fbbf24;
    --accent-foreground: #0a0a0a;
    --muted: #1a1a1a;
    --muted-foreground: #a1a1aa;
    --border: #27272a;
    --card: #0a0a0a;
    --card-foreground: #fafafa;
    --destructive: #ef4444;
    --destructive-foreground: #fafafa;
    --ring: #fbbf24;
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary);
}

/* Improved text contrast and readability */
.text-muted-foreground {
  color: var(--muted-foreground);
}

.text-primary-foreground {
  color: var(--primary-foreground);
}

.text-secondary-foreground {
  color: var(--secondary-foreground);
}

.text-accent-foreground {
  color: var(--accent-foreground);
}

/* Better button contrast */
.bg-primary {
  background-color: var(--primary);
  color: var(--primary-foreground);
}

.bg-secondary {
  background-color: var(--secondary);
  color: var(--secondary-foreground);
}

.bg-accent {
  background-color: var(--accent);
  color: var(--accent-foreground);
}

/* Enhanced focus states for accessibility */
*:focus-visible {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
}

/* Better text shadow for hero sections */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Improved backdrop blur */
.backdrop-blur-enhanced {
  backdrop-filter: blur(12px) saturate(180%);
  -webkit-backdrop-filter: blur(12px) saturate(180%);
}
