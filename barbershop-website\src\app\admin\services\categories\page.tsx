"use client"

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { AdminLayout, PageContainer } from '@/components/admin/layout/admin-layout'
import { DataTable } from '@/components/admin/ui/data-table'
import { Badge } from '@/components/admin/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { 
  Plus, 
  Tag,
  Edit,
  Trash2,
  ArrowUp,
  ArrowDown,
  ToggleLeft,
  ToggleRight,
  Save,
  X
} from 'lucide-react'
import { categoryStore, serviceStore } from '@/lib/admin/storage'
import { ServiceCategory } from '@/lib/types/admin'

export default function ServiceCategoriesPage() {
  const [categories, setCategories] = useState<ServiceCategory[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingCategory, setEditingCategory] = useState<ServiceCategory | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    isActive: true
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    loadData()
  }, [])

  const loadData = () => {
    setLoading(true)
    try {
      const allCategories = categoryStore.getAll()
      // 按order排序
      const sortedCategories = allCategories.sort((a, b) => a.order - b.order)
      setCategories(sortedCategories)
    } catch (error) {
      console.error('Failed to load categories:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    const newErrors: Record<string, string> = {}
    if (!formData.name.trim()) {
      newErrors.name = '请输入分类名称'
    }
    
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors)
      return
    }

    try {
      if (editingCategory) {
        // 更新分类
        categoryStore.update(editingCategory.id, {
          name: formData.name.trim(),
          description: formData.description.trim() || undefined,
          isActive: formData.isActive
        })
      } else {
        // 创建新分类
        const maxOrder = Math.max(...categories.map(c => c.order), 0)
        categoryStore.create({
          name: formData.name.trim(),
          description: formData.description.trim() || undefined,
          order: maxOrder + 1,
          isActive: formData.isActive
        })
      }
      
      resetForm()
      loadData()
    } catch (error) {
      console.error('Failed to save category:', error)
      setErrors({ submit: '保存失败，请重试' })
    }
  }

  const resetForm = () => {
    setFormData({ name: '', description: '', isActive: true })
    setEditingCategory(null)
    setShowForm(false)
    setErrors({})
  }

  const handleEdit = (category: ServiceCategory) => {
    setEditingCategory(category)
    setFormData({
      name: category.name,
      description: category.description || '',
      isActive: category.isActive
    })
    setShowForm(true)
  }

  const handleDelete = (categoryId: string) => {
    // 检查是否有服务使用此分类
    const services = serviceStore.getAll()
    const servicesUsingCategory = services.filter(s => s.category === categoryId)
    
    if (servicesUsingCategory.length > 0) {
      alert(`无法删除此分类，还有 ${servicesUsingCategory.length} 个服务正在使用此分类。`)
      return
    }

    if (confirm('确定要删除这个分类吗？')) {
      try {
        categoryStore.delete(categoryId)
        loadData()
      } catch (error) {
        console.error('Failed to delete category:', error)
      }
    }
  }

  const handleToggleStatus = (categoryId: string, currentStatus: boolean) => {
    try {
      categoryStore.update(categoryId, { isActive: !currentStatus })
      loadData()
    } catch (error) {
      console.error('Failed to toggle category status:', error)
    }
  }

  const moveCategory = (categoryId: string, direction: 'up' | 'down') => {
    const currentIndex = categories.findIndex(c => c.id === categoryId)
    if (currentIndex === -1) return

    const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1
    if (targetIndex < 0 || targetIndex >= categories.length) return

    const currentCategory = categories[currentIndex]
    const targetCategory = categories[targetIndex]

    // 交换order值
    categoryStore.update(currentCategory.id, { order: targetCategory.order })
    categoryStore.update(targetCategory.id, { order: currentCategory.order })

    loadData()
  }

  const getServiceCount = (categoryId: string) => {
    const services = serviceStore.getAll()
    return services.filter(s => s.category === categoryId).length
  }

  const columns = [
    {
      key: 'name',
      title: '分类信息',
      render: (value: string, record: ServiceCategory) => (
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
            <Tag className="h-5 w-5 text-primary" />
          </div>
          <div>
            <div className="flex items-center space-x-2">
              <p className="font-medium">{value}</p>
              {!record.isActive && (
                <Badge variant="destructive" size="sm">
                  已停用
                </Badge>
              )}
            </div>
            <p className="text-sm text-muted-foreground line-clamp-1">
              {record.description || '暂无描述'}
            </p>
          </div>
        </div>
      )
    },
    {
      key: 'order',
      title: '排序',
      render: (value: number, record: ServiceCategory, index: number) => (
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium">{value}</span>
          <div className="flex space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => moveCategory(record.id, 'up')}
              disabled={index === 0}
            >
              <ArrowUp className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => moveCategory(record.id, 'down')}
              disabled={index === categories.length - 1}
            >
              <ArrowDown className="h-3 w-3" />
            </Button>
          </div>
        </div>
      )
    },
    {
      key: 'serviceCount',
      title: '服务数量',
      render: (value: any, record: ServiceCategory) => (
        <div className="text-center">
          <span className="text-lg font-semibold text-blue-600">
            {getServiceCount(record.id)}
          </span>
          <p className="text-xs text-muted-foreground">个服务</p>
        </div>
      )
    },
    {
      key: 'isActive',
      title: '状态',
      render: (value: boolean, record: ServiceCategory) => (
        <div className="flex items-center space-x-2">
          <button
            onClick={() => handleToggleStatus(record.id, value)}
            className="flex items-center space-x-1 hover:opacity-75 transition-opacity"
          >
            {value ? (
              <>
                <ToggleRight className="h-5 w-5 text-green-500" />
                <span className="text-sm text-green-600">启用</span>
              </>
            ) : (
              <>
                <ToggleLeft className="h-5 w-5 text-gray-400" />
                <span className="text-sm text-gray-500">停用</span>
              </>
            )}
          </button>
        </div>
      )
    }
  ]

  return (
    <AdminLayout>
      <PageContainer
        title="服务分类管理"
        description="管理服务分类，设置分类顺序和状态"
        action={
          <div className="flex space-x-2">
            <Link href="/admin/services">
              <Button variant="outline">
                返回服务管理
              </Button>
            </Link>
            <Button onClick={() => setShowForm(true)}>
              <Plus className="h-4 w-4 mr-2" />
              新增分类
            </Button>
          </div>
        }
      >
        {/* 分类表格 */}
        <div className="bg-card border border-border rounded-lg">
          <DataTable
            data={categories}
            columns={columns}
            loading={loading}
            actions={{
              title: '操作',
              render: (record: ServiceCategory) => (
                <div className="flex items-center space-x-1">
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => handleEdit(record)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => handleDelete(record.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              )
            }}
          />
        </div>

        {/* 分类表单弹窗 */}
        {showForm && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-card border border-border rounded-lg p-6 w-full max-w-md mx-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">
                  {editingCategory ? '编辑分类' : '新增分类'}
                </h3>
                <Button variant="ghost" size="sm" onClick={resetForm}>
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    分类名称 <span className="text-red-500">*</span>
                  </label>
                  <Input
                    value={formData.name}
                    onChange={(e) => {
                      setFormData(prev => ({ ...prev, name: e.target.value }))
                      if (errors.name) setErrors(prev => ({ ...prev, name: '' }))
                    }}
                    placeholder="请输入分类名称"
                    className={errors.name ? 'border-red-500' : ''}
                  />
                  {errors.name && (
                    <p className="text-sm text-red-500 mt-1">{errors.name}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    分类描述
                  </label>
                  <Textarea
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="请输入分类描述（可选）"
                    rows={3}
                  />
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="isActive"
                    checked={formData.isActive}
                    onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                    className="w-4 h-4 text-primary border-border rounded focus:ring-primary"
                  />
                  <label htmlFor="isActive" className="text-sm font-medium">
                    启用此分类
                  </label>
                </div>

                {errors.submit && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                    <p className="text-sm text-red-600">{errors.submit}</p>
                  </div>
                )}

                <div className="flex justify-end space-x-3 pt-4">
                  <Button type="button" variant="outline" onClick={resetForm}>
                    取消
                  </Button>
                  <Button type="submit">
                    <Save className="h-4 w-4 mr-2" />
                    {editingCategory ? '更新' : '创建'}
                  </Button>
                </div>
              </form>
            </div>
          </div>
        )}
      </PageContainer>
    </AdminLayout>
  )
}
