"use client"

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { ChevronRight, Home } from 'lucide-react'
import { cn } from '@/lib/utils'

interface BreadcrumbItem {
  label: string
  href?: string
  isActive?: boolean
}

// 路径映射
const pathMap: Record<string, string> = {
  'admin': '管理后台',
  'appointments': '预约管理',
  'customers': '客户管理',
  'services': '服务项目',
  'staff': '员工管理',
  'analytics': '数据统计',
  'settings': '系统设置',
  'new': '新建',
  'edit': '编辑'
}

interface BreadcrumbProps {
  className?: string
  items?: BreadcrumbItem[]
}

export function Breadcrumb({ className, items }: BreadcrumbProps) {
  const pathname = usePathname()

  // 如果提供了自定义items，使用自定义的
  const breadcrumbItems = items || generateBreadcrumbItems(pathname)

  if (breadcrumbItems.length <= 1) {
    return null
  }

  return (
    <nav className={cn("flex items-center space-x-1 text-sm text-muted-foreground", className)}>
      {breadcrumbItems.map((item, index) => (
        <div key={index} className="flex items-center">
          {index > 0 && (
            <ChevronRight className="h-4 w-4 mx-1" />
          )}
          
          {item.href && !item.isActive ? (
            <Link
              href={item.href}
              className="hover:text-foreground transition-colors"
            >
              {index === 0 && <Home className="h-4 w-4 mr-1 inline" />}
              {item.label}
            </Link>
          ) : (
            <span className={cn(
              item.isActive ? "text-foreground font-medium" : "text-muted-foreground"
            )}>
              {index === 0 && <Home className="h-4 w-4 mr-1 inline" />}
              {item.label}
            </span>
          )}
        </div>
      ))}
    </nav>
  )
}

function generateBreadcrumbItems(pathname: string): BreadcrumbItem[] {
  const segments = pathname.split('/').filter(Boolean)
  const items: BreadcrumbItem[] = []

  // 构建面包屑路径
  let currentPath = ''
  
  segments.forEach((segment, index) => {
    currentPath += `/${segment}`
    const isLast = index === segments.length - 1
    const label = pathMap[segment] || segment

    // 特殊处理动态路由
    if (segment.match(/^[a-f0-9-]{36}$/)) {
      // UUID格式，可能是ID
      items.push({
        label: '详情',
        href: isLast ? undefined : currentPath,
        isActive: isLast
      })
    } else {
      items.push({
        label,
        href: isLast ? undefined : currentPath,
        isActive: isLast
      })
    }
  })

  return items
}

// 自定义面包屑Hook
export function useBreadcrumb() {
  const pathname = usePathname()

  const setBreadcrumb = (items: BreadcrumbItem[]) => {
    // 这里可以实现全局面包屑状态管理
    // 暂时返回items，实际使用时可以结合Context或状态管理
    return items
  }

  const addBreadcrumbItem = (item: BreadcrumbItem) => {
    const currentItems = generateBreadcrumbItems(pathname)
    return [...currentItems, item]
  }

  return {
    currentItems: generateBreadcrumbItems(pathname),
    setBreadcrumb,
    addBreadcrumbItem
  }
}
