globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/admin/appointments/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/script.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/script.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/accessibility/skip-links.tsx":{"*":{"id":"(ssr)/./src/components/accessibility/skip-links.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/performance/web-vitals.tsx":{"*":{"id":"(ssr)/./src/components/performance/web-vitals.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/navbar.tsx":{"*":{"id":"(ssr)/./src/components/layout/navbar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/about-preview.tsx":{"*":{"id":"(ssr)/./src/components/sections/about-preview.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/cta-section.tsx":{"*":{"id":"(ssr)/./src/components/sections/cta-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/hero-section.tsx":{"*":{"id":"(ssr)/./src/components/sections/hero-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/services-preview.tsx":{"*":{"id":"(ssr)/./src/components/sections/services-preview.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/testimonials-section.tsx":{"*":{"id":"(ssr)/./src/components/sections/testimonials-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/pages/booking-page-content.tsx":{"*":{"id":"(ssr)/./src/components/pages/booking-page-content.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/login/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/appointments/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/appointments/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/staff/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/staff/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/staff/[id]/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/staff/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/analytics/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/analytics/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\node_modules\\next\\dist\\client\\script.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/script.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\node_modules\\next\\dist\\esm\\client\\script.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/script.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Playfair_Display\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"playfair\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Playfair_Display\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"playfair\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\accessibility\\skip-links.tsx":{"id":"(app-pages-browser)/./src/components/accessibility/skip-links.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\performance\\web-vitals.tsx":{"id":"(app-pages-browser)/./src/components/performance/web-vitals.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\node_modules\\next\\dist\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\node_modules\\next\\dist\\esm\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\layout\\navbar.tsx":{"id":"(app-pages-browser)/./src/components/layout/navbar.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\sections\\about-preview.tsx":{"id":"(app-pages-browser)/./src/components/sections/about-preview.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\sections\\cta-section.tsx":{"id":"(app-pages-browser)/./src/components/sections/cta-section.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\sections\\hero-section.tsx":{"id":"(app-pages-browser)/./src/components/sections/hero-section.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\sections\\services-preview.tsx":{"id":"(app-pages-browser)/./src/components/sections/services-preview.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\sections\\testimonials-section.tsx":{"id":"(app-pages-browser)/./src/components/sections/testimonials-section.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\pages\\booking-page-content.tsx":{"id":"(app-pages-browser)/./src/components/pages/booking-page-content.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/page.tsx","name":"*","chunks":["app/admin/page","static/chunks/app/admin/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\login\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/login/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\appointments\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/appointments/page.tsx","name":"*","chunks":["app/admin/appointments/page","static/chunks/app/admin/appointments/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\staff\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/staff/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\staff\\[id]\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/staff/[id]/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\analytics\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/analytics/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\":[],"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\page":[],"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\layout":[],"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\page":[],"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\appointments\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/script.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/script.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/accessibility/skip-links.tsx":{"*":{"id":"(rsc)/./src/components/accessibility/skip-links.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/performance/web-vitals.tsx":{"*":{"id":"(rsc)/./src/components/performance/web-vitals.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/navbar.tsx":{"*":{"id":"(rsc)/./src/components/layout/navbar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/about-preview.tsx":{"*":{"id":"(rsc)/./src/components/sections/about-preview.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/cta-section.tsx":{"*":{"id":"(rsc)/./src/components/sections/cta-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/hero-section.tsx":{"*":{"id":"(rsc)/./src/components/sections/hero-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/services-preview.tsx":{"*":{"id":"(rsc)/./src/components/sections/services-preview.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/testimonials-section.tsx":{"*":{"id":"(rsc)/./src/components/sections/testimonials-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/pages/booking-page-content.tsx":{"*":{"id":"(rsc)/./src/components/pages/booking-page-content.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/login/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/appointments/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/appointments/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/staff/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/staff/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/staff/[id]/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/staff/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/analytics/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/analytics/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}