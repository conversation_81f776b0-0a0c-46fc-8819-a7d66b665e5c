import Link from "next/link"
import Image from "next/image"
import { Button } from "@/components/ui/button"

const stats = [
  {
    icon: "🏆",
    number: "15+",
    label: "年专业经验",
  },
  {
    icon: "👥",
    number: "5000+",
    label: "满意客户",
  },
  {
    icon: "🕐",
    number: "24/7",
    label: "在线预约",
  },
  {
    icon: "✂️",
    number: "100%",
    label: "专业保证",
  },
]

export function AboutPreview() {
  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="space-y-6">
            <div className="space-y-4">
              <h2 className="text-3xl md:text-4xl font-bold">
                传承经典，创新未来
              </h2>
              <p className="text-lg text-muted-foreground">
                Classic Cuts 成立于2009年，我们致力于为每一位客户提供最专业的理发服务。
                我们的理发师团队拥有丰富的经验和精湛的技艺，结合传统理发工艺与现代时尚元素。
              </p>
              <p className="text-muted-foreground">
                在这里，您不仅能享受到专业的理发服务，更能体验到传统理发店的温馨氛围。
                我们相信，每一次剪发都是一次艺术创作，每一位客户都值得我们用心对待。
              </p>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 gap-6 py-6">
              {stats.map((stat, index) => {
                return (
                  <div key={index} className="text-center">
                    <div className="mx-auto mb-2 p-2 bg-primary/10 rounded-full w-fit">
                      <span className="text-2xl">{stat.icon}</span>
                    </div>
                    <div className="text-2xl font-bold text-primary">
                      {stat.number}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {stat.label}
                    </div>
                  </div>
                )
              })}
            </div>

            <Button asChild size="lg">
              <Link href="/about">了解更多</Link>
            </Button>
          </div>

          {/* Image */}
          <div className="relative">
            <div className="relative h-96 lg:h-[500px] rounded-lg overflow-hidden">
              <Image
                src="/about-barbershop.jpg"
                alt="Classic Cuts Barbershop Team"
                fill
                className="object-cover"
                placeholder="blur"
                blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
              />
            </div>
            
            {/* Decorative Elements */}
            <div className="absolute -top-4 -left-4 w-24 h-24 bg-accent/20 rounded-full blur-xl"></div>
            <div className="absolute -bottom-4 -right-4 w-32 h-32 bg-secondary/20 rounded-full blur-xl"></div>
          </div>
        </div>
      </div>
    </section>
  )
}
