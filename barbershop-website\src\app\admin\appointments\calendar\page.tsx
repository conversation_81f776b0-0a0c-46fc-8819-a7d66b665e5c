"use client"

import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { AdminLayout, PageContainer } from '@/components/admin/layout/admin-layout'
import { CalendarView } from '@/components/admin/appointments/calendar-view'
import { Button } from '@/components/ui/button'
import { List, Plus } from 'lucide-react'
import { Appointment } from '@/lib/types/admin'

export default function AppointmentCalendarPage() {
  const router = useRouter()

  const handleAppointmentClick = (appointment: Appointment) => {
    router.push(`/admin/appointments/${appointment.id}`)
  }

  return (
    <AdminLayout>
      <PageContainer
        title="预约日历"
        description="以日历形式查看和管理预约"
        action={
          <div className="flex space-x-2">
            <Link href="/admin/appointments">
              <Button variant="outline">
                <List className="h-4 w-4 mr-2" />
                列表视图
              </Button>
            </Link>
            <Link href="/admin/appointments/new">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                新建预约
              </Button>
            </Link>
          </div>
        }
      >
        <div className="bg-card border border-border rounded-lg p-6">
          <CalendarView onAppointmentClick={handleAppointmentClick} />
        </div>
      </PageContainer>
    </AdminLayout>
  )
}
