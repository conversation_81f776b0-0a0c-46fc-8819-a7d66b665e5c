"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/web-vitals";
exports.ids = ["vendor-chunks/web-vitals"];
exports.modules = {

/***/ "(ssr)/./node_modules/web-vitals/dist/web-vitals.js":
/*!****************************************************!*\
  !*** ./node_modules/web-vitals/dist/web-vitals.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CLSThresholds: () => (/* binding */ T),\n/* harmony export */   FCPThresholds: () => (/* binding */ b),\n/* harmony export */   INPThresholds: () => (/* binding */ N),\n/* harmony export */   LCPThresholds: () => (/* binding */ x),\n/* harmony export */   TTFBThresholds: () => (/* binding */ $),\n/* harmony export */   onCLS: () => (/* binding */ E),\n/* harmony export */   onFCP: () => (/* binding */ P),\n/* harmony export */   onINP: () => (/* binding */ S),\n/* harmony export */   onLCP: () => (/* binding */ O),\n/* harmony export */   onTTFB: () => (/* binding */ H)\n/* harmony export */ });\nlet e=-1;const t=t=>{addEventListener(\"pageshow\",(n=>{n.persisted&&(e=n.timeStamp,t(n))}),!0)},n=(e,t,n,i)=>{let o,s;return r=>{t.value>=0&&(r||i)&&(s=t.value-(o??0),(s||void 0===o)&&(o=t.value,t.delta=s,t.rating=((e,t)=>e>t[1]?\"poor\":e>t[0]?\"needs-improvement\":\"good\")(t.value,n),e(t)))}},i=e=>{requestAnimationFrame((()=>requestAnimationFrame((()=>e()))))},o=()=>{const e=performance.getEntriesByType(\"navigation\")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},s=()=>{const e=o();return e?.activationStart??0},r=(t,n=-1)=>{const i=o();let r=\"navigate\";e>=0?r=\"back-forward-cache\":i&&(document.prerendering||s()>0?r=\"prerender\":document.wasDiscarded?r=\"restore\":i.type&&(r=i.type.replace(/_/g,\"-\")));return{name:t,value:n,rating:\"good\",delta:0,entries:[],id:`v5-${Date.now()}-${Math.floor(8999999999999*Math.random())+1e12}`,navigationType:r}},c=new WeakMap;function a(e,t){return c.get(e)||c.set(e,new t),c.get(e)}class d{t;i=0;o=[];h(e){if(e.hadRecentInput)return;const t=this.o[0],n=this.o.at(-1);this.i&&t&&n&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(this.i+=e.value,this.o.push(e)):(this.i=e.value,this.o=[e]),this.t?.(e)}}const h=(e,t,n={})=>{try{if(PerformanceObserver.supportedEntryTypes.includes(e)){const i=new PerformanceObserver((e=>{Promise.resolve().then((()=>{t(e.getEntries())}))}));return i.observe({type:e,buffered:!0,...n}),i}}catch{}},f=e=>{let t=!1;return()=>{t||(e(),t=!0)}};let u=-1;const l=()=>\"hidden\"!==document.visibilityState||document.prerendering?1/0:0,m=e=>{\"hidden\"===document.visibilityState&&u>-1&&(u=\"visibilitychange\"===e.type?e.timeStamp:0,v())},g=()=>{addEventListener(\"visibilitychange\",m,!0),addEventListener(\"prerenderingchange\",m,!0)},v=()=>{removeEventListener(\"visibilitychange\",m,!0),removeEventListener(\"prerenderingchange\",m,!0)},p=()=>{if(u<0){const e=s(),n=document.prerendering?void 0:globalThis.performance.getEntriesByType(\"visibility-state\").filter((t=>\"hidden\"===t.name&&t.startTime>e))[0]?.startTime;u=n??l(),g(),t((()=>{setTimeout((()=>{u=l(),g()}))}))}return{get firstHiddenTime(){return u}}},y=e=>{document.prerendering?addEventListener(\"prerenderingchange\",(()=>e()),!0):e()},b=[1800,3e3],P=(e,o={})=>{y((()=>{const c=p();let a,d=r(\"FCP\");const f=h(\"paint\",(e=>{for(const t of e)\"first-contentful-paint\"===t.name&&(f.disconnect(),t.startTime<c.firstHiddenTime&&(d.value=Math.max(t.startTime-s(),0),d.entries.push(t),a(!0)))}));f&&(a=n(e,d,b,o.reportAllChanges),t((t=>{d=r(\"FCP\"),a=n(e,d,b,o.reportAllChanges),i((()=>{d.value=performance.now()-t.timeStamp,a(!0)}))})))}))},T=[.1,.25],E=(e,o={})=>{P(f((()=>{let s,c=r(\"CLS\",0);const f=a(o,d),u=e=>{for(const t of e)f.h(t);f.i>c.value&&(c.value=f.i,c.entries=f.o,s())},l=h(\"layout-shift\",u);l&&(s=n(e,c,T,o.reportAllChanges),document.addEventListener(\"visibilitychange\",(()=>{\"hidden\"===document.visibilityState&&(u(l.takeRecords()),s(!0))})),t((()=>{f.i=0,c=r(\"CLS\",0),s=n(e,c,T,o.reportAllChanges),i((()=>s()))})),setTimeout(s))})))};let _=0,L=1/0,M=0;const C=e=>{for(const t of e)t.interactionId&&(L=Math.min(L,t.interactionId),M=Math.max(M,t.interactionId),_=M?(M-L)/7+1:0)};let I;const w=()=>I?_:performance.interactionCount??0,F=()=>{\"interactionCount\"in performance||I||(I=h(\"event\",C,{type:\"event\",buffered:!0,durationThreshold:0}))};let k=0;class A{u=[];l=new Map;m;v;p(){k=w(),this.u.length=0,this.l.clear()}P(){const e=Math.min(this.u.length-1,Math.floor((w()-k)/50));return this.u[e]}h(e){if(this.m?.(e),!e.interactionId&&\"first-input\"!==e.entryType)return;const t=this.u.at(-1);let n=this.l.get(e.interactionId);if(n||this.u.length<10||e.duration>t.T){if(n?e.duration>n.T?(n.entries=[e],n.T=e.duration):e.duration===n.T&&e.startTime===n.entries[0].startTime&&n.entries.push(e):(n={id:e.interactionId,entries:[e],T:e.duration},this.l.set(n.id,n),this.u.push(n)),this.u.sort(((e,t)=>t.T-e.T)),this.u.length>10){const e=this.u.splice(10);for(const t of e)this.l.delete(t.id)}this.v?.(n)}}}const B=e=>{const t=globalThis.requestIdleCallback||setTimeout;\"hidden\"===document.visibilityState?e():(e=f(e),document.addEventListener(\"visibilitychange\",e,{once:!0}),t((()=>{e(),document.removeEventListener(\"visibilitychange\",e)})))},N=[200,500],S=(e,i={})=>{globalThis.PerformanceEventTiming&&\"interactionId\"in PerformanceEventTiming.prototype&&y((()=>{F();let o,s=r(\"INP\");const c=a(i,A),d=e=>{B((()=>{for(const t of e)c.h(t);const t=c.P();t&&t.T!==s.value&&(s.value=t.T,s.entries=t.entries,o())}))},f=h(\"event\",d,{durationThreshold:i.durationThreshold??40});o=n(e,s,N,i.reportAllChanges),f&&(f.observe({type:\"first-input\",buffered:!0}),document.addEventListener(\"visibilitychange\",(()=>{\"hidden\"===document.visibilityState&&(d(f.takeRecords()),o(!0))})),t((()=>{c.p(),s=r(\"INP\"),o=n(e,s,N,i.reportAllChanges)})))}))};class q{m;h(e){this.m?.(e)}}const x=[2500,4e3],O=(e,o={})=>{y((()=>{const c=p();let d,u=r(\"LCP\");const l=a(o,q),m=e=>{o.reportAllChanges||(e=e.slice(-1));for(const t of e)l.h(t),t.startTime<c.firstHiddenTime&&(u.value=Math.max(t.startTime-s(),0),u.entries=[t],d())},g=h(\"largest-contentful-paint\",m);if(g){d=n(e,u,x,o.reportAllChanges);const s=f((()=>{m(g.takeRecords()),g.disconnect(),d(!0)}));for(const e of[\"keydown\",\"click\",\"visibilitychange\"])addEventListener(e,(()=>B(s)),{capture:!0,once:!0});t((t=>{u=r(\"LCP\"),d=n(e,u,x,o.reportAllChanges),i((()=>{u.value=performance.now()-t.timeStamp,d(!0)}))}))}}))},$=[800,1800],D=e=>{document.prerendering?y((()=>D(e))):\"complete\"!==document.readyState?addEventListener(\"load\",(()=>D(e)),!0):setTimeout(e)},H=(e,i={})=>{let c=r(\"TTFB\"),a=n(e,c,$,i.reportAllChanges);D((()=>{const d=o();d&&(c.value=Math.max(d.responseStart-s(),0),c.entries=[d],a(!0),t((()=>{c=r(\"TTFB\",0),a=n(e,c,$,i.reportAllChanges),a(!0)})))}))};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/web-vitals/dist/web-vitals.js\n");

/***/ })

};
;