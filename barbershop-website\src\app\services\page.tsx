import { Metadata } from "next"
import Image from "next/image"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export const metadata: Metadata = {
  title: "专业理发服务 - Classic Cuts Barbershop",
  description: "探索我们全方位的专业理发服务，包括经典理发、胡须修剪、头发造型等。专业理发师，合理价格，优质服务。",
  keywords: "理发服务,男士理发,胡须修剪,头发造型,洗发护理,面部护理",
}

const services = [
  {
    id: "haircuts",
    icon: "✂️",
    title: "经典理发",
    description: "专业理发师为您提供精准的剪发服务，打造适合您的完美发型",
    basePrice: "¥80-120",
    duration: "45分钟",
    styles: [
      { name: "商务短发", price: "¥80", description: "简洁专业，适合商务场合" },
      { name: "时尚层次", price: "¥90", description: "现代层次感，展现个性魅力" },
      { name: "经典油头", price: "¥100", description: "复古绅士风格，永不过时" },
      { name: "渐变短发", price: "¥110", description: "精细渐变技术，层次分明" },
      { name: "个性造型", price: "¥120", description: "根据脸型定制，独一无二" }
    ]
  },
  {
    id: "beard",
    icon: "🧔",
    title: "胡须修剪",
    description: "精细的胡须造型和修剪，让您的面部轮廓更加立体有型",
    basePrice: "¥60-80",
    duration: "30分钟",
    styles: [
      { name: "胡须修整", price: "¥60", description: "基础修剪，保持整洁" },
      { name: "造型设计", price: "¥70", description: "根据脸型设计胡须造型" },
      { name: "精细雕刻", price: "¥80", description: "精细线条，艺术造型" }
    ]
  },
  {
    id: "styling",
    icon: "✨",
    title: "头发造型",
    description: "使用专业造型产品，为您打造时尚个性的发型造型",
    basePrice: "¥50-70",
    duration: "20分钟",
    styles: [
      { name: "日常造型", price: "¥50", description: "简单自然，日常适用" },
      { name: "商务造型", price: "¥60", description: "正式专业，商务首选" },
      { name: "特殊场合", price: "¥70", description: "精致造型，重要场合" }
    ]
  },
  {
    id: "wash",
    icon: "🧴",
    title: "洗发护理",
    description: "深层清洁和滋养护理，让您的头发健康有光泽",
    basePrice: "¥40-60",
    duration: "25分钟",
    styles: [
      { name: "基础洗发", price: "¥40", description: "温和清洁，去除油脂" },
      { name: "深层护理", price: "¥50", description: "营养滋润，修复发质" },
      { name: "头皮按摩", price: "¥60", description: "舒缓放松，促进血液循环" }
    ]
  },
  {
    id: "facial",
    icon: "🧖‍♂️",
    title: "面部护理",
    description: "专业面部清洁和护理，让您的肌肤焕发健康光彩",
    basePrice: "¥80-120",
    duration: "40分钟",
    styles: [
      { name: "基础清洁", price: "¥80", description: "深层清洁，去除污垢" },
      { name: "保湿护理", price: "¥100", description: "补水滋润，改善肌肤" },
      { name: "抗衰护理", price: "¥120", description: "紧致肌肤，延缓衰老" }
    ]
  },
  {
    id: "package",
    icon: "🎁",
    title: "套餐服务",
    description: "组合服务更优惠，一站式解决您的所有需求",
    basePrice: "¥150-250",
    duration: "90-120分钟",
    styles: [
      { name: "经典套餐", price: "¥150", description: "理发 + 洗发 + 基础造型" },
      { name: "绅士套餐", price: "¥200", description: "理发 + 胡须修剪 + 面部护理" },
      { name: "尊享套餐", price: "¥250", description: "全套服务 + 头皮按摩 + 特殊造型" }
    ]
  }
]

const features = [
  {
    icon: "👨‍💼",
    title: "专业理发师",
    description: "15年以上经验的资深理发师团队"
  },
  {
    icon: "🏆",
    title: "品质保证",
    description: "100%满意保证，不满意免费重做"
  },
  {
    icon: "🕐",
    title: "准时服务",
    description: "严格按预约时间，绝不让您久等"
  },
  {
    icon: "💎",
    title: "优质产品",
    description: "使用国际知名品牌专业产品"
  }
]

export default function ServicesPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-primary to-primary/80 text-primary-foreground">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-3xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              专业理发服务
            </h1>
            <p className="text-xl mb-8 text-primary-foreground/90">
              传承经典理发工艺，提供全方位的男士美容服务。每一项服务都体现我们对品质的追求和对客户的用心。
            </p>
            <div className="flex flex-wrap justify-center gap-6 text-sm">
              <div className="flex items-center space-x-2">
                <span className="text-accent text-lg">⭐</span>
                <span>5星级服务</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-accent text-lg">🏆</span>
                <span>专业认证</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-accent text-lg">💯</span>
                <span>满意保证</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              服务项目
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              从经典理发到专业护理，我们为您提供全方位的男士美容服务
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {services.map((service) => (
              <Card key={service.id} className="group hover:shadow-lg transition-all duration-300">
                <CardHeader>
                  <div className="flex items-center space-x-4">
                    <div className="p-3 bg-primary/10 rounded-full group-hover:bg-primary/20 transition-colors">
                      <span className="text-3xl">{service.icon}</span>
                    </div>
                    <div>
                      <CardTitle className="text-2xl">{service.title}</CardTitle>
                      <CardDescription className="text-base">
                        {service.description}
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center justify-between pt-4">
                    <div className="text-2xl font-bold text-primary">
                      {service.basePrice}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {service.duration}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {service.styles.map((style, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                        <div>
                          <div className="font-medium">{style.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {style.description}
                          </div>
                        </div>
                        <div className="text-lg font-semibold text-primary">
                          {style.price}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              为什么选择我们
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              我们不仅提供专业的理发服务，更注重每一个细节的完美呈现
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-all duration-300">
                <CardHeader>
                  <div className="mx-auto mb-4 p-4 bg-primary/10 rounded-full w-fit">
                    <span className="text-4xl">{feature.icon}</span>
                  </div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                  <CardDescription>
                    {feature.description}
                  </CardDescription>
                </CardHeader>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              准备好体验专业服务了吗？
            </h2>
            <p className="text-xl mb-8 text-primary-foreground/90">
              立即预约，让我们的专业理发师为您打造完美造型
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button asChild size="lg" className="bg-accent hover:bg-accent/90 text-black font-semibold px-8 py-3 text-lg">
                <Link href="/booking">立即在线预约</Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary px-8 py-3 text-lg">
                <Link href="/contact">联系我们</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
