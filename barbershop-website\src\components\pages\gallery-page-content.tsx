"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { FadeIn, StaggeredFadeIn, ScaleIn } from "@/components/animations/fade-in"
import { FloatingElement } from "@/components/animations/page-transition"
import { OptimizedImage, ImageSizes } from "@/components/ui/optimized-image"

// Note: In a real application, these would be actual image URLs
const galleryImages = [
  {
    id: 1,
    category: "haircuts",
    title: "经典商务短发",
    description: "专业商务造型，简洁大方",
    imageUrl: "/api/placeholder/400/300",
    beforeAfter: false
  },
  {
    id: 2,
    category: "haircuts",
    title: "时尚渐变发型",
    description: "现代渐变技术，层次分明",
    imageUrl: "/api/placeholder/400/300",
    beforeAfter: true
  },
  {
    id: 3,
    category: "beard",
    title: "精致胡须造型",
    description: "根据脸型设计的胡须造型",
    imageUrl: "/api/placeholder/400/300",
    beforeAfter: false
  },
  {
    id: 4,
    category: "styling",
    title: "复古油头造型",
    description: "经典复古风格，绅士魅力",
    imageUrl: "/api/placeholder/400/300",
    beforeAfter: true
  },
  {
    id: 5,
    category: "haircuts",
    title: "个性创意发型",
    description: "独特设计，展现个性",
    imageUrl: "/api/placeholder/400/300",
    beforeAfter: false
  },
  {
    id: 6,
    category: "beard",
    title: "胡须精细修剪",
    description: "精细线条，艺术造型",
    imageUrl: "/api/placeholder/400/300",
    beforeAfter: true
  },
  {
    id: 7,
    category: "interior",
    title: "店内环境",
    description: "舒适优雅的理发环境",
    imageUrl: "/api/placeholder/400/300",
    beforeAfter: false
  },
  {
    id: 8,
    category: "interior",
    title: "专业设备",
    description: "国际先进的理发设备",
    imageUrl: "/api/placeholder/400/300",
    beforeAfter: false
  },
  {
    id: 9,
    category: "styling",
    title: "特殊场合造型",
    description: "重要场合的精致造型",
    imageUrl: "/api/placeholder/400/300",
    beforeAfter: true
  },
  {
    id: 10,
    category: "haircuts",
    title: "青年时尚发型",
    description: "年轻活力的时尚造型",
    imageUrl: "/api/placeholder/400/300",
    beforeAfter: false
  },
  {
    id: 11,
    category: "interior",
    title: "等候区域",
    description: "舒适的客户等候空间",
    imageUrl: "/api/placeholder/400/300",
    beforeAfter: false
  },
  {
    id: 12,
    category: "styling",
    title: "婚礼造型",
    description: "新郎专属婚礼造型",
    imageUrl: "/api/placeholder/400/300",
    beforeAfter: true
  }
]

const categories = [
  { id: "all", name: "全部作品", icon: "🎨" },
  { id: "haircuts", name: "理发作品", icon: "✂️" },
  { id: "beard", name: "胡须造型", icon: "🧔" },
  { id: "styling", name: "造型设计", icon: "✨" },
  { id: "interior", name: "店内环境", icon: "🏪" }
]

export function GalleryPageContent() {
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedImage, setSelectedImage] = useState<number | null>(null)

  const filteredImages = selectedCategory === "all" 
    ? galleryImages 
    : galleryImages.filter(img => img.category === selectedCategory)

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-primary to-primary/80 text-primary-foreground">
        <FloatingElement delay={0}>
          <div className="container mx-auto px-4 text-center">
            <div className="max-w-3xl mx-auto">
              <FadeIn delay={0.2}>
                <h1 className="text-4xl md:text-5xl font-bold mb-6">
                  作品展示
                </h1>
              </FadeIn>
              <FadeIn delay={0.4}>
                <p className="text-xl mb-8 text-primary-foreground/90">
                  欣赏我们的专业作品，见证每一次完美的蜕变。从经典理发到创意造型，每一个作品都体现我们的专业技艺。
                </p>
              </FadeIn>
              <div className="flex flex-wrap justify-center gap-6 text-sm">
                <StaggeredFadeIn delay={0.6}>
                  <div className="flex items-center space-x-2">
                    <span className="text-accent text-lg">📸</span>
                    <span>真实作品</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-accent text-lg">🎯</span>
                    <span>专业技艺</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-accent text-lg">✨</span>
                    <span>完美蜕变</span>
                  </div>
                </StaggeredFadeIn>
              </div>
            </div>
          </div>
        </FloatingElement>
      </section>

      {/* Filter Section */}
      <section className="py-12 bg-muted/50">
        <div className="container mx-auto px-4">
          <FadeIn delay={0.2}>
            <div className="flex flex-wrap justify-center gap-4">
              {categories.map((category) => (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? "default" : "outline"}
                  onClick={() => setSelectedCategory(category.id)}
                  className="flex items-center space-x-2"
                >
                  <span>{category.icon}</span>
                  <span>{category.name}</span>
                </Button>
              ))}
            </div>
          </FadeIn>
        </div>
      </section>

      {/* Gallery Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <StaggeredFadeIn>
              {filteredImages.map((image) => (
                <ScaleIn key={image.id} delay={0.1}>
                  <Card
                    className="group cursor-pointer hover:shadow-lg transition-all duration-300 overflow-hidden"
                    onClick={() => setSelectedImage(image.id)}
                  >
                    <div className="relative aspect-[4/3] overflow-hidden">
                      <OptimizedImage
                        src={image.imageUrl}
                        alt={image.title}
                        fill
                        sizes={ImageSizes.gallery}
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                        loading="lazy"
                      />

                      {/* Before/After Badge */}
                      {image.beforeAfter && (
                        <div className="absolute top-3 right-3 bg-accent text-black px-2 py-1 rounded-full text-xs font-semibold">
                          前后对比
                        </div>
                      )}

                      {/* Overlay */}
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 flex items-center justify-center">
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <Button variant="secondary" size="sm">
                            查看详情
                          </Button>
                        </div>
                      </div>
                    </div>

                    <CardContent className="p-4">
                      <h3 className="font-semibold mb-1">{image.title}</h3>
                      <p className="text-sm text-muted-foreground">{image.description}</p>
                    </CardContent>
                  </Card>
                </ScaleIn>
              ))}
            </StaggeredFadeIn>
          </div>
          
          {filteredImages.length === 0 && (
            <FadeIn>
              <div className="text-center py-12">
                <div className="text-6xl mb-4">🎨</div>
                <h3 className="text-xl font-semibold mb-2">暂无作品</h3>
                <p className="text-muted-foreground">该分类下暂时没有作品，请选择其他分类查看。</p>
              </div>
            </FadeIn>
          )}
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <FadeIn>
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                作品统计
              </h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                数字见证我们的专业实力和客户满意度
              </p>
            </div>
          </FadeIn>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <StaggeredFadeIn>
              <div className="text-center">
                <div className="text-4xl mb-2">📸</div>
                <div className="text-3xl font-bold text-primary mb-1">500+</div>
                <div className="text-lg font-semibold mb-1">作品展示</div>
                <div className="text-sm text-muted-foreground">真实客户作品</div>
              </div>
              <div className="text-center">
                <div className="text-4xl mb-2">⭐</div>
                <div className="text-3xl font-bold text-primary mb-1">98%</div>
                <div className="text-lg font-semibold mb-1">满意度</div>
                <div className="text-sm text-muted-foreground">客户好评率</div>
              </div>
              <div className="text-center">
                <div className="text-4xl mb-2">🏆</div>
                <div className="text-3xl font-bold text-primary mb-1">50+</div>
                <div className="text-lg font-semibold mb-1">获奖作品</div>
                <div className="text-sm text-muted-foreground">行业认可</div>
              </div>
              <div className="text-center">
                <div className="text-4xl mb-2">📱</div>
                <div className="text-3xl font-bold text-primary mb-1">1000+</div>
                <div className="text-lg font-semibold mb-1">社交分享</div>
                <div className="text-sm text-muted-foreground">客户主动分享</div>
              </div>
            </StaggeredFadeIn>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <FloatingElement delay={0.2}>
          <div className="container mx-auto px-4 text-center">
            <div className="max-w-3xl mx-auto">
              <FadeIn delay={0.2}>
                <h2 className="text-3xl md:text-4xl font-bold mb-4">
                  想要同样的效果？
                </h2>
              </FadeIn>
              <FadeIn delay={0.4}>
                <p className="text-xl mb-8 text-primary-foreground/90">
                  立即预约，让我们的专业理发师为您打造专属造型
                </p>
              </FadeIn>
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <StaggeredFadeIn delay={0.6}>
                  <Button asChild size="lg" className="bg-accent hover:bg-accent/90 text-black font-semibold px-8 py-3 text-lg">
                    <a href="/booking">立即预约</a>
                  </Button>
                  <Button asChild variant="outline" size="lg" className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary px-8 py-3 text-lg">
                    <a href="/services">查看服务</a>
                  </Button>
                </StaggeredFadeIn>
              </div>
            </div>
          </div>
        </FloatingElement>
      </section>

      {/* Image Modal (Simple version) */}
      {selectedImage && (
        <div 
          className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4"
          onClick={() => setSelectedImage(null)}
        >
          <div className="bg-background rounded-lg p-6 max-w-2xl w-full max-h-[90vh] overflow-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-semibold">
                {galleryImages.find(img => img.id === selectedImage)?.title}
              </h3>
              <Button variant="ghost" size="sm" onClick={() => setSelectedImage(null)}>
                ✕
              </Button>
            </div>
            <div className="aspect-[4/3] bg-muted rounded-lg flex items-center justify-center mb-4">
              <OptimizedImage
                src={galleryImages.find(img => img.id === selectedImage)?.imageUrl || ""}
                alt={galleryImages.find(img => img.id === selectedImage)?.title || ""}
                fill
                className="object-cover rounded-lg"
              />
            </div>
            <p className="text-muted-foreground">
              {galleryImages.find(img => img.id === selectedImage)?.description}
            </p>
          </div>
        </div>
      )}
    </div>
  )
}
