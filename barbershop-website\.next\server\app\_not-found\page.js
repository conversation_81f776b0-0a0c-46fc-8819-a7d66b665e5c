/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Czhaosihao%5CDesktop%5Cpython_study%5Ctony_project%5Cbarbershop-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Czhaosihao%5CDesktop%5Cpython_study%5Ctony_project%5Cbarbershop-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Czhaosihao%5CDesktop%5Cpython_study%5Ctony_project%5Cbarbershop-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Czhaosihao%5CDesktop%5Cpython_study%5Ctony_project%5Cbarbershop-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Czhaosihao%5CDesktop%5Cpython_study%5Ctony_project%5Cbarbershop-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Czhaosihao%5CDesktop%5Cpython_study%5Ctony_project%5Cbarbershop-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3poYW9zaWhhbyU1QyU1Q0Rlc2t0b3AlNUMlNUNweXRob25fc3R1ZHklNUMlNUN0b255X3Byb2plY3QlNUMlNUNiYXJiZXJzaG9wLXdlYnNpdGUlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUN6aGFvc2loYW8lNUMlNUNEZXNrdG9wJTVDJTVDcHl0aG9uX3N0dWR5JTVDJTVDdG9ueV9wcm9qZWN0JTVDJTVDYmFyYmVyc2hvcC13ZWJzaXRlJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDemhhb3NpaGFvJTVDJTVDRGVza3RvcCU1QyU1Q3B5dGhvbl9zdHVkeSU1QyU1Q3RvbnlfcHJvamVjdCU1QyU1Q2JhcmJlcnNob3Atd2Vic2l0ZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3poYW9zaWhhbyU1QyU1Q0Rlc2t0b3AlNUMlNUNweXRob25fc3R1ZHklNUMlNUN0b255X3Byb2plY3QlNUMlNUNiYXJiZXJzaG9wLXdlYnNpdGUlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNodHRwLWFjY2Vzcy1mYWxsYmFjayU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3poYW9zaWhhbyU1QyU1Q0Rlc2t0b3AlNUMlNUNweXRob25fc3R1ZHklNUMlNUN0b255X3Byb2plY3QlNUMlNUNiYXJiZXJzaG9wLXdlYnNpdGUlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3poYW9zaWhhbyU1QyU1Q0Rlc2t0b3AlNUMlNUNweXRob25fc3R1ZHklNUMlNUN0b255X3Byb2plY3QlNUMlNUNiYXJiZXJzaG9wLXdlYnNpdGUlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3poYW9zaWhhbyU1QyU1Q0Rlc2t0b3AlNUMlNUNweXRob25fc3R1ZHklNUMlNUN0b255X3Byb2plY3QlNUMlNUNiYXJiZXJzaG9wLXdlYnNpdGUlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q21ldGFkYXRhLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3poYW9zaWhhbyU1QyU1Q0Rlc2t0b3AlNUMlNUNweXRob25fc3R1ZHklNUMlNUN0b255X3Byb2plY3QlNUMlNUNiYXJiZXJzaG9wLXdlYnNpdGUlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvT0FBZ0w7QUFDaEw7QUFDQSwwT0FBbUw7QUFDbkw7QUFDQSwwT0FBbUw7QUFDbkw7QUFDQSxvUkFBeU07QUFDek07QUFDQSx3T0FBa0w7QUFDbEw7QUFDQSw0UEFBNkw7QUFDN0w7QUFDQSxrUUFBZ007QUFDaE07QUFDQSxzUUFBaU0iLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHpoYW9zaWhhb1xcXFxEZXNrdG9wXFxcXHB5dGhvbl9zdHVkeVxcXFx0b255X3Byb2plY3RcXFxcYmFyYmVyc2hvcC13ZWJzaXRlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXBhZ2UuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHpoYW9zaWhhb1xcXFxEZXNrdG9wXFxcXHB5dGhvbl9zdHVkeVxcXFx0b255X3Byb2plY3RcXFxcYmFyYmVyc2hvcC13ZWJzaXRlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXNlZ21lbnQuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHpoYW9zaWhhb1xcXFxEZXNrdG9wXFxcXHB5dGhvbl9zdHVkeVxcXFx0b255X3Byb2plY3RcXFxcYmFyYmVyc2hvcC13ZWJzaXRlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHpoYW9zaWhhb1xcXFxEZXNrdG9wXFxcXHB5dGhvbl9zdHVkeVxcXFx0b255X3Byb2plY3RcXFxcYmFyYmVyc2hvcC13ZWJzaXRlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcaHR0cC1hY2Nlc3MtZmFsbGJhY2tcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHpoYW9zaWhhb1xcXFxEZXNrdG9wXFxcXHB5dGhvbl9zdHVkeVxcXFx0b255X3Byb2plY3RcXFxcYmFyYmVyc2hvcC13ZWJzaXRlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0LXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcemhhb3NpaGFvXFxcXERlc2t0b3BcXFxccHl0aG9uX3N0dWR5XFxcXHRvbnlfcHJvamVjdFxcXFxiYXJiZXJzaG9wLXdlYnNpdGVcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxhc3luYy1tZXRhZGF0YS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcemhhb3NpaGFvXFxcXERlc2t0b3BcXFxccHl0aG9uX3N0dWR5XFxcXHRvbnlfcHJvamVjdFxcXFxiYXJiZXJzaG9wLXdlYnNpdGVcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxtZXRhZGF0YS1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcemhhb3NpaGFvXFxcXERlc2t0b3BcXFxccHl0aG9uX3N0dWR5XFxcXHRvbnlfcHJvamVjdFxcXFxiYXJiZXJzaG9wLXdlYnNpdGVcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Caccessibility%5C%5Cskip-links.tsx%22%2C%22ids%22%3A%5B%22SkipLinks%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5Cweb-vitals.tsx%22%2C%22ids%22%3A%5B%22PerformanceMonitor%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Caccessibility%5C%5Cskip-links.tsx%22%2C%22ids%22%3A%5B%22SkipLinks%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5Cweb-vitals.tsx%22%2C%22ids%22%3A%5B%22PerformanceMonitor%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(rsc)/./node_modules/next/dist/client/script.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/accessibility/skip-links.tsx */ \"(rsc)/./src/components/accessibility/skip-links.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/performance/web-vitals.tsx */ \"(rsc)/./src/components/performance/web-vitals.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Caccessibility%5C%5Cskip-links.tsx%22%2C%22ids%22%3A%5B%22SkipLinks%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5Cweb-vitals.tsx%22%2C%22ids%22%3A%5B%22PerformanceMonitor%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"4d4854800f1b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHpoYW9zaWhhb1xcRGVza3RvcFxccHl0aG9uX3N0dWR5XFx0b255X3Byb2plY3RcXGJhcmJlcnNob3Atd2Vic2l0ZVxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNGQ0ODU0ODAwZjFiXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_geist_sans_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_geist_sans_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_geist_sans_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_variable_font_geist_mono_subsets_latin_display_swap_variableName_playfair___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Playfair_Display\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"playfair\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Playfair_Display\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"playfair\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_variable_font_geist_mono_subsets_latin_display_swap_variableName_playfair___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_variable_font_geist_mono_subsets_latin_display_swap_variableName_playfair___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_accessibility_skip_links__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/accessibility/skip-links */ \"(rsc)/./src/components/accessibility/skip-links.tsx\");\n/* harmony import */ var _components_seo_structured_data__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/seo/structured-data */ \"(rsc)/./src/components/seo/structured-data.tsx\");\n/* harmony import */ var _components_performance_web_vitals__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/performance/web-vitals */ \"(rsc)/./src/components/performance/web-vitals.tsx\");\n/* harmony import */ var _components_seo_meta_tags__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/seo/meta-tags */ \"(rsc)/./src/components/seo/meta-tags.tsx\");\n\n\n\n\n\n\n\n\nconst metadata = (0,_components_seo_meta_tags__WEBPACK_IMPORTED_MODULE_5__.generateMetadata)({\n    title: \"Tony's Barbershop - 专业理发店\",\n    description: \"Tony's Barbershop - 专业理发店，提供传统理发、现代造型、胡须修剪等服务。技术精湛，服务周到，环境舒适。\",\n    keywords: [\n        \"理发店\",\n        \"专业理发\",\n        \"上海理发店\",\n        \"Tony's Barbershop\",\n        \"理发师\",\n        \"造型设计\",\n        \"胡须修剪\"\n    ],\n    url: \"/\",\n    type: \"website\"\n});\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        className: \"scroll-smooth\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/favicon-32x32.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/favicon-16x16.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/site.webmanifest\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#000000\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//fonts.gstatic.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_geist_sans_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_variable_font_geist_mono_subsets_latin_display_swap_variableName_playfair___WEBPACK_IMPORTED_MODULE_7___default().variable)} antialiased min-h-screen bg-background text-foreground`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_accessibility_skip_links__WEBPACK_IMPORTED_MODULE_2__.SkipLinks, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex min-h-screen flex-col\",\n                        id: \"root\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            id: \"main-content\",\n                            className: \"flex-1\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seo_structured_data__WEBPACK_IMPORTED_MODULE_3__.BusinessStructuredData, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_performance_web_vitals__WEBPACK_IMPORTED_MODULE_4__.PerformanceMonitor, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/accessibility/skip-links.tsx":
/*!*****************************************************!*\
  !*** ./src/components/accessibility/skip-links.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AccessibleButton: () => (/* binding */ AccessibleButton),
/* harmony export */   AccessibleInput: () => (/* binding */ AccessibleInput),
/* harmony export */   AccessibleLabel: () => (/* binding */ AccessibleLabel),
/* harmony export */   AccessibleLink: () => (/* binding */ AccessibleLink),
/* harmony export */   ScreenReaderOnly: () => (/* binding */ ScreenReaderOnly),
/* harmony export */   SkipLink: () => (/* binding */ SkipLink),
/* harmony export */   SkipLinks: () => (/* binding */ SkipLinks),
/* harmony export */   useFocusManagement: () => (/* binding */ useFocusManagement)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const SkipLink = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SkipLink() from the server but SkipLink is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\accessibility\\skip-links.tsx",
"SkipLink",
);const SkipLinks = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SkipLinks() from the server but SkipLinks is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\accessibility\\skip-links.tsx",
"SkipLinks",
);const ScreenReaderOnly = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ScreenReaderOnly() from the server but ScreenReaderOnly is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\accessibility\\skip-links.tsx",
"ScreenReaderOnly",
);const AccessibleButton = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AccessibleButton() from the server but AccessibleButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\accessibility\\skip-links.tsx",
"AccessibleButton",
);const AccessibleLink = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AccessibleLink() from the server but AccessibleLink is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\accessibility\\skip-links.tsx",
"AccessibleLink",
);const AccessibleLabel = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AccessibleLabel() from the server but AccessibleLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\accessibility\\skip-links.tsx",
"AccessibleLabel",
);const AccessibleInput = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AccessibleInput() from the server but AccessibleInput is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\accessibility\\skip-links.tsx",
"AccessibleInput",
);const useFocusManagement = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useFocusManagement() from the server but useFocusManagement is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\accessibility\\skip-links.tsx",
"useFocusManagement",
);

/***/ }),

/***/ "(rsc)/./src/components/performance/web-vitals.tsx":
/*!***************************************************!*\
  !*** ./src/components/performance/web-vitals.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   PerformanceMonitor: () => (/* binding */ PerformanceMonitor),
/* harmony export */   WebVitals: () => (/* binding */ WebVitals),
/* harmony export */   useErrorMonitoring: () => (/* binding */ useErrorMonitoring),
/* harmony export */   useImageLoadingMonitoring: () => (/* binding */ useImageLoadingMonitoring),
/* harmony export */   usePerformanceMonitoring: () => (/* binding */ usePerformanceMonitoring),
/* harmony export */   useUserExperienceMonitoring: () => (/* binding */ useUserExperienceMonitoring)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const WebVitals = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call WebVitals() from the server but WebVitals is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\performance\\web-vitals.tsx",
"WebVitals",
);const usePerformanceMonitoring = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call usePerformanceMonitoring() from the server but usePerformanceMonitoring is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\performance\\web-vitals.tsx",
"usePerformanceMonitoring",
);const useImageLoadingMonitoring = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useImageLoadingMonitoring() from the server but useImageLoadingMonitoring is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\performance\\web-vitals.tsx",
"useImageLoadingMonitoring",
);const useErrorMonitoring = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useErrorMonitoring() from the server but useErrorMonitoring is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\performance\\web-vitals.tsx",
"useErrorMonitoring",
);const useUserExperienceMonitoring = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useUserExperienceMonitoring() from the server but useUserExperienceMonitoring is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\performance\\web-vitals.tsx",
"useUserExperienceMonitoring",
);const PerformanceMonitor = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call PerformanceMonitor() from the server but PerformanceMonitor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\performance\\web-vitals.tsx",
"PerformanceMonitor",
);

/***/ }),

/***/ "(rsc)/./src/components/seo/meta-tags.tsx":
/*!******************************************!*\
  !*** ./src/components/seo/meta-tags.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateJSONLD: () => (/* binding */ generateJSONLD),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata),\n/* harmony export */   pageSEO: () => (/* binding */ pageSEO)\n/* harmony export */ });\n// 基础SEO配置\nconst defaultSEO = {\n    siteName: \"Tony's Barbershop\",\n    locale: 'zh_CN',\n    baseUrl: 'https://tonys-barbershop.com',\n    defaultImage: '/images/og-image.jpg',\n    defaultDescription: '专业理发店，提供传统理发、现代造型、胡须修剪等服务。技术精湛，服务周到，环境舒适。',\n    defaultKeywords: [\n        '理发店',\n        '理发',\n        '造型',\n        '胡须修剪',\n        '专业理发师',\n        '上海理发店',\n        'Tony\\'s Barbershop'\n    ]\n};\n// 生成页面元数据\nfunction generateMetadata({ title, description = defaultSEO.defaultDescription, keywords = defaultSEO.defaultKeywords, image = defaultSEO.defaultImage, url, type = 'website', locale = defaultSEO.locale, siteName = defaultSEO.siteName } = {}) {\n    const fullTitle = title ? `${title} | ${siteName}` : siteName;\n    const fullUrl = url ? `${defaultSEO.baseUrl}${url}` : defaultSEO.baseUrl;\n    const fullImage = image.startsWith('http') ? image : `${defaultSEO.baseUrl}${image}`;\n    return {\n        title: fullTitle,\n        description,\n        keywords: keywords.join(', '),\n        // Open Graph\n        openGraph: {\n            title: fullTitle,\n            description,\n            url: fullUrl,\n            siteName,\n            images: [\n                {\n                    url: fullImage,\n                    width: 1200,\n                    height: 630,\n                    alt: title || siteName\n                }\n            ],\n            locale,\n            type\n        },\n        // Twitter Card\n        twitter: {\n            card: 'summary_large_image',\n            title: fullTitle,\n            description,\n            images: [\n                fullImage\n            ],\n            creator: '@tonys_barbershop',\n            site: '@tonys_barbershop'\n        },\n        // 其他元标签\n        robots: {\n            index: true,\n            follow: true,\n            googleBot: {\n                index: true,\n                follow: true,\n                'max-video-preview': -1,\n                'max-image-preview': 'large',\n                'max-snippet': -1\n            }\n        },\n        // 规范链接\n        alternates: {\n            canonical: fullUrl,\n            languages: {\n                'zh-CN': fullUrl,\n                'en': `${fullUrl}/en`\n            }\n        },\n        // 应用信息\n        applicationName: siteName,\n        generator: 'Next.js',\n        referrer: 'origin-when-cross-origin',\n        // 作者和版权\n        authors: [\n            {\n                name: 'Tony\\'s Barbershop'\n            }\n        ],\n        creator: 'Tony\\'s Barbershop',\n        publisher: 'Tony\\'s Barbershop',\n        // 格式检测\n        formatDetection: {\n            email: false,\n            address: false,\n            telephone: false\n        },\n        // 验证标签\n        verification: {\n            google: 'google-site-verification-code',\n            yandex: 'yandex-verification-code',\n            yahoo: 'yahoo-site-verification-code'\n        },\n        // 其他\n        category: 'business'\n    };\n}\n// 页面特定的SEO配置\nconst pageSEO = {\n    home: {\n        title: '首页',\n        description: 'Tony\\'s Barbershop - 专业理发店，提供传统理发、现代造型、胡须修剪等服务。技术精湛，服务周到，环境舒适。',\n        keywords: [\n            '理发店',\n            '专业理发',\n            '上海理发店',\n            'Tony\\'s Barbershop',\n            '理发师',\n            '造型设计'\n        ],\n        url: '/'\n    },\n    services: {\n        title: '服务项目',\n        description: '查看我们的专业理发服务：经典理发、时尚造型、胡须修剪、洗剪吹等。价格透明，技术精湛。',\n        keywords: [\n            '理发服务',\n            '理发价格',\n            '造型设计',\n            '胡须修剪',\n            '洗剪吹',\n            '专业理发'\n        ],\n        url: '/services'\n    },\n    about: {\n        title: '关于我们',\n        description: '了解Tony\\'s Barbershop的历史、理念和专业团队。我们致力于为每位客户提供最优质的理发服务。',\n        keywords: [\n            '理发店历史',\n            '专业团队',\n            '理发师介绍',\n            '服务理念',\n            'Tony\\'s Barbershop'\n        ],\n        url: '/about'\n    },\n    gallery: {\n        title: '作品展示',\n        description: '欣赏我们的理发作品集，包括各种发型设计、胡须造型等。展现我们的专业技艺和创意。',\n        keywords: [\n            '理发作品',\n            '发型设计',\n            '胡须造型',\n            '理发案例',\n            '造型展示'\n        ],\n        url: '/gallery'\n    },\n    contact: {\n        title: '联系我们',\n        description: '联系Tony\\'s Barbershop预约服务。地址：上海南京路123号，电话：138-0000-0000。',\n        keywords: [\n            '理发店联系方式',\n            '预约理发',\n            '理发店地址',\n            '营业时间',\n            '联系电话'\n        ],\n        url: '/contact'\n    },\n    booking: {\n        title: '在线预约',\n        description: '在线预约Tony\\'s Barbershop的理发服务。选择服务项目、理发师和时间，享受便捷的预约体验。',\n        keywords: [\n            '在线预约',\n            '理发预约',\n            '预约系统',\n            '理发师预约',\n            '服务预约'\n        ],\n        url: '/booking'\n    }\n};\n// JSON-LD 结构化数据生成器\nfunction generateJSONLD(type, data) {\n    return {\n        __html: JSON.stringify({\n            '@context': 'https://schema.org',\n            '@type': type,\n            ...data\n        })\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/seo/meta-tags.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/seo/structured-data.tsx":
/*!************************************************!*\
  !*** ./src/components/seo/structured-data.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BreadcrumbStructuredData: () => (/* binding */ BreadcrumbStructuredData),\n/* harmony export */   BusinessStructuredData: () => (/* binding */ BusinessStructuredData),\n/* harmony export */   FAQStructuredData: () => (/* binding */ FAQStructuredData),\n/* harmony export */   ServicesStructuredData: () => (/* binding */ ServicesStructuredData)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/script */ \"(rsc)/./node_modules/next/dist/api/script.js\");\n\n\n// 理发店基本信息的结构化数据\nfunction BusinessStructuredData() {\n    const businessData = {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"HairSalon\",\n        \"name\": \"Tony's Barbershop\",\n        \"description\": \"专业理发店，提供传统理发、现代造型、胡须修剪等服务\",\n        \"url\": \"https://tonys-barbershop.com\",\n        \"telephone\": \"+86-138-0000-0000\",\n        \"email\": \"<EMAIL>\",\n        \"address\": {\n            \"@type\": \"PostalAddress\",\n            \"streetAddress\": \"南京路123号\",\n            \"addressLocality\": \"上海\",\n            \"addressRegion\": \"上海市\",\n            \"postalCode\": \"200001\",\n            \"addressCountry\": \"CN\"\n        },\n        \"geo\": {\n            \"@type\": \"GeoCoordinates\",\n            \"latitude\": 31.2304,\n            \"longitude\": 121.4737\n        },\n        \"openingHours\": [\n            \"Mo-Fr 09:00-20:00\",\n            \"Sa-Su 10:00-18:00\"\n        ],\n        \"priceRange\": \"¥¥\",\n        \"paymentAccepted\": [\n            \"现金\",\n            \"支付宝\",\n            \"微信支付\",\n            \"银行卡\"\n        ],\n        \"currenciesAccepted\": \"CNY\",\n        \"hasMap\": \"https://maps.google.com/?q=31.2304,121.4737\",\n        \"image\": [\n            \"https://tonys-barbershop.com/images/storefront.jpg\",\n            \"https://tonys-barbershop.com/images/interior.jpg\"\n        ],\n        \"logo\": \"https://tonys-barbershop.com/images/logo.png\",\n        \"sameAs\": [\n            \"https://www.facebook.com/tonys-barbershop\",\n            \"https://www.instagram.com/tonys-barbershop\",\n            \"https://weibo.com/tonys-barbershop\"\n        ],\n        \"aggregateRating\": {\n            \"@type\": \"AggregateRating\",\n            \"ratingValue\": \"4.8\",\n            \"reviewCount\": \"127\",\n            \"bestRating\": \"5\",\n            \"worstRating\": \"1\"\n        },\n        \"review\": [\n            {\n                \"@type\": \"Review\",\n                \"author\": {\n                    \"@type\": \"Person\",\n                    \"name\": \"张先生\"\n                },\n                \"reviewRating\": {\n                    \"@type\": \"Rating\",\n                    \"ratingValue\": \"5\",\n                    \"bestRating\": \"5\"\n                },\n                \"reviewBody\": \"技术精湛，服务周到，环境舒适。强烈推荐！\"\n            }\n        ]\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        id: \"business-structured-data\",\n        type: \"application/ld+json\",\n        dangerouslySetInnerHTML: {\n            __html: JSON.stringify(businessData)\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\seo\\\\structured-data.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, this);\n}\n// 服务页面的结构化数据\nfunction ServicesStructuredData() {\n    const servicesData = {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"Service\",\n        \"serviceType\": \"理发服务\",\n        \"provider\": {\n            \"@type\": \"HairSalon\",\n            \"name\": \"Tony's Barbershop\"\n        },\n        \"hasOfferCatalog\": {\n            \"@type\": \"OfferCatalog\",\n            \"name\": \"理发服务目录\",\n            \"itemListElement\": [\n                {\n                    \"@type\": \"Offer\",\n                    \"itemOffered\": {\n                        \"@type\": \"Service\",\n                        \"name\": \"经典理发\",\n                        \"description\": \"传统理发技艺，适合商务人士\"\n                    },\n                    \"price\": \"80\",\n                    \"priceCurrency\": \"CNY\"\n                },\n                {\n                    \"@type\": \"Offer\",\n                    \"itemOffered\": {\n                        \"@type\": \"Service\",\n                        \"name\": \"时尚造型\",\n                        \"description\": \"现代时尚发型设计\"\n                    },\n                    \"price\": \"120\",\n                    \"priceCurrency\": \"CNY\"\n                },\n                {\n                    \"@type\": \"Offer\",\n                    \"itemOffered\": {\n                        \"@type\": \"Service\",\n                        \"name\": \"胡须修剪\",\n                        \"description\": \"专业胡须造型设计\"\n                    },\n                    \"price\": \"60\",\n                    \"priceCurrency\": \"CNY\"\n                }\n            ]\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        id: \"services-structured-data\",\n        type: \"application/ld+json\",\n        dangerouslySetInnerHTML: {\n            __html: JSON.stringify(servicesData)\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\seo\\\\structured-data.tsx\",\n        lineNumber: 128,\n        columnNumber: 5\n    }, this);\n}\n// 面包屑导航结构化数据\nfunction BreadcrumbStructuredData({ items }) {\n    const breadcrumbData = {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"BreadcrumbList\",\n        \"itemListElement\": items.map((item, index)=>({\n                \"@type\": \"ListItem\",\n                \"position\": index + 1,\n                \"name\": item.name,\n                \"item\": item.url\n            }))\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        id: \"breadcrumb-structured-data\",\n        type: \"application/ld+json\",\n        dangerouslySetInnerHTML: {\n            __html: JSON.stringify(breadcrumbData)\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\seo\\\\structured-data.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n// 常见问题结构化数据\nfunction FAQStructuredData() {\n    const faqData = {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"FAQPage\",\n        \"mainEntity\": [\n            {\n                \"@type\": \"Question\",\n                \"name\": \"需要预约吗？\",\n                \"acceptedAnswer\": {\n                    \"@type\": \"Answer\",\n                    \"text\": \"建议提前预约以确保服务时间，我们也接受现场排队。\"\n                }\n            },\n            {\n                \"@type\": \"Question\",\n                \"name\": \"营业时间是什么？\",\n                \"acceptedAnswer\": {\n                    \"@type\": \"Answer\",\n                    \"text\": \"周一至周五：9:00-20:00，周六至周日：10:00-18:00\"\n                }\n            },\n            {\n                \"@type\": \"Question\",\n                \"name\": \"支持哪些支付方式？\",\n                \"acceptedAnswer\": {\n                    \"@type\": \"Answer\",\n                    \"text\": \"我们支持现金、支付宝、微信支付和银行卡支付。\"\n                }\n            }\n        ]\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        id: \"faq-structured-data\",\n        type: \"application/ld+json\",\n        dangerouslySetInnerHTML: {\n            __html: JSON.stringify(faqData)\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\seo\\\\structured-data.tsx\",\n        lineNumber: 196,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/seo/structured-data.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Caccessibility%5C%5Cskip-links.tsx%22%2C%22ids%22%3A%5B%22SkipLinks%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5Cweb-vitals.tsx%22%2C%22ids%22%3A%5B%22PerformanceMonitor%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Caccessibility%5C%5Cskip-links.tsx%22%2C%22ids%22%3A%5B%22SkipLinks%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5Cweb-vitals.tsx%22%2C%22ids%22%3A%5B%22PerformanceMonitor%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(ssr)/./node_modules/next/dist/client/script.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/accessibility/skip-links.tsx */ \"(ssr)/./src/components/accessibility/skip-links.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/performance/web-vitals.tsx */ \"(ssr)/./src/components/performance/web-vitals.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Caccessibility%5C%5Cskip-links.tsx%22%2C%22ids%22%3A%5B%22SkipLinks%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5Cweb-vitals.tsx%22%2C%22ids%22%3A%5B%22PerformanceMonitor%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/accessibility/skip-links.tsx":
/*!*****************************************************!*\
  !*** ./src/components/accessibility/skip-links.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AccessibleButton: () => (/* binding */ AccessibleButton),\n/* harmony export */   AccessibleInput: () => (/* binding */ AccessibleInput),\n/* harmony export */   AccessibleLabel: () => (/* binding */ AccessibleLabel),\n/* harmony export */   AccessibleLink: () => (/* binding */ AccessibleLink),\n/* harmony export */   ScreenReaderOnly: () => (/* binding */ ScreenReaderOnly),\n/* harmony export */   SkipLink: () => (/* binding */ SkipLink),\n/* harmony export */   SkipLinks: () => (/* binding */ SkipLinks),\n/* harmony export */   useFocusManagement: () => (/* binding */ useFocusManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ SkipLink,SkipLinks,ScreenReaderOnly,AccessibleButton,AccessibleLink,AccessibleLabel,AccessibleInput,useFocusManagement auto */ \n\nfunction SkipLink({ href, children, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        href: href,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4\", \"bg-black text-white px-4 py-2 rounded-md z-50\", \"focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2\", \"transition-all duration-200\", className),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\nfunction SkipLinks() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"sr-only focus-within:not-sr-only\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkipLink, {\n                href: \"#main-content\",\n                children: \"跳转到主要内容\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkipLink, {\n                href: \"#navigation\",\n                children: \"跳转到导航菜单\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkipLink, {\n                href: \"#footer\",\n                children: \"跳转到页脚\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n// 屏幕阅读器专用文本组件\nfunction ScreenReaderOnly({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"sr-only\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n        lineNumber: 40,\n        columnNumber: 10\n    }, this);\n}\nfunction AccessibleButton({ children, ariaLabel, ariaDescribedBy, isLoading = false, className, disabled, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        \"aria-label\": ariaLabel,\n        \"aria-describedby\": ariaDescribedBy,\n        \"aria-disabled\": disabled || isLoading,\n        disabled: disabled || isLoading,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\", \"transition-all duration-200\", className),\n        ...props,\n        children: [\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"sr-only\",\n                children: \"正在加载...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n                lineNumber: 74,\n                columnNumber: 9\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, this);\n}\nfunction AccessibleLink({ children, external = false, ariaLabel, className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        \"aria-label\": ariaLabel,\n        target: external ? '_blank' : undefined,\n        rel: external ? 'noopener noreferrer' : undefined,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\", \"transition-all duration-200\", className),\n        ...props,\n        children: [\n            children,\n            external && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScreenReaderOnly, {\n                children: \"（在新窗口中打开）\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n                lineNumber: 109,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\nfunction AccessibleLabel({ children, required = false, className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"block text-sm font-medium text-gray-700\", className),\n        ...props,\n        children: [\n            children,\n            required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-red-500 ml-1\",\n                        \"aria-hidden\": \"true\",\n                        children: \"*\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScreenReaderOnly, {\n                        children: \"（必填）\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n        lineNumber: 128,\n        columnNumber: 5\n    }, this);\n}\nfunction AccessibleInput({ label, error, helperText, required = false, className, id, ...props }) {\n    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;\n    const errorId = error ? `${inputId}-error` : undefined;\n    const helperId = helperText ? `${inputId}-helper` : undefined;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-1\",\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AccessibleLabel, {\n                htmlFor: inputId,\n                required: required,\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n                lineNumber: 170,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                id: inputId,\n                \"aria-invalid\": error ? 'true' : 'false',\n                \"aria-describedby\": [\n                    errorId,\n                    helperId\n                ].filter(Boolean).join(' ') || undefined,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"w-full px-3 py-2 border border-gray-300 rounded-md\", \"focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent\", \"transition-all duration-200\", error && \"border-red-500\", className),\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, this),\n            helperText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                id: helperId,\n                className: \"text-sm text-gray-600\",\n                children: helperText\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n                lineNumber: 188,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                id: errorId,\n                className: \"text-sm text-red-600\",\n                role: \"alert\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n                lineNumber: 193,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n        lineNumber: 168,\n        columnNumber: 5\n    }, this);\n}\n// 焦点管理 Hook\nfunction useFocusManagement() {\n    const focusElement = (selector)=>{\n        const element = document.querySelector(selector);\n        if (element) {\n            element.focus();\n        }\n    };\n    const trapFocus = (container)=>{\n        const focusableElements = container.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])');\n        const firstElement = focusableElements[0];\n        const lastElement = focusableElements[focusableElements.length - 1];\n        const handleTabKey = (e)=>{\n            if (e.key === 'Tab') {\n                if (e.shiftKey) {\n                    if (document.activeElement === firstElement) {\n                        lastElement.focus();\n                        e.preventDefault();\n                    }\n                } else {\n                    if (document.activeElement === lastElement) {\n                        firstElement.focus();\n                        e.preventDefault();\n                    }\n                }\n            }\n        };\n        container.addEventListener('keydown', handleTabKey);\n        return ()=>container.removeEventListener('keydown', handleTabKey);\n    };\n    return {\n        focusElement,\n        trapFocus\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/accessibility/skip-links.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/performance/web-vitals.tsx":
/*!***************************************************!*\
  !*** ./src/components/performance/web-vitals.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PerformanceMonitor: () => (/* binding */ PerformanceMonitor),\n/* harmony export */   WebVitals: () => (/* binding */ WebVitals),\n/* harmony export */   useErrorMonitoring: () => (/* binding */ useErrorMonitoring),\n/* harmony export */   useImageLoadingMonitoring: () => (/* binding */ useImageLoadingMonitoring),\n/* harmony export */   usePerformanceMonitoring: () => (/* binding */ usePerformanceMonitoring),\n/* harmony export */   useUserExperienceMonitoring: () => (/* binding */ useUserExperienceMonitoring)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var web_vitals__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! web-vitals */ \"(ssr)/./node_modules/web-vitals/dist/web-vitals.js\");\n/* __next_internal_client_entry_do_not_use__ WebVitals,usePerformanceMonitoring,useImageLoadingMonitoring,useErrorMonitoring,useUserExperienceMonitoring,PerformanceMonitor auto */ \n\n\n// 发送指标到分析服务\nfunction sendToAnalytics(metric) {\n    // 这里可以发送到 Google Analytics, Vercel Analytics 等\n    console.log('Web Vitals:', metric);\n    // 示例：发送到 Google Analytics\n    if (false) {}\n}\n// Web Vitals 监控组件\nfunction WebVitals() {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WebVitals.useEffect\": ()=>{\n            // 累积布局偏移 (Cumulative Layout Shift)\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_2__.onCLS)(sendToAnalytics);\n            // 交互到下次绘制 (Interaction to Next Paint)\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_2__.onINP)(sendToAnalytics);\n            // 首次内容绘制 (First Contentful Paint)\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_2__.onFCP)(sendToAnalytics);\n            // 最大内容绘制 (Largest Contentful Paint)\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_2__.onLCP)(sendToAnalytics);\n            // 首字节时间 (Time to First Byte)\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_2__.onTTFB)(sendToAnalytics);\n        }\n    }[\"WebVitals.useEffect\"], []);\n    return null;\n}\n// 性能监控 Hook\nfunction usePerformanceMonitoring() {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"usePerformanceMonitoring.useEffect\": ()=>{\n            // 监控页面加载性能\n            const observer = new PerformanceObserver({\n                \"usePerformanceMonitoring.useEffect\": (list)=>{\n                    for (const entry of list.getEntries()){\n                        if (entry.entryType === 'navigation') {\n                            const navEntry = entry;\n                            console.log('Navigation Timing:', {\n                                domContentLoaded: navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart,\n                                loadComplete: navEntry.loadEventEnd - navEntry.loadEventStart,\n                                firstByte: navEntry.responseStart - navEntry.requestStart,\n                                domInteractive: navEntry.domInteractive - navEntry.fetchStart\n                            });\n                        }\n                        if (entry.entryType === 'resource') {\n                            const resourceEntry = entry;\n                            // 监控慢资源\n                            if (resourceEntry.duration > 1000) {\n                                console.warn('Slow resource:', {\n                                    name: resourceEntry.name,\n                                    duration: resourceEntry.duration,\n                                    size: resourceEntry.transferSize\n                                });\n                            }\n                        }\n                    }\n                }\n            }[\"usePerformanceMonitoring.useEffect\"]);\n            observer.observe({\n                entryTypes: [\n                    'navigation',\n                    'resource'\n                ]\n            });\n            return ({\n                \"usePerformanceMonitoring.useEffect\": ()=>observer.disconnect()\n            })[\"usePerformanceMonitoring.useEffect\"];\n        }\n    }[\"usePerformanceMonitoring.useEffect\"], []);\n    // 监控内存使用\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"usePerformanceMonitoring.useEffect\": ()=>{\n            const checkMemory = {\n                \"usePerformanceMonitoring.useEffect.checkMemory\": ()=>{\n                    if ('memory' in performance) {\n                        const memory = performance.memory;\n                        console.log('Memory Usage:', {\n                            used: Math.round(memory.usedJSHeapSize / 1048576) + ' MB',\n                            total: Math.round(memory.totalJSHeapSize / 1048576) + ' MB',\n                            limit: Math.round(memory.jsHeapSizeLimit / 1048576) + ' MB'\n                        });\n                    }\n                }\n            }[\"usePerformanceMonitoring.useEffect.checkMemory\"];\n            const interval = setInterval(checkMemory, 30000) // 每30秒检查一次\n            ;\n            return ({\n                \"usePerformanceMonitoring.useEffect\": ()=>clearInterval(interval)\n            })[\"usePerformanceMonitoring.useEffect\"];\n        }\n    }[\"usePerformanceMonitoring.useEffect\"], []);\n}\n// 图片懒加载监控\nfunction useImageLoadingMonitoring() {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useImageLoadingMonitoring.useEffect\": ()=>{\n            const images = document.querySelectorAll('img[loading=\"lazy\"]');\n            const observer = new IntersectionObserver({\n                \"useImageLoadingMonitoring.useEffect\": (entries)=>{\n                    entries.forEach({\n                        \"useImageLoadingMonitoring.useEffect\": (entry)=>{\n                            if (entry.isIntersecting) {\n                                const img = entry.target;\n                                const startTime = performance.now();\n                                img.addEventListener('load', {\n                                    \"useImageLoadingMonitoring.useEffect\": ()=>{\n                                        const loadTime = performance.now() - startTime;\n                                        console.log('Image loaded:', {\n                                            src: img.src,\n                                            loadTime: Math.round(loadTime),\n                                            naturalWidth: img.naturalWidth,\n                                            naturalHeight: img.naturalHeight\n                                        });\n                                    }\n                                }[\"useImageLoadingMonitoring.useEffect\"]);\n                                observer.unobserve(img);\n                            }\n                        }\n                    }[\"useImageLoadingMonitoring.useEffect\"]);\n                }\n            }[\"useImageLoadingMonitoring.useEffect\"]);\n            images.forEach({\n                \"useImageLoadingMonitoring.useEffect\": (img)=>observer.observe(img)\n            }[\"useImageLoadingMonitoring.useEffect\"]);\n            return ({\n                \"useImageLoadingMonitoring.useEffect\": ()=>observer.disconnect()\n            })[\"useImageLoadingMonitoring.useEffect\"];\n        }\n    }[\"useImageLoadingMonitoring.useEffect\"], []);\n}\n// 错误监控\nfunction useErrorMonitoring() {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useErrorMonitoring.useEffect\": ()=>{\n            const handleError = {\n                \"useErrorMonitoring.useEffect.handleError\": (event)=>{\n                    console.error('JavaScript Error:', {\n                        message: event.message,\n                        filename: event.filename,\n                        lineno: event.lineno,\n                        colno: event.colno,\n                        error: event.error\n                    });\n                // 发送错误到监控服务\n                // sendErrorToService(event)\n                }\n            }[\"useErrorMonitoring.useEffect.handleError\"];\n            const handleUnhandledRejection = {\n                \"useErrorMonitoring.useEffect.handleUnhandledRejection\": (event)=>{\n                    console.error('Unhandled Promise Rejection:', event.reason);\n                // 发送错误到监控服务\n                // sendErrorToService(event)\n                }\n            }[\"useErrorMonitoring.useEffect.handleUnhandledRejection\"];\n            window.addEventListener('error', handleError);\n            window.addEventListener('unhandledrejection', handleUnhandledRejection);\n            return ({\n                \"useErrorMonitoring.useEffect\": ()=>{\n                    window.removeEventListener('error', handleError);\n                    window.removeEventListener('unhandledrejection', handleUnhandledRejection);\n                }\n            })[\"useErrorMonitoring.useEffect\"];\n        }\n    }[\"useErrorMonitoring.useEffect\"], []);\n}\n// 用户体验监控\nfunction useUserExperienceMonitoring() {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useUserExperienceMonitoring.useEffect\": ()=>{\n            // 监控页面可见性变化\n            const handleVisibilityChange = {\n                \"useUserExperienceMonitoring.useEffect.handleVisibilityChange\": ()=>{\n                    console.log('Page visibility changed:', document.visibilityState);\n                }\n            }[\"useUserExperienceMonitoring.useEffect.handleVisibilityChange\"];\n            // 监控网络状态变化\n            const handleOnline = {\n                \"useUserExperienceMonitoring.useEffect.handleOnline\": ()=>console.log('Network: Online')\n            }[\"useUserExperienceMonitoring.useEffect.handleOnline\"];\n            const handleOffline = {\n                \"useUserExperienceMonitoring.useEffect.handleOffline\": ()=>console.log('Network: Offline')\n            }[\"useUserExperienceMonitoring.useEffect.handleOffline\"];\n            document.addEventListener('visibilitychange', handleVisibilityChange);\n            window.addEventListener('online', handleOnline);\n            window.addEventListener('offline', handleOffline);\n            return ({\n                \"useUserExperienceMonitoring.useEffect\": ()=>{\n                    document.removeEventListener('visibilitychange', handleVisibilityChange);\n                    window.removeEventListener('online', handleOnline);\n                    window.removeEventListener('offline', handleOffline);\n                }\n            })[\"useUserExperienceMonitoring.useEffect\"];\n        }\n    }[\"useUserExperienceMonitoring.useEffect\"], []);\n}\n// 综合性能监控组件\nfunction PerformanceMonitor() {\n    usePerformanceMonitoring();\n    useImageLoadingMonitoring();\n    useErrorMonitoring();\n    useUserExperienceMonitoring();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WebVitals, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\performance\\\\web-vitals.tsx\",\n        lineNumber: 202,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/performance/web-vitals.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatPhoneNumber: () => (/* binding */ formatPhoneNumber),\n/* harmony export */   formatTime: () => (/* binding */ formatTime)\n/* harmony export */ });\nfunction cn(...inputs) {\n    return inputs.filter(Boolean).join(' ');\n}\nfunction formatPhoneNumber(phone) {\n    const cleaned = phone.replace(/\\D/g, '');\n    const match = cleaned.match(/^(\\d{3})(\\d{3})(\\d{4})$/);\n    if (match) {\n        return `(${match[1]}) ${match[2]}-${match[3]}`;\n    }\n    return phone;\n}\nfunction formatTime(time) {\n    const [hours, minutes] = time.split(':');\n    const hour = parseInt(hours, 10);\n    const ampm = hour >= 12 ? 'PM' : 'AM';\n    const displayHour = hour % 12 || 12;\n    return `${displayHour}:${minutes} ${ampm}`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/web-vitals","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Czhaosihao%5CDesktop%5Cpython_study%5Ctony_project%5Cbarbershop-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Czhaosihao%5CDesktop%5Cpython_study%5Ctony_project%5Cbarbershop-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();