"use client"

import { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { Sidebar } from './sidebar'
import { Header } from './header'
import { Breadcrumb } from './breadcrumb'
import { AuthGuard } from '@/components/admin/auth/auth-guard'

interface AdminLayoutProps {
  children: React.ReactNode
  className?: string
  showBreadcrumb?: boolean
  breadcrumbItems?: Array<{ label: string; href?: string; isActive?: boolean }>
}

export function AdminLayout({ 
  children, 
  className, 
  showBreadcrumb = true,
  breadcrumbItems 
}: AdminLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  // 监听窗口大小变化，自动关闭移动端侧边栏
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setSidebarOpen(false)
      }
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // 阻止滚动穿透（当移动端侧边栏打开时）
  useEffect(() => {
    if (sidebarOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [sidebarOpen])

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen)
  }

  return (
    <AuthGuard>
      <div className="min-h-screen bg-background">
        {/* 侧边栏 */}
        <Sidebar 
          isOpen={sidebarOpen} 
          onToggle={toggleSidebar}
        />

        {/* 主内容区域 */}
        <div className="lg:ml-64">
          {/* 顶部栏 */}
          <Header onMenuToggle={toggleSidebar} />

          {/* 面包屑导航 */}
          {showBreadcrumb && (
            <div className="px-4 py-3 border-b border-border bg-card/50">
              <Breadcrumb items={breadcrumbItems} />
            </div>
          )}

          {/* 页面内容 */}
          <main className={cn("p-4 lg:p-6", className)}>
            {children}
          </main>
        </div>
      </div>
    </AuthGuard>
  )
}

// 简化版布局，用于不需要侧边栏的页面
export function SimpleAdminLayout({ 
  children, 
  className 
}: { 
  children: React.ReactNode
  className?: string 
}) {
  return (
    <AuthGuard>
      <div className={cn("min-h-screen bg-background", className)}>
        {children}
      </div>
    </AuthGuard>
  )
}

// 页面容器组件
export function PageContainer({ 
  children, 
  className,
  title,
  description,
  action
}: { 
  children: React.ReactNode
  className?: string
  title?: string
  description?: string
  action?: React.ReactNode
}) {
  return (
    <div className={cn("space-y-6", className)}>
      {(title || description || action) && (
        <div className="flex items-center justify-between">
          <div>
            {title && (
              <h1 className="text-2xl font-bold text-foreground">{title}</h1>
            )}
            {description && (
              <p className="text-muted-foreground mt-1">{description}</p>
            )}
          </div>
          {action && (
            <div>{action}</div>
          )}
        </div>
      )}
      {children}
    </div>
  )
}

// 卡片容器组件
export function CardContainer({ 
  children, 
  className,
  title,
  description,
  action
}: { 
  children: React.ReactNode
  className?: string
  title?: string
  description?: string
  action?: React.ReactNode
}) {
  return (
    <div className={cn("bg-card border border-border rounded-lg", className)}>
      {(title || description || action) && (
        <div className="px-6 py-4 border-b border-border">
          <div className="flex items-center justify-between">
            <div>
              {title && (
                <h3 className="text-lg font-semibold text-foreground">{title}</h3>
              )}
              {description && (
                <p className="text-sm text-muted-foreground mt-1">{description}</p>
              )}
            </div>
            {action && (
              <div>{action}</div>
            )}
          </div>
        </div>
      )}
      <div className="p-6">
        {children}
      </div>
    </div>
  )
}
