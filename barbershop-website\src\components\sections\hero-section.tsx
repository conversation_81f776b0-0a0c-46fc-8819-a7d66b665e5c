"use client"

import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { FadeIn, StaggeredFadeIn, ScaleIn } from "@/components/animations/fade-in"
import { FloatingElement, Magnetic } from "@/components/animations/page-transition"

export function HeroSection() {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-r from-black/80 to-black/60 z-10" />
        <Image
          src="/hero-barbershop.jpg"
          alt="Classic Cuts Barbershop Interior"
          fill
          className="object-cover"
          priority
          placeholder="blur"
          blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
        />
      </div>

      {/* Content */}
      <div className="relative z-20 container mx-auto px-4 text-center text-white">
        <div className="max-w-4xl mx-auto">
          {/* Badge */}
          <FadeIn delay={200}>
            <FloatingElement amplitude={8} duration={4000}>
              <div className="inline-flex items-center space-x-2 bg-accent/40 backdrop-blur-md border-2 border-accent/60 rounded-full px-6 py-3 mb-6 hover:bg-accent/50 transition-all duration-300 shadow-xl">
                <span className="text-black drop-shadow-sm text-lg">✂️</span>
                <span className="text-sm font-semibold text-black drop-shadow-sm">专业理发服务</span>
              </div>
            </FloatingElement>
          </FadeIn>

          {/* Main Heading */}
          <FadeIn delay={400} direction="up" distance={50}>
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight text-shadow">
              <span className="block text-white drop-shadow-lg">经典剪裁</span>
              <span className="block text-accent drop-shadow-lg">现代风格</span>
            </h1>
          </FadeIn>

          {/* Subtitle */}
          <FadeIn delay={600} direction="up" distance={30}>
            <p className="text-xl md:text-2xl mb-8 text-gray-100 max-w-2xl mx-auto drop-shadow-md">
              传承经典理发工艺，融合现代时尚元素，为您打造完美造型
            </p>
          </FadeIn>

          {/* Stats */}
          <StaggeredFadeIn delay={800} staggerDelay={150}>
            {[
              <div className="flex items-center space-x-2">
                <span className="text-accent drop-shadow-sm">⭐</span>
                <span className="text-lg font-semibold text-white drop-shadow-sm">4.9/5 评分</span>
              </div>,
              <div className="flex items-center space-x-2">
                <span className="text-accent drop-shadow-sm">🕐</span>
                <span className="text-lg font-semibold text-white drop-shadow-sm">15年经验</span>
              </div>,
              <div className="flex items-center space-x-2">
                <span className="text-accent drop-shadow-sm">📍</span>
                <span className="text-lg font-semibold text-white drop-shadow-sm">市中心位置</span>
              </div>
            ]}
          </StaggeredFadeIn>
          <div className="flex flex-wrap justify-center items-center gap-8 mb-10">
          </div>

          {/* CTA Buttons */}
          <FadeIn delay={1200} direction="up" distance={20}>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Magnetic strength={0.2}>
                <Button asChild size="lg" className="bg-accent hover:bg-accent/90 text-black font-semibold px-8 py-3 text-lg shadow-xl border-2 border-accent hover:border-accent/90 transition-all duration-300" icon="✂️" rightIcon="→">
                  <Link href="/booking">立即预约</Link>
                </Button>
              </Magnetic>
              <Magnetic strength={0.15}>
                <Button asChild variant="outline" size="lg" className="border-2 border-white/90 text-white bg-white/10 hover:bg-white hover:text-black px-8 py-3 text-lg shadow-xl backdrop-blur-md font-semibold transition-all duration-300" icon="👁️">
                  <Link href="/services">查看服务</Link>
                </Button>
              </Magnetic>
            </div>
          </FadeIn>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
        <div className="animate-bounce">
          <div className="w-6 h-10 border-2 border-white rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </div>
    </section>
  )
}
