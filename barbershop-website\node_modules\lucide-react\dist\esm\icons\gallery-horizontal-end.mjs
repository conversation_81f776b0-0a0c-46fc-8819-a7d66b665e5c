/**
 * lucide-react v0.0.1 - ISC
 */

import createLucideIcon from '../createLucideIcon.mjs';

const GalleryHorizontalEnd = createLucideIcon("GalleryHorizontalEnd", [
  ["path", { d: "M2 7v10", key: "a2pl2d" }],
  ["path", { d: "M6 5v14", key: "1kq3d7" }],
  [
    "rect",
    { width: "12", height: "18", x: "10", y: "3", rx: "2", key: "13i7bc" }
  ]
]);

export { GalleryHorizontalEnd as default };
//# sourceMappingURL=gallery-horizontal-end.mjs.map
