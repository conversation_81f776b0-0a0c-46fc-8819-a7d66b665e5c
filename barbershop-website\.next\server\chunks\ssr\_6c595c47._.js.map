{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/components/layout/navbar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Navbar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Navbar() from the server but Navbar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/navbar.tsx <module evaluation>\",\n    \"Navbar\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,kEACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/components/layout/navbar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Navbar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Navbar() from the server but Navbar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/navbar.tsx\",\n    \"Navbar\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8CACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/components/layout/footer.tsx"], "sourcesContent": ["import Link from \"next/link\"\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-primary text-primary-foreground\">\n      <div className=\"container mx-auto px-4 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Brand */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"text-2xl text-accent\">✂️</span>\n              <span className=\"text-xl font-bold\">Classic Cuts</span>\n            </div>\n            <p className=\"text-sm text-primary-foreground/80\">\n              专业的男士理发服务，传统工艺与现代风格的完美结合。\n            </p>\n            <div className=\"flex space-x-4\">\n              <Link href=\"#\" className=\"text-primary-foreground/80 hover:text-accent transition-colors\">\n                <span className=\"text-xl\">📘</span>\n                <span className=\"sr-only\">Facebook</span>\n              </Link>\n              <Link href=\"#\" className=\"text-primary-foreground/80 hover:text-accent transition-colors\">\n                <span className=\"text-xl\">📷</span>\n                <span className=\"sr-only\">Instagram</span>\n              </Link>\n              <Link href=\"#\" className=\"text-primary-foreground/80 hover:text-accent transition-colors\">\n                <span className=\"text-xl\">🐦</span>\n                <span className=\"sr-only\">Twitter</span>\n              </Link>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold\">快速链接</h3>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/\" className=\"text-sm text-primary-foreground/80 hover:text-accent transition-colors\">\n                  首页\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/services\" className=\"text-sm text-primary-foreground/80 hover:text-accent transition-colors\">\n                  服务项目\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"text-sm text-primary-foreground/80 hover:text-accent transition-colors\">\n                  关于我们\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/gallery\" className=\"text-sm text-primary-foreground/80 hover:text-accent transition-colors\">\n                  作品展示\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"text-sm text-primary-foreground/80 hover:text-accent transition-colors\">\n                  联系我们\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Services */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold\">服务项目</h3>\n            <ul className=\"space-y-2\">\n              <li className=\"text-sm text-primary-foreground/80\">经典理发</li>\n              <li className=\"text-sm text-primary-foreground/80\">胡须修剪</li>\n              <li className=\"text-sm text-primary-foreground/80\">头发造型</li>\n              <li className=\"text-sm text-primary-foreground/80\">洗发护理</li>\n              <li className=\"text-sm text-primary-foreground/80\">面部护理</li>\n            </ul>\n          </div>\n\n          {/* Contact Info */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold\">联系信息</h3>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"text-accent\">📍</span>\n                <span className=\"text-sm text-primary-foreground/80\">\n                  123 Main Street, City, State 12345\n                </span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"text-accent\">📞</span>\n                <span className=\"text-sm text-primary-foreground/80\">\n                  (555) 123-4567\n                </span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"text-accent\">📧</span>\n                <span className=\"text-sm text-primary-foreground/80\">\n                  <EMAIL>\n                </span>\n              </div>\n              <div className=\"flex items-start space-x-2\">\n                <span className=\"text-accent\">🕐</span>\n                <div className=\"text-sm text-primary-foreground/80\">\n                  <div>周一-周五: 9:00 AM - 8:00 PM</div>\n                  <div>周六: 8:00 AM - 6:00 PM</div>\n                  <div>周日: 10:00 AM - 4:00 PM</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"mt-8 pt-8 border-t border-primary-foreground/20\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <p className=\"text-sm text-primary-foreground/80\">\n              © 2024 Classic Cuts Barbershop. All rights reserved.\n            </p>\n            <div className=\"flex space-x-4 mt-4 md:mt-0\">\n              <Link href=\"/privacy\" className=\"text-sm text-primary-foreground/80 hover:text-accent transition-colors\">\n                隐私政策\n              </Link>\n              <Link href=\"/terms\" className=\"text-sm text-primary-foreground/80 hover:text-accent transition-colors\">\n                服务条款\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAuB;;;;;;sDACvC,8OAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAGlD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAMhC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAyE;;;;;;;;;;;sDAIpG,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAyE;;;;;;;;;;;sDAI5G,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAyE;;;;;;;;;;;sDAIzG,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAyE;;;;;;;;;;;sDAI3G,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAyE;;;;;;;;;;;;;;;;;;;;;;;sCAQ/G,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;;;;;;;;;;;;;sCAKvD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,8OAAC;oDAAK,WAAU;8DAAqC;;;;;;;;;;;;sDAIvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,8OAAC;oDAAK,WAAU;8DAAqC;;;;;;;;;;;;sDAIvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,8OAAC;oDAAK,WAAU;8DAAqC;;;;;;;;;;;;sDAIvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAI;;;;;;sEACL,8OAAC;sEAAI;;;;;;sEACL,8OAAC;sEAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOf,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;0CAGlD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAyE;;;;;;kDAGzG,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAyE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrH", "debugId": null}}, {"offset": {"line": 597, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/components/layout/main-layout.tsx"], "sourcesContent": ["import { Navbar } from \"./navbar\"\nimport { Footer } from \"./footer\"\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  return (\n    <div className=\"min-h-screen flex flex-col\">\n      <Navbar />\n      <main className=\"flex-1\">\n        {children}\n      </main>\n      <Footer />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAMO,SAAS,WAAW,EAAE,QAAQ,EAAmB;IACtD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,SAAM;;;;;0BACP,8OAAC;gBAAK,WAAU;0BACb;;;;;;0BAEH,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;;AAGb", "debugId": null}}, {"offset": {"line": 641, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: \"default\" | \"outline\" | \"secondary\" | \"ghost\" | \"link\"\n  size?: \"default\" | \"sm\" | \"lg\" | \"icon\"\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className = \"\", variant = \"default\", size = \"default\", asChild = false, children, ...props }, ref) => {\n    const baseClasses = \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\"\n\n    const variantClasses = {\n      default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n      outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n      secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n      ghost: \"hover:bg-accent hover:text-accent-foreground\",\n      link: \"text-primary underline-offset-4 hover:underline\",\n    }\n\n    const sizeClasses = {\n      default: \"h-10 px-4 py-2\",\n      sm: \"h-9 rounded-md px-3\",\n      lg: \"h-11 rounded-md px-8\",\n      icon: \"h-10 w-10\",\n    }\n\n    const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`.trim()\n\n    if (asChild && React.isValidElement(children)) {\n      return React.cloneElement(children, {\n        className: classes,\n        ref,\n        ...props,\n      })\n    }\n\n    return (\n      <button\n        className={classes}\n        ref={ref}\n        {...props}\n      >\n        {children}\n      </button>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;;;AAQA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,YAAY,EAAE,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC/F,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,SAAS;QACT,WAAW;QACX,OAAO;QACP,MAAM;IACR;IAEA,MAAM,cAAc;QAClB,SAAS;QACT,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,MAAM,UAAU,GAAG,YAAY,CAAC,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC,IAAI;IAElG,IAAI,yBAAW,CAAA,GAAA,qMAAA,CAAA,iBAAoB,AAAD,EAAE,WAAW;QAC7C,qBAAO,CAAA,GAAA,qMAAA,CAAA,eAAkB,AAAD,EAAE,UAAU;YAClC,WAAW;YACX;YACA,GAAG,KAAK;QACV;IACF;IAEA,qBACE,8OAAC;QACC,WAAW;QACX,KAAK;QACJ,GAAG,KAAK;kBAER;;;;;;AAGP;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/components/sections/hero-section.tsx"], "sourcesContent": ["import Link from \"next/link\"\nimport Image from \"next/image\"\nimport { But<PERSON> } from \"@/components/ui/button\"\n\nexport function HeroSection() {\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background Image */}\n      <div className=\"absolute inset-0 z-0\">\n        <div className=\"absolute inset-0 bg-gradient-to-r from-black/70 to-black/50 z-10\" />\n        <Image\n          src=\"/hero-barbershop.jpg\"\n          alt=\"Classic Cuts Barbershop Interior\"\n          fill\n          className=\"object-cover\"\n          priority\n          placeholder=\"blur\"\n          blurDataURL=\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==\"\n        />\n      </div>\n\n      {/* Content */}\n      <div className=\"relative z-20 container mx-auto px-4 text-center text-white\">\n        <div className=\"max-w-4xl mx-auto\">\n          {/* Badge */}\n          <div className=\"inline-flex items-center space-x-2 bg-accent/20 backdrop-blur-sm border border-accent/30 rounded-full px-4 py-2 mb-6\">\n            <span className=\"text-accent\">✂️</span>\n            <span className=\"text-sm font-medium text-accent\">专业理发服务</span>\n          </div>\n\n          {/* Main Heading */}\n          <h1 className=\"text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight\">\n            <span className=\"block\">经典剪裁</span>\n            <span className=\"block text-accent\">现代风格</span>\n          </h1>\n\n          {/* Subtitle */}\n          <p className=\"text-xl md:text-2xl mb-8 text-gray-200 max-w-2xl mx-auto\">\n            传承经典理发工艺，融合现代时尚元素，为您打造完美造型\n          </p>\n\n          {/* Stats */}\n          <div className=\"flex flex-wrap justify-center items-center gap-8 mb-10\">\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"text-accent\">⭐</span>\n              <span className=\"text-lg font-semibold\">4.9/5 评分</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"text-accent\">🕐</span>\n              <span className=\"text-lg font-semibold\">15年经验</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"text-accent\">📍</span>\n              <span className=\"text-lg font-semibold\">市中心位置</span>\n            </div>\n          </div>\n\n          {/* CTA Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n            <Button asChild size=\"lg\" className=\"bg-accent hover:bg-accent/90 text-black font-semibold px-8 py-3 text-lg\">\n              <Link href=\"/booking\">立即预约</Link>\n            </Button>\n            <Button asChild variant=\"outline\" size=\"lg\" className=\"border-white text-white hover:bg-white hover:text-black px-8 py-3 text-lg\">\n              <Link href=\"/services\">查看服务</Link>\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20\">\n        <div className=\"animate-bounce\">\n          <div className=\"w-6 h-10 border-2 border-white rounded-full flex justify-center\">\n            <div className=\"w-1 h-3 bg-white rounded-full mt-2 animate-pulse\"></div>\n          </div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,IAAI;wBACJ,WAAU;wBACV,QAAQ;wBACR,aAAY;wBACZ,aAAY;;;;;;;;;;;;0BAKhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAc;;;;;;8CAC9B,8OAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;sCAIpD,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;oCAAK,WAAU;8CAAQ;;;;;;8CACxB,8OAAC;oCAAK,WAAU;8CAAoB;;;;;;;;;;;;sCAItC,8OAAC;4BAAE,WAAU;sCAA2D;;;;;;sCAKxE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAc;;;;;;sDAC9B,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;8CAE1C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAc;;;;;;sDAC9B,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;8CAE1C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAc;;;;;;sDAC9B,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAK5C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAC,MAAK;oCAAK,WAAU;8CAClC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDAAW;;;;;;;;;;;8CAExB,8OAAC,kIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,WAAU;8CACpD,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO/B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B", "debugId": null}}, {"offset": {"line": 977, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/lib/utils.ts"], "sourcesContent": ["export function cn(...inputs: (string | undefined | null | boolean)[]) {\n  return inputs.filter(Boolean).join(' ')\n}\n\nexport function formatPhoneNumber(phone: string): string {\n  const cleaned = phone.replace(/\\D/g, '')\n  const match = cleaned.match(/^(\\d{3})(\\d{3})(\\d{4})$/)\n  if (match) {\n    return `(${match[1]}) ${match[2]}-${match[3]}`\n  }\n  return phone\n}\n\nexport function formatTime(time: string): string {\n  const [hours, minutes] = time.split(':')\n  const hour = parseInt(hours, 10)\n  const ampm = hour >= 12 ? 'PM' : 'AM'\n  const displayHour = hour % 12 || 12\n  return `${displayHour}:${minutes} ${ampm}`\n}\n"], "names": [], "mappings": ";;;;;AAAO,SAAS,GAAG,GAAG,MAA+C;IACnE,OAAO,OAAO,MAAM,CAAC,SAAS,IAAI,CAAC;AACrC;AAEO,SAAS,kBAAkB,KAAa;IAC7C,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO;IACrC,MAAM,QAAQ,QAAQ,KAAK,CAAC;IAC5B,IAAI,OAAO;QACT,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE;IAChD;IACA,OAAO;AACT;AAEO,SAAS,WAAW,IAAY;IACrC,MAAM,CAAC,OAAO,QAAQ,GAAG,KAAK,KAAK,CAAC;IACpC,MAAM,OAAO,SAAS,OAAO;IAC7B,MAAM,OAAO,QAAQ,KAAK,OAAO;IACjC,MAAM,cAAc,OAAO,MAAM;IACjC,OAAO,GAAG,YAAY,CAAC,EAAE,QAAQ,CAAC,EAAE,MAAM;AAC5C", "debugId": null}}, {"offset": {"line": 1006, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1087, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/components/sections/services-preview.tsx"], "sourcesContent": ["import Link from \"next/link\"\nimport { Button } from \"@/components/ui/button\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\n\nconst services = [\n  {\n    icon: \"✂️\",\n    title: \"经典理发\",\n    description: \"专业理发师为您提供精准的剪发服务，打造适合您的完美发型\",\n    price: \"¥80-120\",\n    duration: \"45分钟\",\n  },\n  {\n    icon: \"⚡\",\n    title: \"胡须修剪\",\n    description: \"精细的胡须造型和修剪，让您的面部轮廓更加立体有型\",\n    price: \"¥60-80\",\n    duration: \"30分钟\",\n  },\n  {\n    icon: \"✨\",\n    title: \"头发造型\",\n    description: \"使用专业造型产品，为您打造时尚个性的发型造型\",\n    price: \"¥50-70\",\n    duration: \"20分钟\",\n  },\n  {\n    icon: \"🧴\",\n    title: \"洗发护理\",\n    description: \"深层清洁和滋养护理，让您的头发健康有光泽\",\n    price: \"¥40-60\",\n    duration: \"25分钟\",\n  },\n]\n\nexport function ServicesPreview() {\n  return (\n    <section className=\"py-20 bg-muted/50\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">\n            专业服务项目\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            我们提供全方位的男士理发和护理服务，每一项服务都体现我们对品质的追求\n          </p>\n        </div>\n\n        {/* Services Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12\">\n          {services.map((service, index) => {\n            return (\n              <Card key={index} className=\"group hover:shadow-lg transition-all duration-300 hover:-translate-y-1\">\n                <CardHeader className=\"text-center\">\n                  <div className=\"mx-auto mb-4 p-3 bg-primary/10 rounded-full w-fit group-hover:bg-primary/20 transition-colors\">\n                    <span className=\"text-3xl\">{service.icon}</span>\n                  </div>\n                  <CardTitle className=\"text-xl\">{service.title}</CardTitle>\n                  <CardDescription className=\"text-sm\">\n                    {service.description}\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"text-center\">\n                  <div className=\"space-y-2\">\n                    <div className=\"text-2xl font-bold text-primary\">\n                      {service.price}\n                    </div>\n                    <div className=\"text-sm text-muted-foreground\">\n                      {service.duration}\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            )\n          })}\n        </div>\n\n        {/* CTA */}\n        <div className=\"text-center\">\n          <Button asChild size=\"lg\" className=\"px-8\">\n            <Link href=\"/services\">查看所有服务</Link>\n          </Button>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,WAAW;IACf;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;IACZ;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;IACZ;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;IACZ;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;IACZ;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAMjE,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS;wBACtB,qBACE,8OAAC,gIAAA,CAAA,OAAI;4BAAa,WAAU;;8CAC1B,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAY,QAAQ,IAAI;;;;;;;;;;;sDAE1C,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAW,QAAQ,KAAK;;;;;;sDAC7C,8OAAC,gIAAA,CAAA,kBAAe;4CAAC,WAAU;sDACxB,QAAQ,WAAW;;;;;;;;;;;;8CAGxB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,KAAK;;;;;;0DAEhB,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;2BAhBd;;;;;oBAsBf;;;;;;8BAIF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAC,MAAK;wBAAK,WAAU;kCAClC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMnC", "debugId": null}}, {"offset": {"line": 1291, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/components/sections/testimonials-section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const TestimonialsSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call TestimonialsSection() from the server but TestimonialsSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/testimonials-section.tsx <module evaluation>\",\n    \"TestimonialsSection\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,kFACA", "debugId": null}}, {"offset": {"line": 1305, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/components/sections/testimonials-section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const TestimonialsSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call TestimonialsSection() from the server but TestimonialsSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/testimonials-section.tsx\",\n    \"TestimonialsSection\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,8DACA", "debugId": null}}, {"offset": {"line": 1319, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1329, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/components/sections/about-preview.tsx"], "sourcesContent": ["import Link from \"next/link\"\nimport Image from \"next/image\"\nimport { Button } from \"@/components/ui/button\"\n\nconst stats = [\n  {\n    icon: \"🏆\",\n    number: \"15+\",\n    label: \"年专业经验\",\n  },\n  {\n    icon: \"👥\",\n    number: \"5000+\",\n    label: \"满意客户\",\n  },\n  {\n    icon: \"🕐\",\n    number: \"24/7\",\n    label: \"在线预约\",\n  },\n  {\n    icon: \"✂️\",\n    number: \"100%\",\n    label: \"专业保证\",\n  },\n]\n\nexport function AboutPreview() {\n  return (\n    <section className=\"py-20\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          {/* Content */}\n          <div className=\"space-y-6\">\n            <div className=\"space-y-4\">\n              <h2 className=\"text-3xl md:text-4xl font-bold\">\n                传承经典，创新未来\n              </h2>\n              <p className=\"text-lg text-muted-foreground\">\n                Classic Cuts 成立于2009年，我们致力于为每一位客户提供最专业的理发服务。\n                我们的理发师团队拥有丰富的经验和精湛的技艺，结合传统理发工艺与现代时尚元素。\n              </p>\n              <p className=\"text-muted-foreground\">\n                在这里，您不仅能享受到专业的理发服务，更能体验到传统理发店的温馨氛围。\n                我们相信，每一次剪发都是一次艺术创作，每一位客户都值得我们用心对待。\n              </p>\n            </div>\n\n            {/* Stats */}\n            <div className=\"grid grid-cols-2 gap-6 py-6\">\n              {stats.map((stat, index) => {\n                return (\n                  <div key={index} className=\"text-center\">\n                    <div className=\"mx-auto mb-2 p-2 bg-primary/10 rounded-full w-fit\">\n                      <span className=\"text-2xl\">{stat.icon}</span>\n                    </div>\n                    <div className=\"text-2xl font-bold text-primary\">\n                      {stat.number}\n                    </div>\n                    <div className=\"text-sm text-muted-foreground\">\n                      {stat.label}\n                    </div>\n                  </div>\n                )\n              })}\n            </div>\n\n            <Button asChild size=\"lg\">\n              <Link href=\"/about\">了解更多</Link>\n            </Button>\n          </div>\n\n          {/* Image */}\n          <div className=\"relative\">\n            <div className=\"relative h-96 lg:h-[500px] rounded-lg overflow-hidden\">\n              <Image\n                src=\"/about-barbershop.jpg\"\n                alt=\"Classic Cuts Barbershop Team\"\n                fill\n                className=\"object-cover\"\n                placeholder=\"blur\"\n                blurDataURL=\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==\"\n              />\n            </div>\n            \n            {/* Decorative Elements */}\n            <div className=\"absolute -top-4 -left-4 w-24 h-24 bg-accent/20 rounded-full blur-xl\"></div>\n            <div className=\"absolute -bottom-4 -right-4 w-32 h-32 bg-secondary/20 rounded-full blur-xl\"></div>\n          </div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,QAAQ;IACZ;QACE,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IACA;QACE,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IACA;QACE,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IACA;QACE,MAAM;QACN,QAAQ;QACR,OAAO;IACT;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAiC;;;;;;kDAG/C,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;kDAI7C,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAOvC,8OAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,MAAM;oCAChB,qBACE,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAY,KAAK,IAAI;;;;;;;;;;;0DAEvC,8OAAC;gDAAI,WAAU;0DACZ,KAAK,MAAM;;;;;;0DAEd,8OAAC;gDAAI,WAAU;0DACZ,KAAK,KAAK;;;;;;;uCARL;;;;;gCAYd;;;;;;0CAGF,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAC,MAAK;0CACnB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAS;;;;;;;;;;;;;;;;;kCAKxB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,IAAI;oCACJ,WAAU;oCACV,aAAY;oCACZ,aAAY;;;;;;;;;;;0CAKhB,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B", "debugId": null}}, {"offset": {"line": 1542, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/components/sections/cta-section.tsx"], "sourcesContent": ["import Link from \"next/link\"\nimport { Button } from \"@/components/ui/button\"\n\nexport function CTASection() {\n  return (\n    <section className=\"py-20 bg-primary text-primary-foreground\">\n      <div className=\"container mx-auto px-4 text-center\">\n        <div className=\"max-w-3xl mx-auto\">\n          {/* Main Heading */}\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">\n            准备好体验专业理发服务了吗？\n          </h2>\n          \n          {/* Subtitle */}\n          <p className=\"text-xl mb-8 text-primary-foreground/90\">\n            立即预约，让我们的专业理发师为您打造完美造型\n          </p>\n\n          {/* Contact Options */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-10\">\n            <div className=\"flex flex-col items-center space-y-2\">\n              <div className=\"p-3 bg-accent/20 rounded-full\">\n                <span className=\"text-2xl text-accent\">📞</span>\n              </div>\n              <div className=\"text-sm font-medium\">电话预约</div>\n              <div className=\"text-sm text-primary-foreground/80\">\n                (555) 123-4567\n              </div>\n            </div>\n\n            <div className=\"flex flex-col items-center space-y-2\">\n              <div className=\"p-3 bg-accent/20 rounded-full\">\n                <span className=\"text-2xl text-accent\">📅</span>\n              </div>\n              <div className=\"text-sm font-medium\">在线预约</div>\n              <div className=\"text-sm text-primary-foreground/80\">\n                24/7 随时预约\n              </div>\n            </div>\n\n            <div className=\"flex flex-col items-center space-y-2\">\n              <div className=\"p-3 bg-accent/20 rounded-full\">\n                <span className=\"text-2xl text-accent\">📍</span>\n              </div>\n              <div className=\"text-sm font-medium\">到店服务</div>\n              <div className=\"text-sm text-primary-foreground/80\">\n                市中心便利位置\n              </div>\n            </div>\n          </div>\n\n          {/* CTA Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n            <Button \n              asChild \n              size=\"lg\" \n              className=\"bg-accent hover:bg-accent/90 text-black font-semibold px-8 py-3 text-lg\"\n            >\n              <Link href=\"/booking\">立即在线预约</Link>\n            </Button>\n            \n            <Button \n              asChild \n              variant=\"outline\" \n              size=\"lg\" \n              className=\"border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary px-8 py-3 text-lg\"\n            >\n              <Link href=\"tel:+15551234567\">电话预约</Link>\n            </Button>\n          </div>\n\n          {/* Additional Info */}\n          <div className=\"mt-8 text-sm text-primary-foreground/80\">\n            <p>营业时间：周一至周五 9:00-20:00 | 周六 8:00-18:00 | 周日 10:00-16:00</p>\n          </div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAKpD,8OAAC;wBAAE,WAAU;kCAA0C;;;;;;kCAKvD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAuB;;;;;;;;;;;kDAEzC,8OAAC;wCAAI,WAAU;kDAAsB;;;;;;kDACrC,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;0CAKtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAuB;;;;;;;;;;;kDAEzC,8OAAC;wCAAI,WAAU;kDAAsB;;;;;;kDACrC,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;0CAKtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAuB;;;;;;;;;;;kDAEzC,8OAAC;wCAAI,WAAU;kDAAsB;;;;;;kDACrC,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;;;;;;;kCAOxD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,OAAO;gCACP,MAAK;gCACL,WAAU;0CAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAW;;;;;;;;;;;0CAGxB,8OAAC,kIAAA,CAAA,SAAM;gCACL,OAAO;gCACP,SAAQ;gCACR,MAAK;gCACL,WAAU;0CAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAmB;;;;;;;;;;;;;;;;;kCAKlC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 1785, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/app/page.tsx"], "sourcesContent": ["import { MainLayout } from \"@/components/layout/main-layout\"\nimport { HeroSection } from \"@/components/sections/hero-section\"\nimport { ServicesPreview } from \"@/components/sections/services-preview\"\nimport { TestimonialsSection } from \"@/components/sections/testimonials-section\"\nimport { AboutPreview } from \"@/components/sections/about-preview\"\nimport { CTASection } from \"@/components/sections/cta-section\"\n\nexport default function Home() {\n  return (\n    <MainLayout>\n      <HeroSection />\n      <ServicesPreview />\n      <AboutPreview />\n      <TestimonialsSection />\n      <CTASection />\n    </MainLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC,8IAAA,CAAA,aAAU;;0BACT,8OAAC,iJAAA,CAAA,cAAW;;;;;0BACZ,8OAAC,qJAAA,CAAA,kBAAe;;;;;0BAChB,8OAAC,kJAAA,CAAA,eAAY;;;;;0BACb,8OAAC,yJAAA,CAAA,sBAAmB;;;;;0BACpB,8OAAC,gJAAA,CAAA,aAAU;;;;;;;;;;;AAGjB", "debugId": null}}]}