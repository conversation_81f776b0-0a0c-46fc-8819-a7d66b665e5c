"use client"

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/admin/ui/badge'
import { 
  User, 
  Phone, 
  Mail, 
  Plus,
  X,
  Save,
  ArrowLeft
} from 'lucide-react'
import { customerStore, serviceStore } from '@/lib/admin/storage'
import { Customer, Service } from '@/lib/types/admin'

interface CustomerFormProps {
  customer?: Customer
  onSubmit?: (customer: Customer) => void
  onCancel?: () => void
}

export function CustomerForm({ customer, onSubmit, onCancel }: CustomerFormProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [services, setServices] = useState<Service[]>([])
  const [newPreference, setNewPreference] = useState('')
  
  const [formData, setFormData] = useState({
    name: customer?.name || '',
    phone: customer?.phone || '',
    email: customer?.email || '',
    preferences: customer?.preferences || [],
    notes: customer?.notes || ''
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    // 加载服务列表用于偏好选择
    const allServices = serviceStore.getAll()
    setServices(allServices.filter(s => s.isActive))
  }, [])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = '请输入客户姓名'
    }

    if (!formData.phone.trim()) {
      newErrors.phone = '请输入手机号码'
    } else if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
      newErrors.phone = '请输入正确的手机号码'
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = '请输入正确的邮箱地址'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)
    try {
      let result: Customer | null

      if (customer && customer.id) {
        // 更新客户
        result = customerStore.update(customer.id, {
          name: formData.name.trim(),
          phone: formData.phone.trim(),
          email: formData.email.trim() || undefined,
          preferences: formData.preferences,
          notes: formData.notes.trim() || undefined
        })
      } else {
        // 创建新客户
        result = customerStore.create({
          name: formData.name.trim(),
          phone: formData.phone.trim(),
          email: formData.email.trim() || undefined,
          preferences: formData.preferences,
          notes: formData.notes.trim() || undefined,
          totalVisits: 0,
          totalSpent: 0
        })
      }

      if (result) {
        if (onSubmit) {
          onSubmit(result)
        } else {
          // 跳转到列表页面并显示成功状态
          const successType = customer && customer.id ? 'updated' : 'created'
          router.push(`/admin/customers?success=${successType}`)
        }
      } else {
        setErrors({ submit: '保存失败，请重试' })
      }
    } catch (error) {
      console.error('Failed to save customer:', error)
      setErrors({ submit: '保存失败，请重试' })
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    if (onCancel) {
      onCancel()
    } else {
      router.back()
    }
  }

  const addPreference = (serviceId: string) => {
    if (serviceId && !formData.preferences.some(p => p.serviceId === serviceId)) {
      setFormData(prev => ({
        ...prev,
        preferences: [...prev.preferences, { serviceId }]
      }))
    }
    setNewPreference('')
  }

  const removePreference = (index: number) => {
    setFormData(prev => ({
      ...prev,
      preferences: prev.preferences.filter((_, i) => i !== index)
    }))
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* 基本信息 */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium flex items-center">
          <User className="h-5 w-5 mr-2" />
          基本信息
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              客户姓名 <span className="text-red-500">*</span>
            </label>
            <Input
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="请输入客户姓名"
              className={errors.name ? 'border-red-500' : ''}
            />
            {errors.name && (
              <p className="text-sm text-red-500 mt-1">{errors.name}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              手机号码 <span className="text-red-500">*</span>
            </label>
            <Input
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              placeholder="请输入手机号码"
              className={errors.phone ? 'border-red-500' : ''}
            />
            {errors.phone && (
              <p className="text-sm text-red-500 mt-1">{errors.phone}</p>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">
            邮箱地址
          </label>
          <Input
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            placeholder="请输入邮箱地址（可选）"
            className={errors.email ? 'border-red-500' : ''}
          />
          {errors.email && (
            <p className="text-sm text-red-500 mt-1">{errors.email}</p>
          )}
        </div>
      </div>

      {/* 服务偏好 */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">服务偏好</h3>
        
        {/* 当前偏好 */}
        <div>
          <label className="block text-sm font-medium mb-2">已选偏好</label>
          <div className="flex flex-wrap gap-2 min-h-[40px] p-3 border border-border rounded-md bg-muted/20">
            {formData.preferences.length > 0 ? (
              formData.preferences.map((pref, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="flex items-center space-x-1"
                >
                  <span>{services.find(s => s.id === pref.serviceId)?.name || pref.serviceId}</span>
                  <button
                    type="button"
                    onClick={() => removePreference(index)}
                    className="ml-1 hover:text-red-500"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))
            ) : (
              <span className="text-sm text-muted-foreground">暂无偏好设置</span>
            )}
          </div>
        </div>

        {/* 添加偏好 */}
        <div>
          <label className="block text-sm font-medium mb-2">添加偏好</label>
          <div className="flex space-x-2">
            <Input
              value={newPreference}
              onChange={(e) => setNewPreference(e.target.value)}
              placeholder="输入服务偏好"
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault()
                  addPreference(newPreference)
                }
              }}
            />
            <Button
              type="button"
              variant="outline"
              onClick={() => addPreference(newPreference)}
              disabled={!newPreference.trim()}
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* 快速选择服务 */}
        <div>
          <label className="block text-sm font-medium mb-2">快速选择</label>
          <div className="flex flex-wrap gap-2">
            {services.map(service => (
              <Button
                key={service.id}
                type="button"
                variant="outline"
                size="sm"
                onClick={() => addPreference(service.id)}
                disabled={formData.preferences.some(p => p.serviceId === service.id)}
                className="text-xs"
              >
                {service.name}
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* 备注 */}
      <div>
        <label className="block text-sm font-medium mb-2">备注信息</label>
        <Textarea
          value={formData.notes}
          onChange={(e) => handleInputChange('notes', e.target.value)}
          placeholder="客户的特殊要求、注意事项等..."
          rows={4}
        />
      </div>

      {/* 错误信息 */}
      {errors.submit && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{errors.submit}</p>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="flex justify-end space-x-3 pt-6 border-t">
        <Button
          type="button"
          variant="outline"
          onClick={handleCancel}
          disabled={loading}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          取消
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? (
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
          ) : (
            <Save className="h-4 w-4 mr-2" />
          )}
          {customer ? '更新客户' : '创建客户'}
        </Button>
      </div>
    </form>
  )
}
