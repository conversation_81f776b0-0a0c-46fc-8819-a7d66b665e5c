"use client"

import { useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { authService } from '@/lib/admin/auth'
import { UserRole } from '@/lib/types/admin'

interface AuthGuardProps {
  children: React.ReactNode
  requiredRole?: UserRole
  fallback?: React.ReactNode
}

export function AuthGuard({ children, requiredRole = 'admin', fallback }: AuthGuardProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthorized, setIsAuthorized] = useState(false)
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    const checkAuth = () => {
      const authState = authService.getAuthState()
      
      // 如果未登录，重定向到登录页
      if (!authState.isAuthenticated) {
        router.push('/admin/login')
        return
      }

      // 检查权限
      const hasPermission = authService.hasPermission(requiredRole)
      
      if (!hasPermission) {
        // 权限不足，可以重定向到无权限页面或显示错误
        setIsAuthorized(false)
        setIsLoading(false)
        return
      }

      setIsAuthorized(true)
      setIsLoading(false)
    }

    checkAuth()
  }, [router, pathname, requiredRole])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-muted-foreground">验证身份中...</p>
        </div>
      </div>
    )
  }

  if (!isAuthorized) {
    return fallback || (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-6xl mb-4">🚫</div>
          <h1 className="text-2xl font-bold text-foreground mb-2">访问被拒绝</h1>
          <p className="text-muted-foreground mb-6">
            您没有权限访问此页面。请联系管理员获取相应权限。
          </p>
          <button
            onClick={() => router.push('/admin')}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            返回首页
          </button>
        </div>
      </div>
    )
  }

  return <>{children}</>
}

// 高阶组件版本
export function withAuthGuard<P extends object>(
  Component: React.ComponentType<P>,
  requiredRole: UserRole = 'admin'
) {
  return function AuthGuardedComponent(props: P) {
    return (
      <AuthGuard requiredRole={requiredRole}>
        <Component {...props} />
      </AuthGuard>
    )
  }
}

// Hook版本，用于在组件内部检查权限
export function useAuthGuard(requiredRole: UserRole = 'admin') {
  const [authState, setAuthState] = useState<{
    isLoading: boolean
    isAuthenticated: boolean
    isAuthorized: boolean
    user: any
  }>({
    isLoading: true,
    isAuthenticated: false,
    isAuthorized: false,
    user: null
  })
  const router = useRouter()

  useEffect(() => {
    const checkAuth = () => {
      const auth = authService.getAuthState()
      
      if (!auth.isAuthenticated) {
        setAuthState({
          isLoading: false,
          isAuthenticated: false,
          isAuthorized: false,
          user: null
        })
        return
      }

      const hasPermission = authService.hasPermission(requiredRole)
      
      setAuthState({
        isLoading: false,
        isAuthenticated: true,
        isAuthorized: hasPermission,
        user: auth.user
      })
    }

    checkAuth()
  }, [requiredRole])

  const redirectToLogin = () => {
    router.push('/admin/login')
  }

  const redirectToHome = () => {
    router.push('/admin')
  }

  return {
    ...authState,
    redirectToLogin,
    redirectToHome
  }
}
