"use client"

import { useState } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/admin/ui/badge'
import { 
  ChevronLeft, 
  ChevronRight, 
  Search, 
  Filter,
  MoreHorizontal,
  ArrowUpDown
} from 'lucide-react'

interface Column<T> {
  key: keyof T | string
  title: string
  render?: (value: any, record: T, index: number) => React.ReactNode
  sortable?: boolean
  width?: string
  align?: 'left' | 'center' | 'right'
}

interface DataTableProps<T> {
  data: T[]
  columns: Column<T>[]
  loading?: boolean
  pagination?: {
    current: number
    pageSize: number
    total: number
    onChange: (page: number, pageSize: number) => void
  }
  searchable?: boolean
  searchPlaceholder?: string
  onSearch?: (value: string) => void
  selectable?: boolean
  selectedRowKeys?: string[]
  onSelectChange?: (selectedRowKeys: string[], selectedRows: T[]) => void
  rowKey?: keyof T | ((record: T) => string)
  actions?: {
    title: string
    render: (record: T, index: number) => React.ReactNode
  }
  className?: string
}

export function DataTable<T extends Record<string, any>>({
  data,
  columns,
  loading = false,
  pagination,
  searchable = false,
  searchPlaceholder = "搜索...",
  onSearch,
  selectable = false,
  selectedRowKeys = [],
  onSelectChange,
  rowKey = 'id',
  actions,
  className
}: DataTableProps<T>) {
  const [searchValue, setSearchValue] = useState('')
  const [sortConfig, setSortConfig] = useState<{
    key: string
    direction: 'asc' | 'desc'
  } | null>(null)

  const getRowKey = (record: T, index: number): string => {
    if (typeof rowKey === 'function') {
      return rowKey(record)
    }
    return record[rowKey] || index.toString()
  }

  const handleSort = (columnKey: string) => {
    let direction: 'asc' | 'desc' = 'asc'
    
    if (sortConfig && sortConfig.key === columnKey && sortConfig.direction === 'asc') {
      direction = 'desc'
    }
    
    setSortConfig({ key: columnKey, direction })
  }

  const handleSearch = (value: string) => {
    setSearchValue(value)
    onSearch?.(value)
  }

  const handleSelectAll = (checked: boolean) => {
    if (!onSelectChange) return
    
    if (checked) {
      const allKeys = data.map((record, index) => getRowKey(record, index))
      onSelectChange(allKeys, data)
    } else {
      onSelectChange([], [])
    }
  }

  const handleSelectRow = (record: T, index: number, checked: boolean) => {
    if (!onSelectChange) return
    
    const key = getRowKey(record, index)
    let newSelectedKeys = [...selectedRowKeys]
    let newSelectedRows = data.filter((item, idx) => 
      selectedRowKeys.includes(getRowKey(item, idx))
    )
    
    if (checked) {
      newSelectedKeys.push(key)
      newSelectedRows.push(record)
    } else {
      newSelectedKeys = newSelectedKeys.filter(k => k !== key)
      newSelectedRows = newSelectedRows.filter(item => 
        getRowKey(item, data.indexOf(item)) !== key
      )
    }
    
    onSelectChange(newSelectedKeys, newSelectedRows)
  }

  const allColumnsWithActions = actions 
    ? [...columns, { key: 'actions', title: actions.title, width: '120px', align: 'center' as const }]
    : columns

  const isAllSelected = data.length > 0 && selectedRowKeys.length === data.length
  const isIndeterminate = selectedRowKeys.length > 0 && selectedRowKeys.length < data.length

  return (
    <div className={cn("space-y-4", className)}>
      {/* 搜索和过滤 */}
      {searchable && (
        <div className="flex items-center space-x-2">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={searchPlaceholder}
              value={searchValue}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            筛选
          </Button>
        </div>
      )}

      {/* 表格 */}
      <div className="border border-border rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-muted/50">
              <tr>
                {selectable && (
                  <th className="w-12 px-4 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={isAllSelected}
                      ref={(input) => {
                        if (input) input.indeterminate = isIndeterminate
                      }}
                      onChange={(e) => handleSelectAll(e.target.checked)}
                      className="rounded border-border"
                    />
                  </th>
                )}
                {allColumnsWithActions.map((column) => (
                  <th
                    key={column.key.toString()}
                    className={cn(
                      "px-4 py-3 text-sm font-medium text-muted-foreground",
                      column.align === 'center' && "text-center",
                      column.align === 'right' && "text-right",
                      column.sortable && "cursor-pointer hover:text-foreground"
                    )}
                    style={{ width: column.width }}
                    onClick={() => column.sortable && handleSort(column.key.toString())}
                  >
                    <div className="flex items-center space-x-1">
                      <span>{column.title}</span>
                      {column.sortable && (
                        <ArrowUpDown className="h-4 w-4" />
                      )}
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {loading ? (
                <tr>
                  <td 
                    colSpan={allColumnsWithActions.length + (selectable ? 1 : 0)}
                    className="px-4 py-8 text-center text-muted-foreground"
                  >
                    <div className="flex items-center justify-center space-x-2">
                      <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                      <span>加载中...</span>
                    </div>
                  </td>
                </tr>
              ) : data.length === 0 ? (
                <tr>
                  <td 
                    colSpan={allColumnsWithActions.length + (selectable ? 1 : 0)}
                    className="px-4 py-8 text-center text-muted-foreground"
                  >
                    暂无数据
                  </td>
                </tr>
              ) : (
                data.map((record, index) => {
                  const key = getRowKey(record, index)
                  const isSelected = selectedRowKeys.includes(key)
                  
                  return (
                    <tr 
                      key={key}
                      className={cn(
                        "border-t border-border hover:bg-muted/50 transition-colors",
                        isSelected && "bg-muted/30"
                      )}
                    >
                      {selectable && (
                        <td className="px-4 py-3">
                          <input
                            type="checkbox"
                            checked={isSelected}
                            onChange={(e) => handleSelectRow(record, index, e.target.checked)}
                            className="rounded border-border"
                          />
                        </td>
                      )}
                      {columns.map((column) => (
                        <td
                          key={column.key.toString()}
                          className={cn(
                            "px-4 py-3 text-sm",
                            column.align === 'center' && "text-center",
                            column.align === 'right' && "text-right"
                          )}
                        >
                          {column.render 
                            ? column.render(record[column.key as keyof T], record, index)
                            : record[column.key as keyof T]
                          }
                        </td>
                      ))}
                      {actions && (
                        <td className="px-4 py-3 text-center">
                          {actions.render(record, index)}
                        </td>
                      )}
                    </tr>
                  )
                })
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* 分页 */}
      {pagination && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            共 {pagination.total} 条记录
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => pagination.onChange(pagination.current - 1, pagination.pageSize)}
              disabled={pagination.current <= 1}
            >
              <ChevronLeft className="h-4 w-4" />
              上一页
            </Button>
            <span className="text-sm">
              第 {pagination.current} 页，共 {Math.ceil(pagination.total / pagination.pageSize)} 页
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => pagination.onChange(pagination.current + 1, pagination.pageSize)}
              disabled={pagination.current >= Math.ceil(pagination.total / pagination.pageSize)}
            >
              下一页
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
