/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M14.828 14.828 21 21", key: "ar5fw7" }],
  ["path", { d: "M21 16v5h-5", key: "1ck2sf" }],
  ["path", { d: "m21 3-9 9-4-4-6 6", key: "1h02xo" }],
  ["path", { d: "M21 8V3h-5", key: "1qoq8a" }]
];
const TrendingUpDown = createLucideIcon("trending-up-down", __iconNode);

export { __iconNode, TrendingUpDown as default };
//# sourceMappingURL=trending-up-down.js.map
