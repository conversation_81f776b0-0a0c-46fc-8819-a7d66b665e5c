"use client"

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/admin/ui/badge'
import { 
  Calendar, 
  Clock, 
  User, 
  Phone,
  Scissors,
  Plus,
  X
} from 'lucide-react'
import { 
  appointmentStore, 
  customerStore, 
  serviceStore, 
  staffStore 
} from '@/lib/admin/storage'
import { 
  Appointment, 
  Customer, 
  Service, 
  Staff,
  AppointmentService 
} from '@/lib/types/admin'

interface AppointmentFormProps {
  appointment?: Appointment
  onSubmit?: (appointment: Appointment) => void
  onCancel?: () => void
}

export function AppointmentForm({ appointment, onSubmit, onCancel }: AppointmentFormProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [customers, setCustomers] = useState<Customer[]>([])
  const [services, setServices] = useState<Service[]>([])
  const [staff, setStaff] = useState<Staff[]>([])

  // 从URL参数获取预填信息
  const searchParams = new URLSearchParams(window.location.search)
  const prefilledDate = searchParams.get('date') || ''
  const prefilledCustomerId = searchParams.get('customerId') || ''

  const [formData, setFormData] = useState({
    customerId: appointment?.customerId || prefilledCustomerId,
    customerName: appointment?.customerName || '',
    customerPhone: appointment?.customerPhone || '',
    staffId: appointment?.staffId || '',
    date: appointment?.date || prefilledDate,
    startTime: appointment?.startTime || '',
    selectedServices: appointment?.services || [] as AppointmentService[],
    notes: appointment?.notes || ''
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [showCustomerForm, setShowCustomerForm] = useState(false)

  useEffect(() => {
    loadData()

    // 如果有预填的客户ID，自动填充客户信息
    if (prefilledCustomerId && !appointment) {
      const customer = customers.find(c => c.id === prefilledCustomerId)
      if (customer) {
        setFormData(prev => ({
          ...prev,
          customerId: customer.id,
          customerName: customer.name,
          customerPhone: customer.phone
        }))
      }
    }
  }, [prefilledCustomerId, customers])

  const loadData = () => {
    setCustomers(customerStore.getAll())
    setServices(serviceStore.getAll().filter(s => s.isActive))
    setStaff(staffStore.getAll().filter(s => s.isActive))
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.customerName.trim()) {
      newErrors.customerName = '请输入客户姓名'
    }

    if (!formData.customerPhone.trim()) {
      newErrors.customerPhone = '请输入客户电话'
    } else if (!/^1[3-9]\d{9}$/.test(formData.customerPhone)) {
      newErrors.customerPhone = '请输入有效的手机号码'
    }

    if (!formData.staffId) {
      newErrors.staffId = '请选择理发师'
    }

    if (!formData.date) {
      newErrors.date = '请选择预约日期'
    }

    if (!formData.startTime) {
      newErrors.startTime = '请选择预约时间'
    }

    if (formData.selectedServices.length === 0) {
      newErrors.services = '请至少选择一个服务项目'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const calculateTotalDuration = () => {
    return formData.selectedServices.reduce((total, service) => total + service.duration, 0)
  }

  const calculateTotalPrice = () => {
    return formData.selectedServices.reduce((total, service) => total + service.price, 0)
  }

  const calculateEndTime = () => {
    if (!formData.startTime) return ''
    
    const [hours, minutes] = formData.startTime.split(':').map(Number)
    const totalMinutes = hours * 60 + minutes + calculateTotalDuration()
    const endHours = Math.floor(totalMinutes / 60)
    const endMinutes = totalMinutes % 60
    
    return `${endHours.toString().padStart(2, '0')}:${endMinutes.toString().padStart(2, '0')}`
  }

  const handleCustomerSelect = (customer: Customer) => {
    setFormData(prev => ({
      ...prev,
      customerId: customer.id,
      customerName: customer.name,
      customerPhone: customer.phone
    }))
    setShowCustomerForm(false)
  }

  const handleServiceToggle = (service: Service) => {
    const isSelected = formData.selectedServices.some(s => s.serviceId === service.id)
    
    if (isSelected) {
      setFormData(prev => ({
        ...prev,
        selectedServices: prev.selectedServices.filter(s => s.serviceId !== service.id)
      }))
    } else {
      const appointmentService: AppointmentService = {
        serviceId: service.id,
        serviceName: service.name,
        duration: service.duration,
        price: service.price
      }
      
      setFormData(prev => ({
        ...prev,
        selectedServices: [...prev.selectedServices, appointmentService]
      }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    setLoading(true)

    try {
      // 如果是新客户，先创建客户记录
      let customerId = formData.customerId
      if (!customerId) {
        const newCustomer = customerStore.create({
          name: formData.customerName,
          phone: formData.customerPhone,
          preferences: [],
          totalVisits: 0,
          totalSpent: 0
        })
        customerId = newCustomer.id
      }

      const selectedStaff = staff.find(s => s.id === formData.staffId)
      
      const appointmentData = {
        customerId,
        customerName: formData.customerName,
        customerPhone: formData.customerPhone,
        staffId: formData.staffId,
        staffName: selectedStaff?.name || '',
        serviceIds: formData.selectedServices.map(s => s.serviceId),
        services: formData.selectedServices,
        date: formData.date,
        startTime: formData.startTime,
        endTime: calculateEndTime(),
        duration: calculateTotalDuration(),
        totalPrice: calculateTotalPrice(),
        status: 'pending' as const,
        notes: formData.notes
      }

      let result: Appointment
      if (appointment) {
        result = appointmentStore.update(appointment.id, appointmentData)!
      } else {
        result = appointmentStore.create(appointmentData)
      }

      onSubmit?.(result)
      
      if (!onSubmit) {
        router.push('/admin/appointments')
      }
    } catch (error) {
      console.error('Failed to save appointment:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* 客户信息 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold flex items-center">
          <User className="h-5 w-5 mr-2" />
          客户信息
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">客户姓名 *</label>
            <div className="relative">
              <Input
                value={formData.customerName}
                onChange={(e) => setFormData(prev => ({ ...prev, customerName: e.target.value }))}
                placeholder="请输入客户姓名"
                className={errors.customerName ? 'border-destructive' : ''}
              />
              {!showCustomerForm && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2"
                  onClick={() => setShowCustomerForm(true)}
                >
                  选择现有客户
                </Button>
              )}
            </div>
            {errors.customerName && (
              <p className="text-sm text-destructive mt-1">{errors.customerName}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">客户电话 *</label>
            <Input
              value={formData.customerPhone}
              onChange={(e) => setFormData(prev => ({ ...prev, customerPhone: e.target.value }))}
              placeholder="请输入客户电话"
              className={errors.customerPhone ? 'border-destructive' : ''}
            />
            {errors.customerPhone && (
              <p className="text-sm text-destructive mt-1">{errors.customerPhone}</p>
            )}
          </div>
        </div>

        {/* 现有客户选择 */}
        {showCustomerForm && (
          <div className="border border-border rounded-lg p-4 bg-muted/20">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium">选择现有客户</h4>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => setShowCustomerForm(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-40 overflow-y-auto">
              {customers.map(customer => (
                <button
                  key={customer.id}
                  type="button"
                  onClick={() => handleCustomerSelect(customer)}
                  className="text-left p-3 border border-border rounded-md hover:bg-accent transition-colors"
                >
                  <p className="font-medium">{customer.name}</p>
                  <p className="text-sm text-muted-foreground">{customer.phone}</p>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* 预约时间 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold flex items-center">
          <Calendar className="h-5 w-5 mr-2" />
          预约时间
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">预约日期 *</label>
            <Input
              type="date"
              value={formData.date}
              onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
              min={new Date().toISOString().split('T')[0]}
              className={errors.date ? 'border-destructive' : ''}
            />
            {errors.date && (
              <p className="text-sm text-destructive mt-1">{errors.date}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">开始时间 *</label>
            <Input
              type="time"
              value={formData.startTime}
              onChange={(e) => setFormData(prev => ({ ...prev, startTime: e.target.value }))}
              className={errors.startTime ? 'border-destructive' : ''}
            />
            {errors.startTime && (
              <p className="text-sm text-destructive mt-1">{errors.startTime}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">结束时间</label>
            <Input
              type="time"
              value={calculateEndTime()}
              disabled
              className="bg-muted"
            />
            <p className="text-xs text-muted-foreground mt-1">
              总时长: {calculateTotalDuration()} 分钟
            </p>
          </div>
        </div>
      </div>

      {/* 理发师选择 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">选择理发师 *</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          {staff.map(member => (
            <button
              key={member.id}
              type="button"
              onClick={() => setFormData(prev => ({ ...prev, staffId: member.id }))}
              className={`p-4 border rounded-lg text-left transition-colors ${
                formData.staffId === member.id
                  ? 'border-primary bg-primary/5'
                  : 'border-border hover:bg-accent'
              }`}
            >
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-accent/20 rounded-full flex items-center justify-center">
                  <span className="text-sm">✂️</span>
                </div>
                <div>
                  <p className="font-medium">{member.name}</p>
                  <p className="text-sm text-muted-foreground">{member.isManager ? '管理员' : '员工'}</p>
                </div>
              </div>
            </button>
          ))}
        </div>
        {errors.staffId && (
          <p className="text-sm text-destructive">{errors.staffId}</p>
        )}
      </div>

      {/* 服务项目选择 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold flex items-center">
          <Scissors className="h-5 w-5 mr-2" />
          服务项目 *
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {services.map(service => {
            const isSelected = formData.selectedServices.some(s => s.serviceId === service.id)
            return (
              <button
                key={service.id}
                type="button"
                onClick={() => handleServiceToggle(service)}
                className={`p-4 border rounded-lg text-left transition-colors ${
                  isSelected
                    ? 'border-primary bg-primary/5'
                    : 'border-border hover:bg-accent'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">{service.name}</p>
                    <p className="text-sm text-muted-foreground">{service.description}</p>
                    <div className="flex items-center space-x-4 mt-2">
                      <span className="text-sm text-muted-foreground">
                        <Clock className="h-3 w-3 inline mr-1" />
                        {service.duration}分钟
                      </span>
                      <span className="text-sm font-medium text-primary">
                        ¥{service.price}
                      </span>
                    </div>
                  </div>
                  {isSelected && (
                    <Badge variant="default" size="sm">已选</Badge>
                  )}
                </div>
              </button>
            )
          })}
        </div>
        
        {formData.selectedServices.length > 0 && (
          <div className="bg-muted/20 border border-border rounded-lg p-4">
            <h4 className="font-medium mb-2">已选服务</h4>
            <div className="space-y-2">
              {formData.selectedServices.map(service => (
                <div key={service.serviceId} className="flex items-center justify-between">
                  <span className="text-sm">{service.serviceName}</span>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-muted-foreground">{service.duration}分钟</span>
                    <span className="text-sm font-medium">¥{service.price}</span>
                  </div>
                </div>
              ))}
              <div className="border-t border-border pt-2 mt-2">
                <div className="flex items-center justify-between font-medium">
                  <span>总计</span>
                  <div className="flex items-center space-x-2">
                    <span>{calculateTotalDuration()}分钟</span>
                    <span className="text-primary">¥{calculateTotalPrice()}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        
        {errors.services && (
          <p className="text-sm text-destructive">{errors.services}</p>
        )}
      </div>

      {/* 备注 */}
      <div className="space-y-2">
        <label className="block text-sm font-medium">备注</label>
        <Textarea
          value={formData.notes}
          onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
          placeholder="请输入备注信息（可选）"
          rows={3}
        />
      </div>

      {/* 操作按钮 */}
      <div className="flex justify-end space-x-3 pt-6 border-t border-border">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel || (() => router.back())}
          disabled={loading}
        >
          取消
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? '保存中...' : (appointment ? '更新预约' : '创建预约')}
        </Button>
      </div>
    </form>
  )
}
