"use client"

import { <PERSON><PERSON>L<PERSON>out, PageContainer, CardContainer } from '@/components/admin/layout/admin-layout'
import { CustomerForm } from '@/components/admin/customers/customer-form'
import { UserPlus } from 'lucide-react'

export default function NewCustomerPage() {
  return (
    <AdminLayout>
      <PageContainer
        title="新增客户"
        description="添加新的客户信息"
      >
        <CardContainer
          title="客户信息"
          description="请填写客户的基本信息和服务偏好"
        >
          <CustomerForm />
        </CardContainer>
      </PageContainer>
    </AdminLayout>
  )
}
