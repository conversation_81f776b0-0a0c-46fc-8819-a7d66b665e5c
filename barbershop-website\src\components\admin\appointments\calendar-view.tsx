"use client"

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/admin/ui/badge'
import { 
  ChevronLeft, 
  ChevronRight, 
  Calendar as CalendarIcon,
  Clock,
  User,
  Plus
} from 'lucide-react'
import { appointmentStore } from '@/lib/admin/storage'
import { Appointment, AppointmentStatus } from '@/lib/types/admin'
import Link from 'next/link'

interface CalendarViewProps {
  onAppointmentClick?: (appointment: Appointment) => void
}

export function CalendarView({ onAppointmentClick }: CalendarViewProps) {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [appointments, setAppointments] = useState<Appointment[]>([])
  const [selectedDate, setSelectedDate] = useState<string | null>(null)

  useEffect(() => {
    loadAppointments()
  }, [currentDate])

  const loadAppointments = () => {
    // 获取当前月份的所有预约
    const year = currentDate.getFullYear()
    const month = currentDate.getMonth()
    const startDate = new Date(year, month, 1)
    const endDate = new Date(year, month + 1, 0)
    
    const allAppointments = appointmentStore.getAll()
    const monthAppointments = allAppointments.filter(appointment => {
      const appointmentDate = new Date(appointment.date)
      return appointmentDate >= startDate && appointmentDate <= endDate
    })
    
    setAppointments(monthAppointments)
  }

  const getDaysInMonth = () => {
    const year = currentDate.getFullYear()
    const month = currentDate.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const daysInMonth = lastDay.getDate()
    const startingDayOfWeek = firstDay.getDay()

    const days = []
    
    // 添加上个月的日期（填充）
    for (let i = startingDayOfWeek - 1; i >= 0; i--) {
      const prevDate = new Date(year, month, -i)
      days.push({
        date: prevDate,
        isCurrentMonth: false,
        dateString: prevDate.toISOString().split('T')[0]
      })
    }
    
    // 添加当前月的日期
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day)
      days.push({
        date,
        isCurrentMonth: true,
        dateString: date.toISOString().split('T')[0]
      })
    }
    
    // 添加下个月的日期（填充到42天）
    const remainingDays = 42 - days.length
    for (let day = 1; day <= remainingDays; day++) {
      const nextDate = new Date(year, month + 1, day)
      days.push({
        date: nextDate,
        isCurrentMonth: false,
        dateString: nextDate.toISOString().split('T')[0]
      })
    }
    
    return days
  }

  const getAppointmentsForDate = (dateString: string) => {
    return appointments.filter(appointment => appointment.date === dateString)
  }

  const getStatusColor = (status: AppointmentStatus) => {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      confirmed: 'bg-green-100 text-green-800 border-green-200',
      in_progress: 'bg-blue-100 text-blue-800 border-blue-200',
      completed: 'bg-gray-100 text-gray-800 border-gray-200',
      cancelled: 'bg-red-100 text-red-800 border-red-200',
      no_show: 'bg-red-100 text-red-800 border-red-200'
    }
    return colors[status] || colors.pending
  }

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev)
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1)
      } else {
        newDate.setMonth(prev.getMonth() + 1)
      }
      return newDate
    })
  }

  const goToToday = () => {
    setCurrentDate(new Date())
  }

  const formatMonth = (date: Date) => {
    return date.toLocaleDateString('zh-CN', { 
      year: 'numeric', 
      month: 'long' 
    })
  }

  const isToday = (dateString: string) => {
    const today = new Date().toISOString().split('T')[0]
    return dateString === today
  }

  const days = getDaysInMonth()
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']

  return (
    <div className="space-y-4">
      {/* 日历头部 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h2 className="text-xl font-semibold flex items-center">
            <CalendarIcon className="h-5 w-5 mr-2" />
            {formatMonth(currentDate)}
          </h2>
          <Button variant="outline" size="sm" onClick={goToToday}>
            今天
          </Button>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigateMonth('prev')}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigateMonth('next')}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* 日历网格 */}
      <div className="border border-border rounded-lg overflow-hidden bg-card">
        {/* 星期标题 */}
        <div className="grid grid-cols-7 bg-muted/50">
          {weekdays.map(weekday => (
            <div key={weekday} className="p-3 text-center text-sm font-medium text-muted-foreground border-r border-border last:border-r-0">
              {weekday}
            </div>
          ))}
        </div>

        {/* 日期网格 */}
        <div className="grid grid-cols-7">
          {days.map((day, index) => {
            const dayAppointments = getAppointmentsForDate(day.dateString)
            const isSelected = selectedDate === day.dateString
            const todayClass = isToday(day.dateString) ? 'bg-primary/5 border-primary/20' : ''
            
            return (
              <div
                key={index}
                className={`min-h-[120px] p-2 border-r border-b border-border last:border-r-0 ${
                  !day.isCurrentMonth ? 'bg-muted/20' : 'bg-card'
                } ${todayClass} ${isSelected ? 'bg-accent/20' : ''} hover:bg-accent/10 transition-colors cursor-pointer`}
                onClick={() => setSelectedDate(isSelected ? null : day.dateString)}
              >
                <div className="flex items-center justify-between mb-2">
                  <span className={`text-sm font-medium ${
                    !day.isCurrentMonth ? 'text-muted-foreground' : 
                    isToday(day.dateString) ? 'text-primary font-bold' : 'text-foreground'
                  }`}>
                    {day.date.getDate()}
                  </span>
                  
                  {day.isCurrentMonth && dayAppointments.length === 0 && (
                    <Link href={`/admin/appointments/new?date=${day.dateString}`}>
                      <Button variant="ghost" size="sm" className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 hover:opacity-100">
                        <Plus className="h-3 w-3" />
                      </Button>
                    </Link>
                  )}
                </div>

                {/* 预约列表 */}
                <div className="space-y-1">
                  {dayAppointments.slice(0, 3).map(appointment => (
                    <div
                      key={appointment.id}
                      onClick={(e) => {
                        e.stopPropagation()
                        onAppointmentClick?.(appointment)
                      }}
                      className={`text-xs p-1 rounded border cursor-pointer hover:shadow-sm transition-shadow ${getStatusColor(appointment.status)}`}
                    >
                      <div className="flex items-center justify-between">
                        <span className="font-medium truncate">
                          {appointment.startTime} {appointment.customerName}
                        </span>
                      </div>
                      <div className="truncate opacity-75">
                        {appointment.services[0]?.serviceName}
                        {appointment.services.length > 1 && ` +${appointment.services.length - 1}`}
                      </div>
                    </div>
                  ))}
                  
                  {dayAppointments.length > 3 && (
                    <div className="text-xs text-muted-foreground text-center py-1">
                      +{dayAppointments.length - 3} 更多
                    </div>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* 选中日期的详细信息 */}
      {selectedDate && (
        <div className="border border-border rounded-lg p-4 bg-card">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold">
              {new Date(selectedDate).toLocaleDateString('zh-CN', { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric',
                weekday: 'long'
              })} 的预约
            </h3>
            <Link href={`/admin/appointments/new?date=${selectedDate}`}>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                新建预约
              </Button>
            </Link>
          </div>

          <div className="space-y-3">
            {getAppointmentsForDate(selectedDate).map(appointment => (
              <div
                key={appointment.id}
                onClick={() => onAppointmentClick?.(appointment)}
                className="flex items-center justify-between p-3 border border-border rounded-lg hover:bg-accent/5 cursor-pointer transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">{appointment.startTime}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span>{appointment.customerName}</span>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {appointment.services.map(s => s.serviceName).join(', ')}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant={
                    appointment.status === 'confirmed' ? 'success' :
                    appointment.status === 'pending' ? 'warning' :
                    appointment.status === 'cancelled' ? 'destructive' : 'default'
                  } size="sm">
                    {appointment.status === 'pending' && '待确认'}
                    {appointment.status === 'confirmed' && '已确认'}
                    {appointment.status === 'in_progress' && '进行中'}
                    {appointment.status === 'completed' && '已完成'}
                    {appointment.status === 'cancelled' && '已取消'}
                    {appointment.status === 'no_show' && '未到店'}
                  </Badge>
                  <span className="text-sm font-medium text-primary">
                    ¥{appointment.totalPrice}
                  </span>
                </div>
              </div>
            ))}

            {getAppointmentsForDate(selectedDate).length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <CalendarIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>这一天还没有预约</p>
                <Link href={`/admin/appointments/new?date=${selectedDate}`}>
                  <Button className="mt-2" size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    创建预约
                  </Button>
                </Link>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
