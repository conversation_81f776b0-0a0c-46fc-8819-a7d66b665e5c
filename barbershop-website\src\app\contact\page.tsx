"use client"

import { <PERSON>ada<PERSON> } from "next"
import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"

interface FormData {
  name: string
  email: string
  phone: string
  service: string
  message: string
}

interface FormErrors {
  name?: string
  email?: string
  phone?: string
  service?: string
  message?: string
}

const services = [
  { value: "", label: "请选择服务类型" },
  { value: "haircut", label: "理发服务" },
  { value: "beard", label: "胡须造型" },
  { value: "styling", label: "造型设计" },
  { value: "wash", label: "洗发护理" },
  { value: "facial", label: "面部护理" },
  { value: "package", label: "套餐服务" },
  { value: "consultation", label: "造型咨询" },
  { value: "other", label: "其他服务" }
]

const businessHours = [
  { day: "周一", hours: "9:00 - 20:00", isOpen: true },
  { day: "周二", hours: "9:00 - 20:00", isOpen: true },
  { day: "周三", hours: "9:00 - 20:00", isOpen: true },
  { day: "周四", hours: "9:00 - 20:00", isOpen: true },
  { day: "周五", hours: "9:00 - 21:00", isOpen: true },
  { day: "周六", hours: "8:00 - 21:00", isOpen: true },
  { day: "周日", hours: "10:00 - 18:00", isOpen: true }
]

export default function ContactPage() {
  const [formData, setFormData] = useState<FormData>({
    name: "",
    email: "",
    phone: "",
    service: "",
    message: ""
  })
  
  const [errors, setErrors] = useState<FormErrors>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<"idle" | "success" | "error">("idle")

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    if (!formData.name.trim()) {
      newErrors.name = "请输入您的姓名"
    }

    if (!formData.email.trim()) {
      newErrors.email = "请输入邮箱地址"
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "请输入有效的邮箱地址"
    }

    if (!formData.phone.trim()) {
      newErrors.phone = "请输入联系电话"
    } else if (!/^1[3-9]\d{9}$/.test(formData.phone.replace(/\s|-/g, ""))) {
      newErrors.phone = "请输入有效的手机号码"
    }

    if (!formData.service) {
      newErrors.service = "请选择服务类型"
    }

    if (!formData.message.trim()) {
      newErrors.message = "请输入留言内容"
    } else if (formData.message.trim().length < 10) {
      newErrors.message = "留言内容至少需要10个字符"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    setSubmitStatus("idle")

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // In a real application, you would send the data to your backend
      console.log("Form submitted:", formData)
      
      setSubmitStatus("success")
      setFormData({
        name: "",
        email: "",
        phone: "",
        service: "",
        message: ""
      })
    } catch (error) {
      setSubmitStatus("error")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-primary to-primary/80 text-primary-foreground">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-3xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              联系我们
            </h1>
            <p className="text-xl mb-8 text-primary-foreground/90">
              有任何问题或需要预约服务？我们随时为您提供专业的咨询和服务。期待与您的交流！
            </p>
            <div className="flex flex-wrap justify-center gap-6 text-sm">
              <div className="flex items-center space-x-2">
                <span className="text-accent text-lg">📞</span>
                <span>专业咨询</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-accent text-lg">📍</span>
                <span>便利位置</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-accent text-lg">⏰</span>
                <span>灵活时间</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Form and Info Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div>
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl flex items-center space-x-2">
                    <span>📝</span>
                    <span>联系表单</span>
                  </CardTitle>
                  <p className="text-muted-foreground">
                    填写下方表单，我们会在24小时内回复您
                  </p>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Name Field */}
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium mb-2">
                        姓名 *
                      </label>
                      <input
                        type="text"
                        id="name"
                        value={formData.name}
                        onChange={(e) => handleInputChange("name", e.target.value)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary ${
                          errors.name ? "border-red-500" : "border-input"
                        }`}
                        placeholder="请输入您的姓名"
                      />
                      {errors.name && (
                        <p className="text-red-500 text-sm mt-1">{errors.name}</p>
                      )}
                    </div>

                    {/* Email Field */}
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium mb-2">
                        邮箱地址 *
                      </label>
                      <input
                        type="email"
                        id="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange("email", e.target.value)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary ${
                          errors.email ? "border-red-500" : "border-input"
                        }`}
                        placeholder="请输入您的邮箱地址"
                      />
                      {errors.email && (
                        <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                      )}
                    </div>

                    {/* Phone Field */}
                    <div>
                      <label htmlFor="phone" className="block text-sm font-medium mb-2">
                        联系电话 *
                      </label>
                      <input
                        type="tel"
                        id="phone"
                        value={formData.phone}
                        onChange={(e) => handleInputChange("phone", e.target.value)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary ${
                          errors.phone ? "border-red-500" : "border-input"
                        }`}
                        placeholder="请输入您的手机号码"
                      />
                      {errors.phone && (
                        <p className="text-red-500 text-sm mt-1">{errors.phone}</p>
                      )}
                    </div>

                    {/* Service Selection */}
                    <div>
                      <label htmlFor="service" className="block text-sm font-medium mb-2">
                        服务类型 *
                      </label>
                      <select
                        id="service"
                        value={formData.service}
                        onChange={(e) => handleInputChange("service", e.target.value)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary ${
                          errors.service ? "border-red-500" : "border-input"
                        }`}
                      >
                        {services.map((service) => (
                          <option key={service.value} value={service.value}>
                            {service.label}
                          </option>
                        ))}
                      </select>
                      {errors.service && (
                        <p className="text-red-500 text-sm mt-1">{errors.service}</p>
                      )}
                    </div>

                    {/* Message Field */}
                    <div>
                      <label htmlFor="message" className="block text-sm font-medium mb-2">
                        留言内容 *
                      </label>
                      <textarea
                        id="message"
                        rows={4}
                        value={formData.message}
                        onChange={(e) => handleInputChange("message", e.target.value)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary resize-none ${
                          errors.message ? "border-red-500" : "border-input"
                        }`}
                        placeholder="请详细描述您的需求或问题..."
                      />
                      {errors.message && (
                        <p className="text-red-500 text-sm mt-1">{errors.message}</p>
                      )}
                    </div>

                    {/* Submit Button */}
                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      className="w-full bg-primary hover:bg-primary/90 text-primary-foreground font-semibold py-3"
                    >
                      {isSubmitting ? (
                        <span className="flex items-center space-x-2">
                          <span>⏳</span>
                          <span>提交中...</span>
                        </span>
                      ) : (
                        <span className="flex items-center space-x-2">
                          <span>📤</span>
                          <span>发送消息</span>
                        </span>
                      )}
                    </Button>

                    {/* Submit Status Messages */}
                    {submitStatus === "success" && (
                      <div className="bg-green-50 border border-green-200 rounded-md p-4 text-green-800">
                        <div className="flex items-center space-x-2">
                          <span>✅</span>
                          <span>消息发送成功！我们会尽快回复您。</span>
                        </div>
                      </div>
                    )}

                    {submitStatus === "error" && (
                      <div className="bg-red-50 border border-red-200 rounded-md p-4 text-red-800">
                        <div className="flex items-center space-x-2">
                          <span>❌</span>
                          <span>发送失败，请稍后重试或直接联系我们。</span>
                        </div>
                      </div>
                    )}
                  </form>
                </CardContent>
              </Card>
            </div>

            {/* Contact Information */}
            <div className="space-y-6">
              {/* Business Hours */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-xl flex items-center space-x-2">
                    <span>⏰</span>
                    <span>营业时间</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {businessHours.map((schedule) => (
                      <div key={schedule.day} className="flex justify-between items-center">
                        <span className="font-medium">{schedule.day}</span>
                        <span className={`${schedule.isOpen ? "text-green-600" : "text-red-600"}`}>
                          {schedule.hours}
                        </span>
                      </div>
                    ))}
                  </div>
                  <div className="mt-4 p-3 bg-accent/10 rounded-md">
                    <p className="text-sm text-muted-foreground">
                      💡 建议提前预约，确保您的专属时间
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Contact Details */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-xl flex items-center space-x-2">
                    <span>📞</span>
                    <span>联系方式</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <span className="text-lg">📱</span>
                    <div>
                      <p className="font-medium">电话预约</p>
                      <p className="text-muted-foreground">+86 138-0000-0000</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className="text-lg">📧</span>
                    <div>
                      <p className="font-medium">邮箱咨询</p>
                      <p className="text-muted-foreground"><EMAIL></p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className="text-lg">💬</span>
                    <div>
                      <p className="font-medium">微信客服</p>
                      <p className="text-muted-foreground">ClassicCuts2024</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Location */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-xl flex items-center space-x-2">
                    <span>📍</span>
                    <span>店铺位置</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <p className="font-medium mb-2">Classic Cuts 理发店</p>
                    <p className="text-muted-foreground mb-4">
                      上海市黄浦区南京东路123号<br />
                      新世界大厦2楼201室
                    </p>
                  </div>
                  
                  {/* Map Placeholder */}
                  <div className="aspect-video bg-muted rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-4xl mb-2">🗺️</div>
                      <p className="text-muted-foreground">地图位置</p>
                      <p className="text-sm text-muted-foreground mt-1">
                        点击查看详细路线
                      </p>
                    </div>
                  </div>

                  {/* Transportation */}
                  <div className="space-y-3">
                    <h4 className="font-medium">🚇 交通指南</h4>
                    <div className="space-y-2 text-sm text-muted-foreground">
                      <p>• 地铁1号线、2号线人民广场站 (3号出口步行5分钟)</p>
                      <p>• 地铁8号线人民广场站 (1号出口步行3分钟)</p>
                      <p>• 公交20、37、49路人民广场站</p>
                    </div>
                  </div>

                  {/* Parking */}
                  <div className="space-y-3">
                    <h4 className="font-medium">🅿️ 停车信息</h4>
                    <div className="space-y-2 text-sm text-muted-foreground">
                      <p>• 新世界大厦地下停车场 (B1-B3层)</p>
                      <p>• 人民广场地下停车场 (步行2分钟)</p>
                      <p>• 周边路边停车位 (收费标准：10元/小时)</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              准备好开始您的造型之旅了吗？
            </h2>
            <p className="text-lg text-muted-foreground mb-8">
              立即预约或联系我们，让专业理发师为您打造完美造型
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button asChild size="lg" className="bg-primary hover:bg-primary/90 text-primary-foreground font-semibold px-8 py-3 text-lg">
                <a href="/booking">在线预约</a>
              </Button>
              <Button asChild variant="outline" size="lg" className="px-8 py-3 text-lg">
                <a href="tel:+8613800000000">电话预约</a>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
