/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/about/page";
exports.ids = ["app/about/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5Czhaosihao%5CDesktop%5Cpython_study%5Ctony_project%5Cbarbershop-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Czhaosihao%5CDesktop%5Cpython_study%5Ctony_project%5Cbarbershop-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5Czhaosihao%5CDesktop%5Cpython_study%5Ctony_project%5Cbarbershop-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Czhaosihao%5CDesktop%5Cpython_study%5Ctony_project%5Cbarbershop-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/about/page.tsx */ \"(rsc)/./src/app/about/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'about',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\about\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\about\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/about/page\",\n        pathname: \"/about\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5Czhaosihao%5CDesktop%5Cpython_study%5Ctony_project%5Cbarbershop-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Czhaosihao%5CDesktop%5Cpython_study%5Ctony_project%5Cbarbershop-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3poYW9zaWhhbyU1QyU1Q0Rlc2t0b3AlNUMlNUNweXRob25fc3R1ZHklNUMlNUN0b255X3Byb2plY3QlNUMlNUNiYXJiZXJzaG9wLXdlYnNpdGUlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUN6aGFvc2loYW8lNUMlNUNEZXNrdG9wJTVDJTVDcHl0aG9uX3N0dWR5JTVDJTVDdG9ueV9wcm9qZWN0JTVDJTVDYmFyYmVyc2hvcC13ZWJzaXRlJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDemhhb3NpaGFvJTVDJTVDRGVza3RvcCU1QyU1Q3B5dGhvbl9zdHVkeSU1QyU1Q3RvbnlfcHJvamVjdCU1QyU1Q2JhcmJlcnNob3Atd2Vic2l0ZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3poYW9zaWhhbyU1QyU1Q0Rlc2t0b3AlNUMlNUNweXRob25fc3R1ZHklNUMlNUN0b255X3Byb2plY3QlNUMlNUNiYXJiZXJzaG9wLXdlYnNpdGUlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNodHRwLWFjY2Vzcy1mYWxsYmFjayU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3poYW9zaWhhbyU1QyU1Q0Rlc2t0b3AlNUMlNUNweXRob25fc3R1ZHklNUMlNUN0b255X3Byb2plY3QlNUMlNUNiYXJiZXJzaG9wLXdlYnNpdGUlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3poYW9zaWhhbyU1QyU1Q0Rlc2t0b3AlNUMlNUNweXRob25fc3R1ZHklNUMlNUN0b255X3Byb2plY3QlNUMlNUNiYXJiZXJzaG9wLXdlYnNpdGUlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3poYW9zaWhhbyU1QyU1Q0Rlc2t0b3AlNUMlNUNweXRob25fc3R1ZHklNUMlNUN0b255X3Byb2plY3QlNUMlNUNiYXJiZXJzaG9wLXdlYnNpdGUlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q21ldGFkYXRhLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3poYW9zaWhhbyU1QyU1Q0Rlc2t0b3AlNUMlNUNweXRob25fc3R1ZHklNUMlNUN0b255X3Byb2plY3QlNUMlNUNiYXJiZXJzaG9wLXdlYnNpdGUlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvT0FBZ0w7QUFDaEw7QUFDQSwwT0FBbUw7QUFDbkw7QUFDQSwwT0FBbUw7QUFDbkw7QUFDQSxvUkFBeU07QUFDek07QUFDQSx3T0FBa0w7QUFDbEw7QUFDQSw0UEFBNkw7QUFDN0w7QUFDQSxrUUFBZ007QUFDaE07QUFDQSxzUUFBaU0iLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHpoYW9zaWhhb1xcXFxEZXNrdG9wXFxcXHB5dGhvbl9zdHVkeVxcXFx0b255X3Byb2plY3RcXFxcYmFyYmVyc2hvcC13ZWJzaXRlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXBhZ2UuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHpoYW9zaWhhb1xcXFxEZXNrdG9wXFxcXHB5dGhvbl9zdHVkeVxcXFx0b255X3Byb2plY3RcXFxcYmFyYmVyc2hvcC13ZWJzaXRlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXNlZ21lbnQuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHpoYW9zaWhhb1xcXFxEZXNrdG9wXFxcXHB5dGhvbl9zdHVkeVxcXFx0b255X3Byb2plY3RcXFxcYmFyYmVyc2hvcC13ZWJzaXRlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHpoYW9zaWhhb1xcXFxEZXNrdG9wXFxcXHB5dGhvbl9zdHVkeVxcXFx0b255X3Byb2plY3RcXFxcYmFyYmVyc2hvcC13ZWJzaXRlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcaHR0cC1hY2Nlc3MtZmFsbGJhY2tcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHpoYW9zaWhhb1xcXFxEZXNrdG9wXFxcXHB5dGhvbl9zdHVkeVxcXFx0b255X3Byb2plY3RcXFxcYmFyYmVyc2hvcC13ZWJzaXRlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0LXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcemhhb3NpaGFvXFxcXERlc2t0b3BcXFxccHl0aG9uX3N0dWR5XFxcXHRvbnlfcHJvamVjdFxcXFxiYXJiZXJzaG9wLXdlYnNpdGVcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxhc3luYy1tZXRhZGF0YS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcemhhb3NpaGFvXFxcXERlc2t0b3BcXFxccHl0aG9uX3N0dWR5XFxcXHRvbnlfcHJvamVjdFxcXFxiYXJiZXJzaG9wLXdlYnNpdGVcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxtZXRhZGF0YS1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcemhhb3NpaGFvXFxcXERlc2t0b3BcXFxccHl0aG9uX3N0dWR5XFxcXHRvbnlfcHJvamVjdFxcXFxiYXJiZXJzaG9wLXdlYnNpdGVcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Caccessibility%5C%5Cskip-links.tsx%22%2C%22ids%22%3A%5B%22SkipLinks%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5Cweb-vitals.tsx%22%2C%22ids%22%3A%5B%22PerformanceMonitor%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Caccessibility%5C%5Cskip-links.tsx%22%2C%22ids%22%3A%5B%22SkipLinks%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5Cweb-vitals.tsx%22%2C%22ids%22%3A%5B%22PerformanceMonitor%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(rsc)/./node_modules/next/dist/client/script.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/accessibility/skip-links.tsx */ \"(rsc)/./src/components/accessibility/skip-links.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/performance/web-vitals.tsx */ \"(rsc)/./src/components/performance/web-vitals.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Caccessibility%5C%5Cskip-links.tsx%22%2C%22ids%22%3A%5B%22SkipLinks%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5Cweb-vitals.tsx%22%2C%22ids%22%3A%5B%22PerformanceMonitor%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Cpages%5C%5Cabout-page-content.tsx%22%2C%22ids%22%3A%5B%22AboutPageContent%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Cpages%5C%5Cabout-page-content.tsx%22%2C%22ids%22%3A%5B%22AboutPageContent%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(rsc)/./node_modules/next/dist/client/script.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/pages/about-page-content.tsx */ \"(rsc)/./src/components/pages/about-page-content.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3poYW9zaWhhbyU1QyU1Q0Rlc2t0b3AlNUMlNUNweXRob25fc3R1ZHklNUMlNUN0b255X3Byb2plY3QlNUMlNUNiYXJiZXJzaG9wLXdlYnNpdGUlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q3NjcmlwdC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDemhhb3NpaGFvJTVDJTVDRGVza3RvcCU1QyU1Q3B5dGhvbl9zdHVkeSU1QyU1Q3RvbnlfcHJvamVjdCU1QyU1Q2JhcmJlcnNob3Atd2Vic2l0ZSU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNwYWdlcyU1QyU1Q2Fib3V0LXBhZ2UtY29udGVudC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJBYm91dFBhZ2VDb250ZW50JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvTUFBK0o7QUFDL0o7QUFDQSxzTUFBd00iLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHpoYW9zaWhhb1xcXFxEZXNrdG9wXFxcXHB5dGhvbl9zdHVkeVxcXFx0b255X3Byb2plY3RcXFxcYmFyYmVyc2hvcC13ZWJzaXRlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXHNjcmlwdC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQWJvdXRQYWdlQ29udGVudFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXHpoYW9zaWhhb1xcXFxEZXNrdG9wXFxcXHB5dGhvbl9zdHVkeVxcXFx0b255X3Byb2plY3RcXFxcYmFyYmVyc2hvcC13ZWJzaXRlXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHBhZ2VzXFxcXGFib3V0LXBhZ2UtY29udGVudC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Cpages%5C%5Cabout-page-content.tsx%22%2C%22ids%22%3A%5B%22AboutPageContent%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcemhhb3NpaGFvXFxEZXNrdG9wXFxweXRob25fc3R1ZHlcXHRvbnlfcHJvamVjdFxcYmFyYmVyc2hvcC13ZWJzaXRlXFxzcmNcXGFwcFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/about/page.tsx":
/*!********************************!*\
  !*** ./src/app/about/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AboutPage),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_pages_about_page_content__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/pages/about-page-content */ \"(rsc)/./src/components/pages/about-page-content.tsx\");\n/* harmony import */ var _components_seo_meta_tags__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/seo/meta-tags */ \"(rsc)/./src/components/seo/meta-tags.tsx\");\n/* harmony import */ var _components_seo_structured_data__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/seo/structured-data */ \"(rsc)/./src/components/seo/structured-data.tsx\");\n\n\n\n\nconst metadata = (0,_components_seo_meta_tags__WEBPACK_IMPORTED_MODULE_2__.generateMetadata)(_components_seo_meta_tags__WEBPACK_IMPORTED_MODULE_2__.pageSEO.about);\nfunction AboutPage() {\n    const breadcrumbItems = [\n        {\n            name: \"首页\",\n            url: \"https://tonys-barbershop.com/\"\n        },\n        {\n            name: \"关于我们\",\n            url: \"https://tonys-barbershop.com/about\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pages_about_page_content__WEBPACK_IMPORTED_MODULE_1__.AboutPageContent, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seo_structured_data__WEBPACK_IMPORTED_MODULE_3__.BreadcrumbStructuredData, {\n                items: breadcrumbItems\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\about\\\\page.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2Fib3V0L3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQXdFO0FBQ0Y7QUFDSztBQUVwRSxNQUFNSSxXQUFXSCwyRUFBZ0JBLENBQUNDLDhEQUFPQSxDQUFDRyxLQUFLLEVBQUM7QUFFeEMsU0FBU0M7SUFDdEIsTUFBTUMsa0JBQWtCO1FBQ3RCO1lBQUVDLE1BQU07WUFBTUMsS0FBSztRQUFnQztRQUNuRDtZQUFFRCxNQUFNO1lBQVFDLEtBQUs7UUFBcUM7S0FDM0Q7SUFFRCxxQkFDRTs7MEJBQ0UsOERBQUNULGtGQUFnQkE7Ozs7OzBCQUNqQiw4REFBQ0cscUZBQXdCQTtnQkFBQ08sT0FBT0g7Ozs7Ozs7O0FBR3ZDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHpoYW9zaWhhb1xcRGVza3RvcFxccHl0aG9uX3N0dWR5XFx0b255X3Byb2plY3RcXGJhcmJlcnNob3Atd2Vic2l0ZVxcc3JjXFxhcHBcXGFib3V0XFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBYm91dFBhZ2VDb250ZW50IH0gZnJvbSBcIkAvY29tcG9uZW50cy9wYWdlcy9hYm91dC1wYWdlLWNvbnRlbnRcIlxuaW1wb3J0IHsgZ2VuZXJhdGVNZXRhZGF0YSwgcGFnZVNFTyB9IGZyb20gXCJAL2NvbXBvbmVudHMvc2VvL21ldGEtdGFnc1wiXG5pbXBvcnQgeyBCcmVhZGNydW1iU3RydWN0dXJlZERhdGEgfSBmcm9tIFwiQC9jb21wb25lbnRzL3Nlby9zdHJ1Y3R1cmVkLWRhdGFcIlxuXG5leHBvcnQgY29uc3QgbWV0YWRhdGEgPSBnZW5lcmF0ZU1ldGFkYXRhKHBhZ2VTRU8uYWJvdXQpXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFib3V0UGFnZSgpIHtcbiAgY29uc3QgYnJlYWRjcnVtYkl0ZW1zID0gW1xuICAgIHsgbmFtZTogXCLpppbpobVcIiwgdXJsOiBcImh0dHBzOi8vdG9ueXMtYmFyYmVyc2hvcC5jb20vXCIgfSxcbiAgICB7IG5hbWU6IFwi5YWz5LqO5oiR5LusXCIsIHVybDogXCJodHRwczovL3RvbnlzLWJhcmJlcnNob3AuY29tL2Fib3V0XCIgfVxuICBdXG5cbiAgcmV0dXJuIChcbiAgICA8PlxuICAgICAgPEFib3V0UGFnZUNvbnRlbnQgLz5cbiAgICAgIDxCcmVhZGNydW1iU3RydWN0dXJlZERhdGEgaXRlbXM9e2JyZWFkY3J1bWJJdGVtc30gLz5cbiAgICA8Lz5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkFib3V0UGFnZUNvbnRlbnQiLCJnZW5lcmF0ZU1ldGFkYXRhIiwicGFnZVNFTyIsIkJyZWFkY3J1bWJTdHJ1Y3R1cmVkRGF0YSIsIm1ldGFkYXRhIiwiYWJvdXQiLCJBYm91dFBhZ2UiLCJicmVhZGNydW1iSXRlbXMiLCJuYW1lIiwidXJsIiwiaXRlbXMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/about/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"4d4854800f1b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHpoYW9zaWhhb1xcRGVza3RvcFxccHl0aG9uX3N0dWR5XFx0b255X3Byb2plY3RcXGJhcmJlcnNob3Atd2Vic2l0ZVxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNGQ0ODU0ODAwZjFiXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_geist_sans_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_geist_sans_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_geist_sans_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_variable_font_geist_mono_subsets_latin_display_swap_variableName_playfair___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Playfair_Display\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"playfair\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Playfair_Display\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"playfair\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_variable_font_geist_mono_subsets_latin_display_swap_variableName_playfair___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_variable_font_geist_mono_subsets_latin_display_swap_variableName_playfair___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_accessibility_skip_links__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/accessibility/skip-links */ \"(rsc)/./src/components/accessibility/skip-links.tsx\");\n/* harmony import */ var _components_seo_structured_data__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/seo/structured-data */ \"(rsc)/./src/components/seo/structured-data.tsx\");\n/* harmony import */ var _components_performance_web_vitals__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/performance/web-vitals */ \"(rsc)/./src/components/performance/web-vitals.tsx\");\n/* harmony import */ var _components_seo_meta_tags__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/seo/meta-tags */ \"(rsc)/./src/components/seo/meta-tags.tsx\");\n\n\n\n\n\n\n\n\nconst metadata = (0,_components_seo_meta_tags__WEBPACK_IMPORTED_MODULE_5__.generateMetadata)({\n    title: \"Tony's Barbershop - 专业理发店\",\n    description: \"Tony's Barbershop - 专业理发店，提供传统理发、现代造型、胡须修剪等服务。技术精湛，服务周到，环境舒适。\",\n    keywords: [\n        \"理发店\",\n        \"专业理发\",\n        \"上海理发店\",\n        \"Tony's Barbershop\",\n        \"理发师\",\n        \"造型设计\",\n        \"胡须修剪\"\n    ],\n    url: \"/\",\n    type: \"website\"\n});\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        className: \"scroll-smooth\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/favicon-32x32.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/favicon-16x16.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/site.webmanifest\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#000000\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//fonts.gstatic.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_geist_sans_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Playfair_Display_arguments_variable_font_geist_mono_subsets_latin_display_swap_variableName_playfair___WEBPACK_IMPORTED_MODULE_7___default().variable)} antialiased min-h-screen bg-background text-foreground`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_accessibility_skip_links__WEBPACK_IMPORTED_MODULE_2__.SkipLinks, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex min-h-screen flex-col\",\n                        id: \"root\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            id: \"main-content\",\n                            className: \"flex-1\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seo_structured_data__WEBPACK_IMPORTED_MODULE_3__.BusinessStructuredData, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_performance_web_vitals__WEBPACK_IMPORTED_MODULE_4__.PerformanceMonitor, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/accessibility/skip-links.tsx":
/*!*****************************************************!*\
  !*** ./src/components/accessibility/skip-links.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AccessibleButton: () => (/* binding */ AccessibleButton),
/* harmony export */   AccessibleInput: () => (/* binding */ AccessibleInput),
/* harmony export */   AccessibleLabel: () => (/* binding */ AccessibleLabel),
/* harmony export */   AccessibleLink: () => (/* binding */ AccessibleLink),
/* harmony export */   ScreenReaderOnly: () => (/* binding */ ScreenReaderOnly),
/* harmony export */   SkipLink: () => (/* binding */ SkipLink),
/* harmony export */   SkipLinks: () => (/* binding */ SkipLinks),
/* harmony export */   useFocusManagement: () => (/* binding */ useFocusManagement)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const SkipLink = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SkipLink() from the server but SkipLink is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\accessibility\\skip-links.tsx",
"SkipLink",
);const SkipLinks = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SkipLinks() from the server but SkipLinks is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\accessibility\\skip-links.tsx",
"SkipLinks",
);const ScreenReaderOnly = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ScreenReaderOnly() from the server but ScreenReaderOnly is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\accessibility\\skip-links.tsx",
"ScreenReaderOnly",
);const AccessibleButton = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AccessibleButton() from the server but AccessibleButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\accessibility\\skip-links.tsx",
"AccessibleButton",
);const AccessibleLink = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AccessibleLink() from the server but AccessibleLink is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\accessibility\\skip-links.tsx",
"AccessibleLink",
);const AccessibleLabel = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AccessibleLabel() from the server but AccessibleLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\accessibility\\skip-links.tsx",
"AccessibleLabel",
);const AccessibleInput = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AccessibleInput() from the server but AccessibleInput is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\accessibility\\skip-links.tsx",
"AccessibleInput",
);const useFocusManagement = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useFocusManagement() from the server but useFocusManagement is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\accessibility\\skip-links.tsx",
"useFocusManagement",
);

/***/ }),

/***/ "(rsc)/./src/components/pages/about-page-content.tsx":
/*!*****************************************************!*\
  !*** ./src/components/pages/about-page-content.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AboutPageContent: () => (/* binding */ AboutPageContent)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AboutPageContent = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AboutPageContent() from the server but AboutPageContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\pages\\about-page-content.tsx",
"AboutPageContent",
);

/***/ }),

/***/ "(rsc)/./src/components/performance/web-vitals.tsx":
/*!***************************************************!*\
  !*** ./src/components/performance/web-vitals.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   PerformanceMonitor: () => (/* binding */ PerformanceMonitor),
/* harmony export */   WebVitals: () => (/* binding */ WebVitals),
/* harmony export */   useErrorMonitoring: () => (/* binding */ useErrorMonitoring),
/* harmony export */   useImageLoadingMonitoring: () => (/* binding */ useImageLoadingMonitoring),
/* harmony export */   usePerformanceMonitoring: () => (/* binding */ usePerformanceMonitoring),
/* harmony export */   useUserExperienceMonitoring: () => (/* binding */ useUserExperienceMonitoring)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const WebVitals = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call WebVitals() from the server but WebVitals is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\performance\\web-vitals.tsx",
"WebVitals",
);const usePerformanceMonitoring = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call usePerformanceMonitoring() from the server but usePerformanceMonitoring is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\performance\\web-vitals.tsx",
"usePerformanceMonitoring",
);const useImageLoadingMonitoring = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useImageLoadingMonitoring() from the server but useImageLoadingMonitoring is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\performance\\web-vitals.tsx",
"useImageLoadingMonitoring",
);const useErrorMonitoring = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useErrorMonitoring() from the server but useErrorMonitoring is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\performance\\web-vitals.tsx",
"useErrorMonitoring",
);const useUserExperienceMonitoring = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useUserExperienceMonitoring() from the server but useUserExperienceMonitoring is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\performance\\web-vitals.tsx",
"useUserExperienceMonitoring",
);const PerformanceMonitor = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call PerformanceMonitor() from the server but PerformanceMonitor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\performance\\web-vitals.tsx",
"PerformanceMonitor",
);

/***/ }),

/***/ "(rsc)/./src/components/seo/meta-tags.tsx":
/*!******************************************!*\
  !*** ./src/components/seo/meta-tags.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateJSONLD: () => (/* binding */ generateJSONLD),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata),\n/* harmony export */   pageSEO: () => (/* binding */ pageSEO)\n/* harmony export */ });\n// 基础SEO配置\nconst defaultSEO = {\n    siteName: \"Tony's Barbershop\",\n    locale: 'zh_CN',\n    baseUrl: 'https://tonys-barbershop.com',\n    defaultImage: '/images/og-image.jpg',\n    defaultDescription: '专业理发店，提供传统理发、现代造型、胡须修剪等服务。技术精湛，服务周到，环境舒适。',\n    defaultKeywords: [\n        '理发店',\n        '理发',\n        '造型',\n        '胡须修剪',\n        '专业理发师',\n        '上海理发店',\n        'Tony\\'s Barbershop'\n    ]\n};\n// 生成页面元数据\nfunction generateMetadata({ title, description = defaultSEO.defaultDescription, keywords = defaultSEO.defaultKeywords, image = defaultSEO.defaultImage, url, type = 'website', locale = defaultSEO.locale, siteName = defaultSEO.siteName } = {}) {\n    const fullTitle = title ? `${title} | ${siteName}` : siteName;\n    const fullUrl = url ? `${defaultSEO.baseUrl}${url}` : defaultSEO.baseUrl;\n    const fullImage = image.startsWith('http') ? image : `${defaultSEO.baseUrl}${image}`;\n    return {\n        title: fullTitle,\n        description,\n        keywords: keywords.join(', '),\n        // Open Graph\n        openGraph: {\n            title: fullTitle,\n            description,\n            url: fullUrl,\n            siteName,\n            images: [\n                {\n                    url: fullImage,\n                    width: 1200,\n                    height: 630,\n                    alt: title || siteName\n                }\n            ],\n            locale,\n            type\n        },\n        // Twitter Card\n        twitter: {\n            card: 'summary_large_image',\n            title: fullTitle,\n            description,\n            images: [\n                fullImage\n            ],\n            creator: '@tonys_barbershop',\n            site: '@tonys_barbershop'\n        },\n        // 其他元标签\n        robots: {\n            index: true,\n            follow: true,\n            googleBot: {\n                index: true,\n                follow: true,\n                'max-video-preview': -1,\n                'max-image-preview': 'large',\n                'max-snippet': -1\n            }\n        },\n        // 规范链接\n        alternates: {\n            canonical: fullUrl,\n            languages: {\n                'zh-CN': fullUrl,\n                'en': `${fullUrl}/en`\n            }\n        },\n        // 应用信息\n        applicationName: siteName,\n        generator: 'Next.js',\n        referrer: 'origin-when-cross-origin',\n        // 作者和版权\n        authors: [\n            {\n                name: 'Tony\\'s Barbershop'\n            }\n        ],\n        creator: 'Tony\\'s Barbershop',\n        publisher: 'Tony\\'s Barbershop',\n        // 格式检测\n        formatDetection: {\n            email: false,\n            address: false,\n            telephone: false\n        },\n        // 验证标签\n        verification: {\n            google: 'google-site-verification-code',\n            yandex: 'yandex-verification-code',\n            yahoo: 'yahoo-site-verification-code'\n        },\n        // 其他\n        category: 'business'\n    };\n}\n// 页面特定的SEO配置\nconst pageSEO = {\n    home: {\n        title: '首页',\n        description: 'Tony\\'s Barbershop - 专业理发店，提供传统理发、现代造型、胡须修剪等服务。技术精湛，服务周到，环境舒适。',\n        keywords: [\n            '理发店',\n            '专业理发',\n            '上海理发店',\n            'Tony\\'s Barbershop',\n            '理发师',\n            '造型设计'\n        ],\n        url: '/'\n    },\n    services: {\n        title: '服务项目',\n        description: '查看我们的专业理发服务：经典理发、时尚造型、胡须修剪、洗剪吹等。价格透明，技术精湛。',\n        keywords: [\n            '理发服务',\n            '理发价格',\n            '造型设计',\n            '胡须修剪',\n            '洗剪吹',\n            '专业理发'\n        ],\n        url: '/services'\n    },\n    about: {\n        title: '关于我们',\n        description: '了解Tony\\'s Barbershop的历史、理念和专业团队。我们致力于为每位客户提供最优质的理发服务。',\n        keywords: [\n            '理发店历史',\n            '专业团队',\n            '理发师介绍',\n            '服务理念',\n            'Tony\\'s Barbershop'\n        ],\n        url: '/about'\n    },\n    gallery: {\n        title: '作品展示',\n        description: '欣赏我们的理发作品集，包括各种发型设计、胡须造型等。展现我们的专业技艺和创意。',\n        keywords: [\n            '理发作品',\n            '发型设计',\n            '胡须造型',\n            '理发案例',\n            '造型展示'\n        ],\n        url: '/gallery'\n    },\n    contact: {\n        title: '联系我们',\n        description: '联系Tony\\'s Barbershop预约服务。地址：上海南京路123号，电话：138-0000-0000。',\n        keywords: [\n            '理发店联系方式',\n            '预约理发',\n            '理发店地址',\n            '营业时间',\n            '联系电话'\n        ],\n        url: '/contact'\n    },\n    booking: {\n        title: '在线预约',\n        description: '在线预约Tony\\'s Barbershop的理发服务。选择服务项目、理发师和时间，享受便捷的预约体验。',\n        keywords: [\n            '在线预约',\n            '理发预约',\n            '预约系统',\n            '理发师预约',\n            '服务预约'\n        ],\n        url: '/booking'\n    }\n};\n// JSON-LD 结构化数据生成器\nfunction generateJSONLD(type, data) {\n    return {\n        __html: JSON.stringify({\n            '@context': 'https://schema.org',\n            '@type': type,\n            ...data\n        })\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/seo/meta-tags.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/seo/structured-data.tsx":
/*!************************************************!*\
  !*** ./src/components/seo/structured-data.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BreadcrumbStructuredData: () => (/* binding */ BreadcrumbStructuredData),\n/* harmony export */   BusinessStructuredData: () => (/* binding */ BusinessStructuredData),\n/* harmony export */   FAQStructuredData: () => (/* binding */ FAQStructuredData),\n/* harmony export */   ServicesStructuredData: () => (/* binding */ ServicesStructuredData)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/script */ \"(rsc)/./node_modules/next/dist/api/script.js\");\n\n\n// 理发店基本信息的结构化数据\nfunction BusinessStructuredData() {\n    const businessData = {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"HairSalon\",\n        \"name\": \"Tony's Barbershop\",\n        \"description\": \"专业理发店，提供传统理发、现代造型、胡须修剪等服务\",\n        \"url\": \"https://tonys-barbershop.com\",\n        \"telephone\": \"+86-138-0000-0000\",\n        \"email\": \"<EMAIL>\",\n        \"address\": {\n            \"@type\": \"PostalAddress\",\n            \"streetAddress\": \"南京路123号\",\n            \"addressLocality\": \"上海\",\n            \"addressRegion\": \"上海市\",\n            \"postalCode\": \"200001\",\n            \"addressCountry\": \"CN\"\n        },\n        \"geo\": {\n            \"@type\": \"GeoCoordinates\",\n            \"latitude\": 31.2304,\n            \"longitude\": 121.4737\n        },\n        \"openingHours\": [\n            \"Mo-Fr 09:00-20:00\",\n            \"Sa-Su 10:00-18:00\"\n        ],\n        \"priceRange\": \"¥¥\",\n        \"paymentAccepted\": [\n            \"现金\",\n            \"支付宝\",\n            \"微信支付\",\n            \"银行卡\"\n        ],\n        \"currenciesAccepted\": \"CNY\",\n        \"hasMap\": \"https://maps.google.com/?q=31.2304,121.4737\",\n        \"image\": [\n            \"https://tonys-barbershop.com/images/storefront.jpg\",\n            \"https://tonys-barbershop.com/images/interior.jpg\"\n        ],\n        \"logo\": \"https://tonys-barbershop.com/images/logo.png\",\n        \"sameAs\": [\n            \"https://www.facebook.com/tonys-barbershop\",\n            \"https://www.instagram.com/tonys-barbershop\",\n            \"https://weibo.com/tonys-barbershop\"\n        ],\n        \"aggregateRating\": {\n            \"@type\": \"AggregateRating\",\n            \"ratingValue\": \"4.8\",\n            \"reviewCount\": \"127\",\n            \"bestRating\": \"5\",\n            \"worstRating\": \"1\"\n        },\n        \"review\": [\n            {\n                \"@type\": \"Review\",\n                \"author\": {\n                    \"@type\": \"Person\",\n                    \"name\": \"张先生\"\n                },\n                \"reviewRating\": {\n                    \"@type\": \"Rating\",\n                    \"ratingValue\": \"5\",\n                    \"bestRating\": \"5\"\n                },\n                \"reviewBody\": \"技术精湛，服务周到，环境舒适。强烈推荐！\"\n            }\n        ]\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        id: \"business-structured-data\",\n        type: \"application/ld+json\",\n        dangerouslySetInnerHTML: {\n            __html: JSON.stringify(businessData)\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\seo\\\\structured-data.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, this);\n}\n// 服务页面的结构化数据\nfunction ServicesStructuredData() {\n    const servicesData = {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"Service\",\n        \"serviceType\": \"理发服务\",\n        \"provider\": {\n            \"@type\": \"HairSalon\",\n            \"name\": \"Tony's Barbershop\"\n        },\n        \"hasOfferCatalog\": {\n            \"@type\": \"OfferCatalog\",\n            \"name\": \"理发服务目录\",\n            \"itemListElement\": [\n                {\n                    \"@type\": \"Offer\",\n                    \"itemOffered\": {\n                        \"@type\": \"Service\",\n                        \"name\": \"经典理发\",\n                        \"description\": \"传统理发技艺，适合商务人士\"\n                    },\n                    \"price\": \"80\",\n                    \"priceCurrency\": \"CNY\"\n                },\n                {\n                    \"@type\": \"Offer\",\n                    \"itemOffered\": {\n                        \"@type\": \"Service\",\n                        \"name\": \"时尚造型\",\n                        \"description\": \"现代时尚发型设计\"\n                    },\n                    \"price\": \"120\",\n                    \"priceCurrency\": \"CNY\"\n                },\n                {\n                    \"@type\": \"Offer\",\n                    \"itemOffered\": {\n                        \"@type\": \"Service\",\n                        \"name\": \"胡须修剪\",\n                        \"description\": \"专业胡须造型设计\"\n                    },\n                    \"price\": \"60\",\n                    \"priceCurrency\": \"CNY\"\n                }\n            ]\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        id: \"services-structured-data\",\n        type: \"application/ld+json\",\n        dangerouslySetInnerHTML: {\n            __html: JSON.stringify(servicesData)\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\seo\\\\structured-data.tsx\",\n        lineNumber: 128,\n        columnNumber: 5\n    }, this);\n}\n// 面包屑导航结构化数据\nfunction BreadcrumbStructuredData({ items }) {\n    const breadcrumbData = {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"BreadcrumbList\",\n        \"itemListElement\": items.map((item, index)=>({\n                \"@type\": \"ListItem\",\n                \"position\": index + 1,\n                \"name\": item.name,\n                \"item\": item.url\n            }))\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        id: \"breadcrumb-structured-data\",\n        type: \"application/ld+json\",\n        dangerouslySetInnerHTML: {\n            __html: JSON.stringify(breadcrumbData)\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\seo\\\\structured-data.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n// 常见问题结构化数据\nfunction FAQStructuredData() {\n    const faqData = {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"FAQPage\",\n        \"mainEntity\": [\n            {\n                \"@type\": \"Question\",\n                \"name\": \"需要预约吗？\",\n                \"acceptedAnswer\": {\n                    \"@type\": \"Answer\",\n                    \"text\": \"建议提前预约以确保服务时间，我们也接受现场排队。\"\n                }\n            },\n            {\n                \"@type\": \"Question\",\n                \"name\": \"营业时间是什么？\",\n                \"acceptedAnswer\": {\n                    \"@type\": \"Answer\",\n                    \"text\": \"周一至周五：9:00-20:00，周六至周日：10:00-18:00\"\n                }\n            },\n            {\n                \"@type\": \"Question\",\n                \"name\": \"支持哪些支付方式？\",\n                \"acceptedAnswer\": {\n                    \"@type\": \"Answer\",\n                    \"text\": \"我们支持现金、支付宝、微信支付和银行卡支付。\"\n                }\n            }\n        ]\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        id: \"faq-structured-data\",\n        type: \"application/ld+json\",\n        dangerouslySetInnerHTML: {\n            __html: JSON.stringify(faqData)\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\seo\\\\structured-data.tsx\",\n        lineNumber: 196,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/seo/structured-data.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Caccessibility%5C%5Cskip-links.tsx%22%2C%22ids%22%3A%5B%22SkipLinks%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5Cweb-vitals.tsx%22%2C%22ids%22%3A%5B%22PerformanceMonitor%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Caccessibility%5C%5Cskip-links.tsx%22%2C%22ids%22%3A%5B%22SkipLinks%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5Cweb-vitals.tsx%22%2C%22ids%22%3A%5B%22PerformanceMonitor%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(ssr)/./node_modules/next/dist/client/script.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/accessibility/skip-links.tsx */ \"(ssr)/./src/components/accessibility/skip-links.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/performance/web-vitals.tsx */ \"(ssr)/./src/components/performance/web-vitals.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Playfair_Display%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22playfair%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Caccessibility%5C%5Cskip-links.tsx%22%2C%22ids%22%3A%5B%22SkipLinks%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5Cweb-vitals.tsx%22%2C%22ids%22%3A%5B%22PerformanceMonitor%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Cpages%5C%5Cabout-page-content.tsx%22%2C%22ids%22%3A%5B%22AboutPageContent%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Cpages%5C%5Cabout-page-content.tsx%22%2C%22ids%22%3A%5B%22AboutPageContent%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(ssr)/./node_modules/next/dist/client/script.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/pages/about-page-content.tsx */ \"(ssr)/./src/components/pages/about-page-content.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3poYW9zaWhhbyU1QyU1Q0Rlc2t0b3AlNUMlNUNweXRob25fc3R1ZHklNUMlNUN0b255X3Byb2plY3QlNUMlNUNiYXJiZXJzaG9wLXdlYnNpdGUlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q3NjcmlwdC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDemhhb3NpaGFvJTVDJTVDRGVza3RvcCU1QyU1Q3B5dGhvbl9zdHVkeSU1QyU1Q3RvbnlfcHJvamVjdCU1QyU1Q2JhcmJlcnNob3Atd2Vic2l0ZSU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNwYWdlcyU1QyU1Q2Fib3V0LXBhZ2UtY29udGVudC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJBYm91dFBhZ2VDb250ZW50JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvTUFBK0o7QUFDL0o7QUFDQSxzTUFBd00iLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHpoYW9zaWhhb1xcXFxEZXNrdG9wXFxcXHB5dGhvbl9zdHVkeVxcXFx0b255X3Byb2plY3RcXFxcYmFyYmVyc2hvcC13ZWJzaXRlXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXHNjcmlwdC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQWJvdXRQYWdlQ29udGVudFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXHpoYW9zaWhhb1xcXFxEZXNrdG9wXFxcXHB5dGhvbl9zdHVkeVxcXFx0b255X3Byb2plY3RcXFxcYmFyYmVyc2hvcC13ZWJzaXRlXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHBhZ2VzXFxcXGFib3V0LXBhZ2UtY29udGVudC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Czhaosihao%5C%5CDesktop%5C%5Cpython_study%5C%5Ctony_project%5C%5Cbarbershop-website%5C%5Csrc%5C%5Ccomponents%5C%5Cpages%5C%5Cabout-page-content.tsx%22%2C%22ids%22%3A%5B%22AboutPageContent%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/accessibility/skip-links.tsx":
/*!*****************************************************!*\
  !*** ./src/components/accessibility/skip-links.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AccessibleButton: () => (/* binding */ AccessibleButton),\n/* harmony export */   AccessibleInput: () => (/* binding */ AccessibleInput),\n/* harmony export */   AccessibleLabel: () => (/* binding */ AccessibleLabel),\n/* harmony export */   AccessibleLink: () => (/* binding */ AccessibleLink),\n/* harmony export */   ScreenReaderOnly: () => (/* binding */ ScreenReaderOnly),\n/* harmony export */   SkipLink: () => (/* binding */ SkipLink),\n/* harmony export */   SkipLinks: () => (/* binding */ SkipLinks),\n/* harmony export */   useFocusManagement: () => (/* binding */ useFocusManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ SkipLink,SkipLinks,ScreenReaderOnly,AccessibleButton,AccessibleLink,AccessibleLabel,AccessibleInput,useFocusManagement auto */ \n\nfunction SkipLink({ href, children, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        href: href,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4\", \"bg-black text-white px-4 py-2 rounded-md z-50\", \"focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2\", \"transition-all duration-200\", className),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\nfunction SkipLinks() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"sr-only focus-within:not-sr-only\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkipLink, {\n                href: \"#main-content\",\n                children: \"跳转到主要内容\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkipLink, {\n                href: \"#navigation\",\n                children: \"跳转到导航菜单\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkipLink, {\n                href: \"#footer\",\n                children: \"跳转到页脚\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n// 屏幕阅读器专用文本组件\nfunction ScreenReaderOnly({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"sr-only\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n        lineNumber: 40,\n        columnNumber: 10\n    }, this);\n}\nfunction AccessibleButton({ children, ariaLabel, ariaDescribedBy, isLoading = false, className, disabled, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        \"aria-label\": ariaLabel,\n        \"aria-describedby\": ariaDescribedBy,\n        \"aria-disabled\": disabled || isLoading,\n        disabled: disabled || isLoading,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\", \"transition-all duration-200\", className),\n        ...props,\n        children: [\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"sr-only\",\n                children: \"正在加载...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n                lineNumber: 74,\n                columnNumber: 9\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, this);\n}\nfunction AccessibleLink({ children, external = false, ariaLabel, className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        \"aria-label\": ariaLabel,\n        target: external ? '_blank' : undefined,\n        rel: external ? 'noopener noreferrer' : undefined,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\", \"transition-all duration-200\", className),\n        ...props,\n        children: [\n            children,\n            external && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScreenReaderOnly, {\n                children: \"（在新窗口中打开）\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n                lineNumber: 109,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\nfunction AccessibleLabel({ children, required = false, className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"block text-sm font-medium text-gray-700\", className),\n        ...props,\n        children: [\n            children,\n            required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-red-500 ml-1\",\n                        \"aria-hidden\": \"true\",\n                        children: \"*\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScreenReaderOnly, {\n                        children: \"（必填）\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n        lineNumber: 128,\n        columnNumber: 5\n    }, this);\n}\nfunction AccessibleInput({ label, error, helperText, required = false, className, id, ...props }) {\n    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;\n    const errorId = error ? `${inputId}-error` : undefined;\n    const helperId = helperText ? `${inputId}-helper` : undefined;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-1\",\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AccessibleLabel, {\n                htmlFor: inputId,\n                required: required,\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n                lineNumber: 170,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                id: inputId,\n                \"aria-invalid\": error ? 'true' : 'false',\n                \"aria-describedby\": [\n                    errorId,\n                    helperId\n                ].filter(Boolean).join(' ') || undefined,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"w-full px-3 py-2 border border-gray-300 rounded-md\", \"focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent\", \"transition-all duration-200\", error && \"border-red-500\", className),\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, this),\n            helperText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                id: helperId,\n                className: \"text-sm text-gray-600\",\n                children: helperText\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n                lineNumber: 188,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                id: errorId,\n                className: \"text-sm text-red-600\",\n                role: \"alert\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n                lineNumber: 193,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\accessibility\\\\skip-links.tsx\",\n        lineNumber: 168,\n        columnNumber: 5\n    }, this);\n}\n// 焦点管理 Hook\nfunction useFocusManagement() {\n    const focusElement = (selector)=>{\n        const element = document.querySelector(selector);\n        if (element) {\n            element.focus();\n        }\n    };\n    const trapFocus = (container)=>{\n        const focusableElements = container.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])');\n        const firstElement = focusableElements[0];\n        const lastElement = focusableElements[focusableElements.length - 1];\n        const handleTabKey = (e)=>{\n            if (e.key === 'Tab') {\n                if (e.shiftKey) {\n                    if (document.activeElement === firstElement) {\n                        lastElement.focus();\n                        e.preventDefault();\n                    }\n                } else {\n                    if (document.activeElement === lastElement) {\n                        firstElement.focus();\n                        e.preventDefault();\n                    }\n                }\n            }\n        };\n        container.addEventListener('keydown', handleTabKey);\n        return ()=>container.removeEventListener('keydown', handleTabKey);\n    };\n    return {\n        focusElement,\n        trapFocus\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/accessibility/skip-links.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/animations/fade-in.tsx":
/*!***********************************************!*\
  !*** ./src/components/animations/fade-in.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CountUp: () => (/* binding */ CountUp),\n/* harmony export */   FadeIn: () => (/* binding */ FadeIn),\n/* harmony export */   ScaleIn: () => (/* binding */ ScaleIn),\n/* harmony export */   SlideIn: () => (/* binding */ SlideIn),\n/* harmony export */   StaggeredFadeIn: () => (/* binding */ StaggeredFadeIn)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ FadeIn,StaggeredFadeIn,ScaleIn,SlideIn,CountUp auto */ \n\nfunction FadeIn({ children, delay = 0, duration = 600, direction = \"up\", distance = 30, className = \"\", threshold = 0.1 }) {\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const elementRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FadeIn.useEffect\": ()=>{\n            const observer = new IntersectionObserver({\n                \"FadeIn.useEffect\": ([entry])=>{\n                    if (entry.isIntersecting) {\n                        setTimeout({\n                            \"FadeIn.useEffect\": ()=>{\n                                setIsVisible(true);\n                            }\n                        }[\"FadeIn.useEffect\"], delay);\n                    }\n                }\n            }[\"FadeIn.useEffect\"], {\n                threshold,\n                rootMargin: \"0px 0px -50px 0px\"\n            });\n            const currentElement = elementRef.current;\n            if (currentElement) {\n                observer.observe(currentElement);\n            }\n            return ({\n                \"FadeIn.useEffect\": ()=>{\n                    if (currentElement) {\n                        observer.unobserve(currentElement);\n                    }\n                }\n            })[\"FadeIn.useEffect\"];\n        }\n    }[\"FadeIn.useEffect\"], [\n        delay,\n        threshold\n    ]);\n    const getTransform = ()=>{\n        if (isVisible) return \"translate3d(0, 0, 0)\";\n        switch(direction){\n            case \"up\":\n                return `translate3d(0, ${distance}px, 0)`;\n            case \"down\":\n                return `translate3d(0, -${distance}px, 0)`;\n            case \"left\":\n                return `translate3d(${distance}px, 0, 0)`;\n            case \"right\":\n                return `translate3d(-${distance}px, 0, 0)`;\n            default:\n                return \"translate3d(0, 0, 0)\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: elementRef,\n        className: className,\n        style: {\n            opacity: isVisible ? 1 : 0,\n            transform: getTransform(),\n            transition: `opacity ${duration}ms ease-out, transform ${duration}ms ease-out`,\n            willChange: \"opacity, transform\"\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\animations\\\\fade-in.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, this);\n}\nfunction StaggeredFadeIn({ children, delay = 0, staggerDelay = 100, duration = 600, direction = \"up\", distance = 30, className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children.map((child, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FadeIn, {\n                delay: delay + index * staggerDelay,\n                duration: duration,\n                direction: direction,\n                distance: distance,\n                className: className,\n                children: child\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\animations\\\\fade-in.tsx\",\n                lineNumber: 109,\n                columnNumber: 9\n            }, this))\n    }, void 0, false);\n}\nfunction ScaleIn({ children, delay = 0, duration = 600, scale = 0.8, className = \"\", threshold = 0.1 }) {\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const elementRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ScaleIn.useEffect\": ()=>{\n            const observer = new IntersectionObserver({\n                \"ScaleIn.useEffect\": ([entry])=>{\n                    if (entry.isIntersecting) {\n                        setTimeout({\n                            \"ScaleIn.useEffect\": ()=>{\n                                setIsVisible(true);\n                            }\n                        }[\"ScaleIn.useEffect\"], delay);\n                    }\n                }\n            }[\"ScaleIn.useEffect\"], {\n                threshold,\n                rootMargin: \"0px 0px -50px 0px\"\n            });\n            const currentElement = elementRef.current;\n            if (currentElement) {\n                observer.observe(currentElement);\n            }\n            return ({\n                \"ScaleIn.useEffect\": ()=>{\n                    if (currentElement) {\n                        observer.unobserve(currentElement);\n                    }\n                }\n            })[\"ScaleIn.useEffect\"];\n        }\n    }[\"ScaleIn.useEffect\"], [\n        delay,\n        threshold\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: elementRef,\n        className: className,\n        style: {\n            opacity: isVisible ? 1 : 0,\n            transform: isVisible ? \"scale(1)\" : `scale(${scale})`,\n            transition: `opacity ${duration}ms ease-out, transform ${duration}ms ease-out`,\n            willChange: \"opacity, transform\"\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\animations\\\\fade-in.tsx\",\n        lineNumber: 172,\n        columnNumber: 5\n    }, this);\n}\nfunction SlideIn({ children, delay = 0, duration = 800, direction = \"left\", distance = 100, className = \"\", threshold = 0.1 }) {\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const elementRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SlideIn.useEffect\": ()=>{\n            const observer = new IntersectionObserver({\n                \"SlideIn.useEffect\": ([entry])=>{\n                    if (entry.isIntersecting) {\n                        setTimeout({\n                            \"SlideIn.useEffect\": ()=>{\n                                setIsVisible(true);\n                            }\n                        }[\"SlideIn.useEffect\"], delay);\n                    }\n                }\n            }[\"SlideIn.useEffect\"], {\n                threshold,\n                rootMargin: \"0px 0px -50px 0px\"\n            });\n            const currentElement = elementRef.current;\n            if (currentElement) {\n                observer.observe(currentElement);\n            }\n            return ({\n                \"SlideIn.useEffect\": ()=>{\n                    if (currentElement) {\n                        observer.unobserve(currentElement);\n                    }\n                }\n            })[\"SlideIn.useEffect\"];\n        }\n    }[\"SlideIn.useEffect\"], [\n        delay,\n        threshold\n    ]);\n    const getTransform = ()=>{\n        if (isVisible) return \"translateX(0)\";\n        return direction === \"left\" ? `translateX(-${distance}px)` : `translateX(${distance}px)`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: elementRef,\n        className: className,\n        style: {\n            opacity: isVisible ? 1 : 0,\n            transform: getTransform(),\n            transition: `opacity ${duration}ms ease-out, transform ${duration}ms ease-out`,\n            willChange: \"opacity, transform\"\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\animations\\\\fade-in.tsx\",\n        lineNumber: 242,\n        columnNumber: 5\n    }, this);\n}\nfunction CountUp({ end, start = 0, duration = 2000, delay = 0, suffix = \"\", prefix = \"\", className = \"\" }) {\n    const [count, setCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(start);\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const elementRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CountUp.useEffect\": ()=>{\n            const observer = new IntersectionObserver({\n                \"CountUp.useEffect\": ([entry])=>{\n                    if (entry.isIntersecting && !isVisible) {\n                        setIsVisible(true);\n                        setTimeout({\n                            \"CountUp.useEffect\": ()=>{\n                                const startTime = Date.now();\n                                const startValue = start;\n                                const endValue = end;\n                                const totalDuration = duration;\n                                const updateCount = {\n                                    \"CountUp.useEffect.updateCount\": ()=>{\n                                        const elapsed = Date.now() - startTime;\n                                        const progress = Math.min(elapsed / totalDuration, 1);\n                                        // Easing function for smooth animation\n                                        const easeOutQuart = 1 - Math.pow(1 - progress, 4);\n                                        const currentValue = Math.round(startValue + (endValue - startValue) * easeOutQuart);\n                                        setCount(currentValue);\n                                        if (progress < 1) {\n                                            requestAnimationFrame(updateCount);\n                                        }\n                                    }\n                                }[\"CountUp.useEffect.updateCount\"];\n                                requestAnimationFrame(updateCount);\n                            }\n                        }[\"CountUp.useEffect\"], delay);\n                    }\n                }\n            }[\"CountUp.useEffect\"], {\n                threshold: 0.5\n            });\n            const currentElement = elementRef.current;\n            if (currentElement) {\n                observer.observe(currentElement);\n            }\n            return ({\n                \"CountUp.useEffect\": ()=>{\n                    if (currentElement) {\n                        observer.unobserve(currentElement);\n                    }\n                }\n            })[\"CountUp.useEffect\"];\n        }\n    }[\"CountUp.useEffect\"], [\n        start,\n        end,\n        duration,\n        delay,\n        isVisible\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        ref: elementRef,\n        className: className,\n        children: [\n            prefix,\n            count.toLocaleString(),\n            suffix\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\animations\\\\fade-in.tsx\",\n        lineNumber: 328,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/animations/fade-in.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/animations/page-transition.tsx":
/*!*******************************************************!*\
  !*** ./src/components/animations/page-transition.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FloatingElement: () => (/* binding */ FloatingElement),\n/* harmony export */   Magnetic: () => (/* binding */ Magnetic),\n/* harmony export */   PageTransition: () => (/* binding */ PageTransition),\n/* harmony export */   Parallax: () => (/* binding */ Parallax),\n/* harmony export */   Pulse: () => (/* binding */ Pulse),\n/* harmony export */   Reveal: () => (/* binding */ Reveal),\n/* harmony export */   SmoothScroll: () => (/* binding */ SmoothScroll),\n/* harmony export */   Typewriter: () => (/* binding */ Typewriter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(ssr)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ PageTransition,SmoothScroll,Parallax,FloatingElement,Pulse,Typewriter,Reveal,Magnetic auto */ \n\n\n\nfunction PageTransition({ children }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [displayChildren, setDisplayChildren] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(children);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"PageTransition.useEffect\": ()=>{\n            setIsLoading(true);\n            const timer = setTimeout({\n                \"PageTransition.useEffect.timer\": ()=>{\n                    setDisplayChildren(children);\n                    setIsLoading(false);\n                }\n            }[\"PageTransition.useEffect.timer\"], 300);\n            return ({\n                \"PageTransition.useEffect\": ()=>clearTimeout(timer)\n            })[\"PageTransition.useEffect\"];\n        }\n    }[\"PageTransition.useEffect\"], [\n        pathname,\n        children\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `fixed inset-0 z-50 bg-background transition-opacity duration-300 ${isLoading ? \"opacity-100\" : \"opacity-0 pointer-events-none\"}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 border-4 border-primary/20 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\animations\\\\page-transition.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\animations\\\\page-transition.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\animations\\\\page-transition.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-lg font-medium text-muted-foreground\",\n                                children: \"✂️ Classic Cuts\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\animations\\\\page-transition.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\animations\\\\page-transition.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\animations\\\\page-transition.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\animations\\\\page-transition.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `transition-opacity duration-500 ${isLoading ? \"opacity-0\" : \"opacity-100\"}`,\n                children: displayChildren\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\animations\\\\page-transition.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\animations\\\\page-transition.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\nfunction SmoothScroll({ children }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"SmoothScroll.useEffect\": ()=>{\n            // Add smooth scrolling behavior\n            document.documentElement.style.scrollBehavior = \"smooth\";\n            return ({\n                \"SmoothScroll.useEffect\": ()=>{\n                    document.documentElement.style.scrollBehavior = \"auto\";\n                }\n            })[\"SmoothScroll.useEffect\"];\n        }\n    }[\"SmoothScroll.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\nfunction Parallax({ children, speed = 0.5, className = \"\" }) {\n    const [offset, setOffset] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Parallax.useEffect\": ()=>{\n            const handleScroll = {\n                \"Parallax.useEffect.handleScroll\": ()=>{\n                    setOffset(window.pageYOffset * speed);\n                }\n            }[\"Parallax.useEffect.handleScroll\"];\n            window.addEventListener(\"scroll\", handleScroll, {\n                passive: true\n            });\n            return ({\n                \"Parallax.useEffect\": ()=>window.removeEventListener(\"scroll\", handleScroll)\n            })[\"Parallax.useEffect\"];\n        }\n    }[\"Parallax.useEffect\"], [\n        speed\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        style: {\n            transform: `translateY(${offset}px)`,\n            willChange: \"transform\"\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\animations\\\\page-transition.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\nfunction FloatingElement({ children, amplitude = 10, duration = 3000, delay = 0, className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            animation: `float ${duration}ms ease-in-out infinite`,\n            animationDelay: `${delay}ms`,\n            animationFillMode: \"both\"\n        },\n        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n            [\n                \"523c345b70d662ba\",\n                [\n                    amplitude\n                ]\n            ]\n        ]) + \" \" + (className || \"\"),\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"523c345b70d662ba\",\n                dynamic: [\n                    amplitude\n                ],\n                children: `@-webkit-keyframes float{0%,100%{-webkit-transform:translatey(0px);transform:translatey(0px)}50%{-webkit-transform:translatey(-${amplitude}px);transform:translatey(-${amplitude}px)}}@-moz-keyframes float{0%,100%{-moz-transform:translatey(0px);transform:translatey(0px)}50%{-moz-transform:translatey(-${amplitude}px);transform:translatey(-${amplitude}px)}}@-o-keyframes float{0%,100%{-o-transform:translatey(0px);transform:translatey(0px)}50%{-o-transform:translatey(-${amplitude}px);transform:translatey(-${amplitude}px)}}@keyframes float{0%,100%{-webkit-transform:translatey(0px);-moz-transform:translatey(0px);-o-transform:translatey(0px);transform:translatey(0px)}50%{-webkit-transform:translatey(-${amplitude}px);-moz-transform:translatey(-${amplitude}px);-o-transform:translatey(-${amplitude}px);transform:translatey(-${amplitude}px)}}`\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\animations\\\\page-transition.tsx\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, this);\n}\nfunction Pulse({ children, scale = 1.05, duration = 2000, className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            animation: `pulse ${duration}ms ease-in-out infinite`\n        },\n        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n            [\n                \"384e04bbba70ecd0\",\n                [\n                    scale\n                ]\n            ]\n        ]) + \" \" + (className || \"\"),\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"384e04bbba70ecd0\",\n                dynamic: [\n                    scale\n                ],\n                children: `@-webkit-keyframes pulse{0%,100%{-webkit-transform:scale(1);transform:scale(1)}50%{-webkit-transform:scale(${scale});transform:scale(${scale})}}@-moz-keyframes pulse{0%,100%{-moz-transform:scale(1);transform:scale(1)}50%{-moz-transform:scale(${scale});transform:scale(${scale})}}@-o-keyframes pulse{0%,100%{-o-transform:scale(1);transform:scale(1)}50%{-o-transform:scale(${scale});transform:scale(${scale})}}@keyframes pulse{0%,100%{-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1)}50%{-webkit-transform:scale(${scale});-moz-transform:scale(${scale});-o-transform:scale(${scale});transform:scale(${scale})}}`\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\animations\\\\page-transition.tsx\",\n        lineNumber: 156,\n        columnNumber: 5\n    }, this);\n}\nfunction Typewriter({ text, speed = 50, delay = 0, className = \"\", onComplete }) {\n    const [displayText, setDisplayText] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [isStarted, setIsStarted] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Typewriter.useEffect\": ()=>{\n            const startTimer = setTimeout({\n                \"Typewriter.useEffect.startTimer\": ()=>{\n                    setIsStarted(true);\n                }\n            }[\"Typewriter.useEffect.startTimer\"], delay);\n            return ({\n                \"Typewriter.useEffect\": ()=>clearTimeout(startTimer)\n            })[\"Typewriter.useEffect\"];\n        }\n    }[\"Typewriter.useEffect\"], [\n        delay\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Typewriter.useEffect\": ()=>{\n            if (!isStarted) return;\n            if (currentIndex < text.length) {\n                const timer = setTimeout({\n                    \"Typewriter.useEffect.timer\": ()=>{\n                        setDisplayText({\n                            \"Typewriter.useEffect.timer\": (prev)=>prev + text[currentIndex]\n                        }[\"Typewriter.useEffect.timer\"]);\n                        setCurrentIndex({\n                            \"Typewriter.useEffect.timer\": (prev)=>prev + 1\n                        }[\"Typewriter.useEffect.timer\"]);\n                    }\n                }[\"Typewriter.useEffect.timer\"], speed);\n                return ({\n                    \"Typewriter.useEffect\": ()=>clearTimeout(timer)\n                })[\"Typewriter.useEffect\"];\n            } else if (onComplete) {\n                onComplete();\n            }\n        }\n    }[\"Typewriter.useEffect\"], [\n        currentIndex,\n        text,\n        speed,\n        isStarted,\n        onComplete\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: className,\n        children: [\n            displayText,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"animate-pulse\",\n                children: \"|\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\animations\\\\page-transition.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\animations\\\\page-transition.tsx\",\n        lineNumber: 220,\n        columnNumber: 5\n    }, this);\n}\nfunction Reveal({ children, direction = \"horizontal\", duration = 800, delay = 0, className = \"\" }) {\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Reveal.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"Reveal.useEffect.timer\": ()=>{\n                    setIsVisible(true);\n                }\n            }[\"Reveal.useEffect.timer\"], delay);\n            return ({\n                \"Reveal.useEffect\": ()=>clearTimeout(timer)\n            })[\"Reveal.useEffect\"];\n        }\n    }[\"Reveal.useEffect\"], [\n        delay\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative overflow-hidden ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `transition-transform duration-${duration} ease-out ${isVisible ? \"translate-x-0 translate-y-0\" : direction === \"horizontal\" ? \"translate-x-full\" : \"translate-y-full\"}`,\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\animations\\\\page-transition.tsx\",\n                lineNumber: 254,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `absolute inset-0 bg-primary transition-transform duration-${duration} ease-out ${isVisible ? direction === \"horizontal\" ? \"translate-x-full\" : \"translate-y-full\" : \"translate-x-0 translate-y-0\"}`,\n                style: {\n                    transitionDelay: `${delay}ms`\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\animations\\\\page-transition.tsx\",\n                lineNumber: 262,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\animations\\\\page-transition.tsx\",\n        lineNumber: 253,\n        columnNumber: 5\n    }, this);\n}\nfunction Magnetic({ children, strength = 0.3, className = \"\" }) {\n    const [position, setPosition] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        x: 0,\n        y: 0\n    });\n    const handleMouseMove = (e)=>{\n        const rect = e.currentTarget.getBoundingClientRect();\n        const centerX = rect.left + rect.width / 2;\n        const centerY = rect.top + rect.height / 2;\n        const deltaX = (e.clientX - centerX) * strength;\n        const deltaY = (e.clientY - centerY) * strength;\n        setPosition({\n            x: deltaX,\n            y: deltaY\n        });\n    };\n    const handleMouseLeave = ()=>{\n        setPosition({\n            x: 0,\n            y: 0\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        onMouseMove: handleMouseMove,\n        onMouseLeave: handleMouseLeave,\n        style: {\n            transform: `translate(${position.x}px, ${position.y}px)`,\n            transition: \"transform 0.3s ease-out\"\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\animations\\\\page-transition.tsx\",\n        lineNumber: 299,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/animations/page-transition.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/pages/about-page-content.tsx":
/*!*****************************************************!*\
  !*** ./src/components/pages/about-page-content.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AboutPageContent: () => (/* binding */ AboutPageContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_animations_fade_in__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/animations/fade-in */ \"(ssr)/./src/components/animations/fade-in.tsx\");\n/* harmony import */ var _components_animations_page_transition__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/animations/page-transition */ \"(ssr)/./src/components/animations/page-transition.tsx\");\n/* __next_internal_client_entry_do_not_use__ AboutPageContent auto */ \n\n\n\n\n\n\n// Team members data\nconst teamMembers = [\n    {\n        name: \"李师傅\",\n        role: \"首席理发师\",\n        experience: \"15年经验\",\n        specialty: \"经典理发、胡须造型\",\n        image: \"/team-member-1.jpg\",\n        description: \"拥有15年丰富经验的资深理发师，擅长各种经典发型设计。\"\n    },\n    {\n        name: \"王师傅\",\n        role: \"高级理发师\",\n        experience: \"12年经验\",\n        specialty: \"现代造型、头发护理\",\n        image: \"/team-member-2.jpg\",\n        description: \"专注于现代时尚造型，为客户打造个性化的完美发型。\"\n    },\n    {\n        name: \"张师傅\",\n        role: \"造型师\",\n        experience: \"8年经验\",\n        specialty: \"创意造型、色彩搭配\",\n        image: \"/team-member-3.jpg\",\n        description: \"年轻有活力的造型师，善于创新和色彩搭配。\"\n    }\n];\n// Company values\nconst values = [\n    {\n        icon: \"🎯\",\n        title: \"专业品质\",\n        description: \"我们坚持使用最优质的产品和工具，确保每一次服务都达到最高标准。\"\n    },\n    {\n        icon: \"❤️\",\n        title: \"用心服务\",\n        description: \"每一位客户都是独特的，我们用心倾听需求，提供个性化的专业建议。\"\n    },\n    {\n        icon: \"🏆\",\n        title: \"持续创新\",\n        description: \"紧跟时尚潮流，不断学习新技术，为客户带来最前沿的造型体验。\"\n    },\n    {\n        icon: \"🤝\",\n        title: \"诚信经营\",\n        description: \"以诚待客，公平定价，建立长期的信任关系是我们的经营理念。\"\n    }\n];\n// Company history timeline\nconst timeline = [\n    {\n        year: \"2009\",\n        title: \"创立初期\",\n        description: \"Classic Cuts 在市中心开设第一家店铺，开始我们的理发事业。\"\n    },\n    {\n        year: \"2012\",\n        title: \"团队扩展\",\n        description: \"招募更多专业理发师，建立了专业的服务团队。\"\n    },\n    {\n        year: \"2015\",\n        title: \"服务升级\",\n        description: \"引入现代化设备和高端护理产品，提升服务质量。\"\n    },\n    {\n        year: \"2018\",\n        title: \"品牌发展\",\n        description: \"建立品牌形象，成为本地知名的专业理发品牌。\"\n    },\n    {\n        year: \"2021\",\n        title: \"数字化转型\",\n        description: \"推出在线预约系统，提供更便捷的服务体验。\"\n    },\n    {\n        year: \"2024\",\n        title: \"持续发展\",\n        description: \"继续致力于提供最优质的理发服务，服务更多客户。\"\n    }\n];\nfunction AboutPageContent() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative py-20 bg-gradient-to-br from-primary/10 to-accent/10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_fade_in__WEBPACK_IMPORTED_MODULE_5__.FadeIn, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-6xl font-bold mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_page_transition__WEBPACK_IMPORTED_MODULE_6__.Typewriter, {\n                                            text: \"关于 Classic Cuts\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_fade_in__WEBPACK_IMPORTED_MODULE_5__.FadeIn, {\n                                    delay: 500,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl md:text-2xl text-muted-foreground mb-8\",\n                                        children: \"传承经典理发工艺，融合现代时尚元素，为每一位客户打造完美造型\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_fade_in__WEBPACK_IMPORTED_MODULE_5__.FadeIn, {\n                                    delay: 800,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center space-x-8 text-sm text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-accent\",\n                                                        children: \"\\uD83D\\uDCC5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"成立于 2009 年\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-accent\",\n                                                        children: \"\\uD83D\\uDC65\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"5000+ 满意客户\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-accent\",\n                                                        children: \"\\uD83C\\uDFC6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"15 年专业经验\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_page_transition__WEBPACK_IMPORTED_MODULE_6__.FloatingElement, {\n                        delay: 1000,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-20 left-10 w-20 h-20 bg-accent/20 rounded-full blur-xl\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_page_transition__WEBPACK_IMPORTED_MODULE_6__.FloatingElement, {\n                        delay: 1500,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-20 right-10 w-32 h-32 bg-primary/20 rounded-full blur-xl\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_fade_in__WEBPACK_IMPORTED_MODULE_5__.FadeIn, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl md:text-4xl font-bold\",\n                                            children: \"我们的故事\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4 text-muted-foreground\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Classic Cuts 成立于2009年，源于对传统理发工艺的热爱和对完美造型的追求。 我们的创始人李师傅从小就对理发艺术充满热情，经过多年的学习和实践， 决定开设自己的理发店，为更多人提供专业的理发服务。\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: '十五年来，我们始终坚持\"品质第一，客户至上\"的经营理念， 不断提升服务质量，引进先进设备和优质产品。我们相信， 每一次剪发都是一次艺术创作，每一位客户都值得我们用心对待。'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"如今，Classic Cuts 已经成为本地知名的专业理发品牌， 我们的团队由经验丰富的理发师组成，为客户提供从经典到现代的各种造型服务。 我们将继续传承经典，创新未来，为每一位客户打造完美造型。\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_fade_in__WEBPACK_IMPORTED_MODULE_5__.FadeIn, {\n                                delay: 300,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative h-96 lg:h-[500px] rounded-lg overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                src: \"/about-story.jpg\",\n                                                alt: \"Classic Cuts 的故事\",\n                                                fill: true,\n                                                className: \"object-cover\",\n                                                placeholder: \"blur\",\n                                                blurDataURL: \"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_page_transition__WEBPACK_IMPORTED_MODULE_6__.FloatingElement, {\n                                            delay: 2000,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-6 -right-6 w-24 h-24 bg-accent/30 rounded-full blur-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_page_transition__WEBPACK_IMPORTED_MODULE_6__.FloatingElement, {\n                                            delay: 2500,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -bottom-6 -left-6 w-32 h-32 bg-primary/30 rounded-full blur-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-muted/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_fade_in__WEBPACK_IMPORTED_MODULE_5__.FadeIn, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl md:text-4xl font-bold mb-4\",\n                                        children: \"我们的价值观\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-muted-foreground max-w-2xl mx-auto\",\n                                        children: \"这些核心价值观指导着我们的每一个决定和行动，确保为客户提供最优质的服务\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_fade_in__WEBPACK_IMPORTED_MODULE_5__.StaggeredFadeIn, {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                            children: values.map((value, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_fade_in__WEBPACK_IMPORTED_MODULE_5__.ScaleIn, {\n                                    delay: index * 100,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        className: \"text-center h-full hover:shadow-lg transition-all duration-300 hover:scale-105\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mx-auto mb-4 p-3 bg-primary/10 rounded-full w-fit\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl\",\n                                                        children: value.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold mb-3\",\n                                                    children: value.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: value.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 17\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_fade_in__WEBPACK_IMPORTED_MODULE_5__.FadeIn, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl md:text-4xl font-bold mb-4\",\n                                        children: \"发展历程\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-muted-foreground max-w-2xl mx-auto\",\n                                        children: \"从2009年的小店铺到今天的专业品牌，见证我们的成长历程\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-primary/20\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_fade_in__WEBPACK_IMPORTED_MODULE_5__.StaggeredFadeIn, {\n                                        className: \"space-y-12\",\n                                        children: timeline.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_fade_in__WEBPACK_IMPORTED_MODULE_5__.ScaleIn, {\n                                                delay: index * 150,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                                className: \"hover:shadow-lg transition-all duration-300\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                                    className: \"p-6\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-2xl font-bold text-primary mb-2\",\n                                                                            children: item.year\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                                            lineNumber: 246,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-xl font-semibold mb-3\",\n                                                                            children: item.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                                            lineNumber: 247,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-muted-foreground\",\n                                                                            children: item.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                                            lineNumber: 248,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                                    lineNumber: 245,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative z-10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-4 h-4 bg-primary rounded-full border-4 border-background\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-1/2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                lineNumber: 223,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-muted/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_fade_in__WEBPACK_IMPORTED_MODULE_5__.FadeIn, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl md:text-4xl font-bold mb-4\",\n                                        children: \"专业团队\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-muted-foreground max-w-2xl mx-auto\",\n                                        children: \"我们的理发师团队拥有丰富的经验和精湛的技艺，为您提供最专业的服务\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_fade_in__WEBPACK_IMPORTED_MODULE_5__.StaggeredFadeIn, {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: teamMembers.map((member, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_fade_in__WEBPACK_IMPORTED_MODULE_5__.ScaleIn, {\n                                    delay: index * 200,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        className: \"text-center hover:shadow-lg transition-all duration-300 hover:scale-105\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative w-32 h-32 mx-auto mb-4 rounded-full overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                        src: member.image,\n                                                        alt: member.name,\n                                                        fill: true,\n                                                        className: \"object-cover\",\n                                                        placeholder: \"blur\",\n                                                        blurDataURL: \"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold mb-2\",\n                                                    children: member.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-primary font-medium mb-1\",\n                                                    children: member.role\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-muted-foreground mb-2\",\n                                                    children: member.experience\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-accent mb-3\",\n                                                    children: member.specialty\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: member.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 17\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-primary text-primary-foreground\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_fade_in__WEBPACK_IMPORTED_MODULE_5__.FadeIn, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-3xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold mb-4\",\n                                    children: \"体验我们的专业服务\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl mb-8 text-primary-foreground/90\",\n                                    children: \"预约我们的专业理发师，让我们为您打造完美造型\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            asChild: true,\n                                            size: \"lg\",\n                                            className: \"bg-accent hover:bg-accent/90 text-black font-semibold\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                href: \"/booking\",\n                                                children: \"立即预约\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            asChild: true,\n                                            variant: \"outline\",\n                                            size: \"lg\",\n                                            className: \"border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                href: \"/services\",\n                                                children: \"查看服务\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n                lineNumber: 309,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\pages\\\\about-page-content.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/pages/about-page-content.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/performance/web-vitals.tsx":
/*!***************************************************!*\
  !*** ./src/components/performance/web-vitals.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PerformanceMonitor: () => (/* binding */ PerformanceMonitor),\n/* harmony export */   WebVitals: () => (/* binding */ WebVitals),\n/* harmony export */   useErrorMonitoring: () => (/* binding */ useErrorMonitoring),\n/* harmony export */   useImageLoadingMonitoring: () => (/* binding */ useImageLoadingMonitoring),\n/* harmony export */   usePerformanceMonitoring: () => (/* binding */ usePerformanceMonitoring),\n/* harmony export */   useUserExperienceMonitoring: () => (/* binding */ useUserExperienceMonitoring)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var web_vitals__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! web-vitals */ \"(ssr)/./node_modules/web-vitals/dist/web-vitals.js\");\n/* __next_internal_client_entry_do_not_use__ WebVitals,usePerformanceMonitoring,useImageLoadingMonitoring,useErrorMonitoring,useUserExperienceMonitoring,PerformanceMonitor auto */ \n\n\n// 发送指标到分析服务\nfunction sendToAnalytics(metric) {\n    // 这里可以发送到 Google Analytics, Vercel Analytics 等\n    console.log('Web Vitals:', metric);\n    // 示例：发送到 Google Analytics\n    if (false) {}\n}\n// Web Vitals 监控组件\nfunction WebVitals() {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WebVitals.useEffect\": ()=>{\n            // 累积布局偏移 (Cumulative Layout Shift)\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_2__.onCLS)(sendToAnalytics);\n            // 交互到下次绘制 (Interaction to Next Paint)\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_2__.onINP)(sendToAnalytics);\n            // 首次内容绘制 (First Contentful Paint)\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_2__.onFCP)(sendToAnalytics);\n            // 最大内容绘制 (Largest Contentful Paint)\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_2__.onLCP)(sendToAnalytics);\n            // 首字节时间 (Time to First Byte)\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_2__.onTTFB)(sendToAnalytics);\n        }\n    }[\"WebVitals.useEffect\"], []);\n    return null;\n}\n// 性能监控 Hook\nfunction usePerformanceMonitoring() {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"usePerformanceMonitoring.useEffect\": ()=>{\n            // 监控页面加载性能\n            const observer = new PerformanceObserver({\n                \"usePerformanceMonitoring.useEffect\": (list)=>{\n                    for (const entry of list.getEntries()){\n                        if (entry.entryType === 'navigation') {\n                            const navEntry = entry;\n                            console.log('Navigation Timing:', {\n                                domContentLoaded: navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart,\n                                loadComplete: navEntry.loadEventEnd - navEntry.loadEventStart,\n                                firstByte: navEntry.responseStart - navEntry.requestStart,\n                                domInteractive: navEntry.domInteractive - navEntry.navigationStart\n                            });\n                        }\n                        if (entry.entryType === 'resource') {\n                            const resourceEntry = entry;\n                            // 监控慢资源\n                            if (resourceEntry.duration > 1000) {\n                                console.warn('Slow resource:', {\n                                    name: resourceEntry.name,\n                                    duration: resourceEntry.duration,\n                                    size: resourceEntry.transferSize\n                                });\n                            }\n                        }\n                    }\n                }\n            }[\"usePerformanceMonitoring.useEffect\"]);\n            observer.observe({\n                entryTypes: [\n                    'navigation',\n                    'resource'\n                ]\n            });\n            return ({\n                \"usePerformanceMonitoring.useEffect\": ()=>observer.disconnect()\n            })[\"usePerformanceMonitoring.useEffect\"];\n        }\n    }[\"usePerformanceMonitoring.useEffect\"], []);\n    // 监控内存使用\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"usePerformanceMonitoring.useEffect\": ()=>{\n            const checkMemory = {\n                \"usePerformanceMonitoring.useEffect.checkMemory\": ()=>{\n                    if ('memory' in performance) {\n                        const memory = performance.memory;\n                        console.log('Memory Usage:', {\n                            used: Math.round(memory.usedJSHeapSize / 1048576) + ' MB',\n                            total: Math.round(memory.totalJSHeapSize / 1048576) + ' MB',\n                            limit: Math.round(memory.jsHeapSizeLimit / 1048576) + ' MB'\n                        });\n                    }\n                }\n            }[\"usePerformanceMonitoring.useEffect.checkMemory\"];\n            const interval = setInterval(checkMemory, 30000) // 每30秒检查一次\n            ;\n            return ({\n                \"usePerformanceMonitoring.useEffect\": ()=>clearInterval(interval)\n            })[\"usePerformanceMonitoring.useEffect\"];\n        }\n    }[\"usePerformanceMonitoring.useEffect\"], []);\n}\n// 图片懒加载监控\nfunction useImageLoadingMonitoring() {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useImageLoadingMonitoring.useEffect\": ()=>{\n            const images = document.querySelectorAll('img[loading=\"lazy\"]');\n            const observer = new IntersectionObserver({\n                \"useImageLoadingMonitoring.useEffect\": (entries)=>{\n                    entries.forEach({\n                        \"useImageLoadingMonitoring.useEffect\": (entry)=>{\n                            if (entry.isIntersecting) {\n                                const img = entry.target;\n                                const startTime = performance.now();\n                                img.addEventListener('load', {\n                                    \"useImageLoadingMonitoring.useEffect\": ()=>{\n                                        const loadTime = performance.now() - startTime;\n                                        console.log('Image loaded:', {\n                                            src: img.src,\n                                            loadTime: Math.round(loadTime),\n                                            naturalWidth: img.naturalWidth,\n                                            naturalHeight: img.naturalHeight\n                                        });\n                                    }\n                                }[\"useImageLoadingMonitoring.useEffect\"]);\n                                observer.unobserve(img);\n                            }\n                        }\n                    }[\"useImageLoadingMonitoring.useEffect\"]);\n                }\n            }[\"useImageLoadingMonitoring.useEffect\"]);\n            images.forEach({\n                \"useImageLoadingMonitoring.useEffect\": (img)=>observer.observe(img)\n            }[\"useImageLoadingMonitoring.useEffect\"]);\n            return ({\n                \"useImageLoadingMonitoring.useEffect\": ()=>observer.disconnect()\n            })[\"useImageLoadingMonitoring.useEffect\"];\n        }\n    }[\"useImageLoadingMonitoring.useEffect\"], []);\n}\n// 错误监控\nfunction useErrorMonitoring() {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useErrorMonitoring.useEffect\": ()=>{\n            const handleError = {\n                \"useErrorMonitoring.useEffect.handleError\": (event)=>{\n                    console.error('JavaScript Error:', {\n                        message: event.message,\n                        filename: event.filename,\n                        lineno: event.lineno,\n                        colno: event.colno,\n                        error: event.error\n                    });\n                // 发送错误到监控服务\n                // sendErrorToService(event)\n                }\n            }[\"useErrorMonitoring.useEffect.handleError\"];\n            const handleUnhandledRejection = {\n                \"useErrorMonitoring.useEffect.handleUnhandledRejection\": (event)=>{\n                    console.error('Unhandled Promise Rejection:', event.reason);\n                // 发送错误到监控服务\n                // sendErrorToService(event)\n                }\n            }[\"useErrorMonitoring.useEffect.handleUnhandledRejection\"];\n            window.addEventListener('error', handleError);\n            window.addEventListener('unhandledrejection', handleUnhandledRejection);\n            return ({\n                \"useErrorMonitoring.useEffect\": ()=>{\n                    window.removeEventListener('error', handleError);\n                    window.removeEventListener('unhandledrejection', handleUnhandledRejection);\n                }\n            })[\"useErrorMonitoring.useEffect\"];\n        }\n    }[\"useErrorMonitoring.useEffect\"], []);\n}\n// 用户体验监控\nfunction useUserExperienceMonitoring() {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useUserExperienceMonitoring.useEffect\": ()=>{\n            // 监控页面可见性变化\n            const handleVisibilityChange = {\n                \"useUserExperienceMonitoring.useEffect.handleVisibilityChange\": ()=>{\n                    console.log('Page visibility changed:', document.visibilityState);\n                }\n            }[\"useUserExperienceMonitoring.useEffect.handleVisibilityChange\"];\n            // 监控网络状态变化\n            const handleOnline = {\n                \"useUserExperienceMonitoring.useEffect.handleOnline\": ()=>console.log('Network: Online')\n            }[\"useUserExperienceMonitoring.useEffect.handleOnline\"];\n            const handleOffline = {\n                \"useUserExperienceMonitoring.useEffect.handleOffline\": ()=>console.log('Network: Offline')\n            }[\"useUserExperienceMonitoring.useEffect.handleOffline\"];\n            document.addEventListener('visibilitychange', handleVisibilityChange);\n            window.addEventListener('online', handleOnline);\n            window.addEventListener('offline', handleOffline);\n            return ({\n                \"useUserExperienceMonitoring.useEffect\": ()=>{\n                    document.removeEventListener('visibilitychange', handleVisibilityChange);\n                    window.removeEventListener('online', handleOnline);\n                    window.removeEventListener('offline', handleOffline);\n                }\n            })[\"useUserExperienceMonitoring.useEffect\"];\n        }\n    }[\"useUserExperienceMonitoring.useEffect\"], []);\n}\n// 综合性能监控组件\nfunction PerformanceMonitor() {\n    usePerformanceMonitoring();\n    useImageLoadingMonitoring();\n    useErrorMonitoring();\n    useUserExperienceMonitoring();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WebVitals, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\performance\\\\web-vitals.tsx\",\n        lineNumber: 202,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wZXJmb3JtYW5jZS93ZWItdml0YWxzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFFaUM7QUFDOEI7QUFXL0QsWUFBWTtBQUNaLFNBQVNNLGdCQUFnQkMsTUFBYztJQUNyQywrQ0FBK0M7SUFDL0NDLFFBQVFDLEdBQUcsQ0FBQyxlQUFlRjtJQUUzQiwwQkFBMEI7SUFDMUIsSUFBSSxLQUFxRCxFQUFFLEVBTzFEO0FBQ0g7QUFFQSxrQkFBa0I7QUFDWCxTQUFTYTtJQUNkcEIsZ0RBQVNBOytCQUFDO1lBQ1IsbUNBQW1DO1lBQ25DQyxpREFBS0EsQ0FBQ0s7WUFFTixzQ0FBc0M7WUFDdENKLGlEQUFLQSxDQUFDSTtZQUVOLGtDQUFrQztZQUNsQ0gsaURBQUtBLENBQUNHO1lBRU4sb0NBQW9DO1lBQ3BDRixpREFBS0EsQ0FBQ0U7WUFFTiw2QkFBNkI7WUFDN0JELGtEQUFNQSxDQUFDQztRQUNUOzhCQUFHLEVBQUU7SUFFTCxPQUFPO0FBQ1Q7QUFFQSxZQUFZO0FBQ0wsU0FBU2U7SUFDZHJCLGdEQUFTQTs4Q0FBQztZQUNSLFdBQVc7WUFDWCxNQUFNc0IsV0FBVyxJQUFJQztzREFBb0IsQ0FBQ0M7b0JBQ3hDLEtBQUssTUFBTUMsU0FBU0QsS0FBS0UsVUFBVSxHQUFJO3dCQUNyQyxJQUFJRCxNQUFNRSxTQUFTLEtBQUssY0FBYzs0QkFDcEMsTUFBTUMsV0FBV0g7NEJBQ2pCakIsUUFBUUMsR0FBRyxDQUFDLHNCQUFzQjtnQ0FDaENvQixrQkFBa0JELFNBQVNFLHdCQUF3QixHQUFHRixTQUFTRywwQkFBMEI7Z0NBQ3pGQyxjQUFjSixTQUFTSyxZQUFZLEdBQUdMLFNBQVNNLGNBQWM7Z0NBQzdEQyxXQUFXUCxTQUFTUSxhQUFhLEdBQUdSLFNBQVNTLFlBQVk7Z0NBQ3pEQyxnQkFBZ0JWLFNBQVNVLGNBQWMsR0FBR1YsU0FBU1csZUFBZTs0QkFDcEU7d0JBQ0Y7d0JBRUEsSUFBSWQsTUFBTUUsU0FBUyxLQUFLLFlBQVk7NEJBQ2xDLE1BQU1hLGdCQUFnQmY7NEJBQ3RCLFFBQVE7NEJBQ1IsSUFBSWUsY0FBY0MsUUFBUSxHQUFHLE1BQU07Z0NBQ2pDakMsUUFBUWtDLElBQUksQ0FBQyxrQkFBa0I7b0NBQzdCOUIsTUFBTTRCLGNBQWM1QixJQUFJO29DQUN4QjZCLFVBQVVELGNBQWNDLFFBQVE7b0NBQ2hDRSxNQUFNSCxjQUFjSSxZQUFZO2dDQUNsQzs0QkFDRjt3QkFDRjtvQkFDRjtnQkFDRjs7WUFFQXRCLFNBQVN1QixPQUFPLENBQUM7Z0JBQUVDLFlBQVk7b0JBQUM7b0JBQWM7aUJBQVc7WUFBQztZQUUxRDtzREFBTyxJQUFNeEIsU0FBU3lCLFVBQVU7O1FBQ2xDOzZDQUFHLEVBQUU7SUFFTCxTQUFTO0lBQ1QvQyxnREFBU0E7OENBQUM7WUFDUixNQUFNZ0Q7a0VBQWM7b0JBQ2xCLElBQUksWUFBWUMsYUFBYTt3QkFDM0IsTUFBTUMsU0FBUyxZQUFxQkEsTUFBTTt3QkFDMUMxQyxRQUFRQyxHQUFHLENBQUMsaUJBQWlCOzRCQUMzQjBDLE1BQU1sQyxLQUFLQyxLQUFLLENBQUNnQyxPQUFPRSxjQUFjLEdBQUcsV0FBVzs0QkFDcERDLE9BQU9wQyxLQUFLQyxLQUFLLENBQUNnQyxPQUFPSSxlQUFlLEdBQUcsV0FBVzs0QkFDdERDLE9BQU90QyxLQUFLQyxLQUFLLENBQUNnQyxPQUFPTSxlQUFlLEdBQUcsV0FBVzt3QkFDeEQ7b0JBQ0Y7Z0JBQ0Y7O1lBRUEsTUFBTUMsV0FBV0MsWUFBWVYsYUFBYSxPQUFPLFdBQVc7O1lBQzVEO3NEQUFPLElBQU1XLGNBQWNGOztRQUM3Qjs2Q0FBRyxFQUFFO0FBQ1A7QUFFQSxVQUFVO0FBQ0gsU0FBU0c7SUFDZDVELGdEQUFTQTsrQ0FBQztZQUNSLE1BQU02RCxTQUFTQyxTQUFTQyxnQkFBZ0IsQ0FBQztZQUV6QyxNQUFNekMsV0FBVyxJQUFJMEM7dURBQXFCLENBQUNDO29CQUN6Q0EsUUFBUUMsT0FBTzsrREFBQyxDQUFDekM7NEJBQ2YsSUFBSUEsTUFBTTBDLGNBQWMsRUFBRTtnQ0FDeEIsTUFBTUMsTUFBTTNDLE1BQU00QyxNQUFNO2dDQUN4QixNQUFNQyxZQUFZckIsWUFBWXNCLEdBQUc7Z0NBRWpDSCxJQUFJSSxnQkFBZ0IsQ0FBQzsyRUFBUTt3Q0FDM0IsTUFBTUMsV0FBV3hCLFlBQVlzQixHQUFHLEtBQUtEO3dDQUNyQzlELFFBQVFDLEdBQUcsQ0FBQyxpQkFBaUI7NENBQzNCaUUsS0FBS04sSUFBSU0sR0FBRzs0Q0FDWkQsVUFBVXhELEtBQUtDLEtBQUssQ0FBQ3VEOzRDQUNyQkUsY0FBY1AsSUFBSU8sWUFBWTs0Q0FDOUJDLGVBQWVSLElBQUlRLGFBQWE7d0NBQ2xDO29DQUNGOztnQ0FFQXRELFNBQVN1RCxTQUFTLENBQUNUOzRCQUNyQjt3QkFDRjs7Z0JBQ0Y7O1lBRUFQLE9BQU9LLE9BQU87dURBQUMsQ0FBQ0UsTUFBUTlDLFNBQVN1QixPQUFPLENBQUN1Qjs7WUFFekM7dURBQU8sSUFBTTlDLFNBQVN5QixVQUFVOztRQUNsQzs4Q0FBRyxFQUFFO0FBQ1A7QUFFQSxPQUFPO0FBQ0EsU0FBUytCO0lBQ2Q5RSxnREFBU0E7d0NBQUM7WUFDUixNQUFNK0U7NERBQWMsQ0FBQ0M7b0JBQ25CeEUsUUFBUXlFLEtBQUssQ0FBQyxxQkFBcUI7d0JBQ2pDQyxTQUFTRixNQUFNRSxPQUFPO3dCQUN0QkMsVUFBVUgsTUFBTUcsUUFBUTt3QkFDeEJDLFFBQVFKLE1BQU1JLE1BQU07d0JBQ3BCQyxPQUFPTCxNQUFNSyxLQUFLO3dCQUNsQkosT0FBT0QsTUFBTUMsS0FBSztvQkFDcEI7Z0JBRUEsWUFBWTtnQkFDWiw0QkFBNEI7Z0JBQzlCOztZQUVBLE1BQU1LO3lFQUEyQixDQUFDTjtvQkFDaEN4RSxRQUFReUUsS0FBSyxDQUFDLGdDQUFnQ0QsTUFBTU8sTUFBTTtnQkFFMUQsWUFBWTtnQkFDWiw0QkFBNEI7Z0JBQzlCOztZQUVBN0UsT0FBTzhELGdCQUFnQixDQUFDLFNBQVNPO1lBQ2pDckUsT0FBTzhELGdCQUFnQixDQUFDLHNCQUFzQmM7WUFFOUM7Z0RBQU87b0JBQ0w1RSxPQUFPOEUsbUJBQW1CLENBQUMsU0FBU1Q7b0JBQ3BDckUsT0FBTzhFLG1CQUFtQixDQUFDLHNCQUFzQkY7Z0JBQ25EOztRQUNGO3VDQUFHLEVBQUU7QUFDUDtBQUVBLFNBQVM7QUFDRixTQUFTRztJQUNkekYsZ0RBQVNBO2lEQUFDO1lBQ1IsWUFBWTtZQUNaLE1BQU0wRjtnRkFBeUI7b0JBQzdCbEYsUUFBUUMsR0FBRyxDQUFDLDRCQUE0QnFELFNBQVM2QixlQUFlO2dCQUNsRTs7WUFFQSxXQUFXO1lBQ1gsTUFBTUM7c0VBQWUsSUFBTXBGLFFBQVFDLEdBQUcsQ0FBQzs7WUFDdkMsTUFBTW9GO3VFQUFnQixJQUFNckYsUUFBUUMsR0FBRyxDQUFDOztZQUV4Q3FELFNBQVNVLGdCQUFnQixDQUFDLG9CQUFvQmtCO1lBQzlDaEYsT0FBTzhELGdCQUFnQixDQUFDLFVBQVVvQjtZQUNsQ2xGLE9BQU84RCxnQkFBZ0IsQ0FBQyxXQUFXcUI7WUFFbkM7eURBQU87b0JBQ0wvQixTQUFTMEIsbUJBQW1CLENBQUMsb0JBQW9CRTtvQkFDakRoRixPQUFPOEUsbUJBQW1CLENBQUMsVUFBVUk7b0JBQ3JDbEYsT0FBTzhFLG1CQUFtQixDQUFDLFdBQVdLO2dCQUN4Qzs7UUFDRjtnREFBRyxFQUFFO0FBQ1A7QUFFQSxXQUFXO0FBQ0osU0FBU0M7SUFDZHpFO0lBQ0F1QztJQUNBa0I7SUFDQVc7SUFFQSxxQkFBTyw4REFBQ3JFOzs7OztBQUNWIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHpoYW9zaWhhb1xcRGVza3RvcFxccHl0aG9uX3N0dWR5XFx0b255X3Byb2plY3RcXGJhcmJlcnNob3Atd2Vic2l0ZVxcc3JjXFxjb21wb25lbnRzXFxwZXJmb3JtYW5jZVxcd2ViLXZpdGFscy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgb25DTFMsIG9uSU5QLCBvbkZDUCwgb25MQ1AsIG9uVFRGQiB9IGZyb20gJ3dlYi12aXRhbHMnXG5cbi8vIFdlYiBWaXRhbHMg5oyH5qCH57G75Z6LXG5pbnRlcmZhY2UgTWV0cmljIHtcbiAgbmFtZTogc3RyaW5nXG4gIHZhbHVlOiBudW1iZXJcbiAgcmF0aW5nOiAnZ29vZCcgfCAnbmVlZHMtaW1wcm92ZW1lbnQnIHwgJ3Bvb3InXG4gIGRlbHRhOiBudW1iZXJcbiAgaWQ6IHN0cmluZ1xufVxuXG4vLyDlj5HpgIHmjIfmoIfliLDliIbmnpDmnI3liqFcbmZ1bmN0aW9uIHNlbmRUb0FuYWx5dGljcyhtZXRyaWM6IE1ldHJpYykge1xuICAvLyDov5nph4zlj6/ku6Xlj5HpgIHliLAgR29vZ2xlIEFuYWx5dGljcywgVmVyY2VsIEFuYWx5dGljcyDnrYlcbiAgY29uc29sZS5sb2coJ1dlYiBWaXRhbHM6JywgbWV0cmljKVxuICBcbiAgLy8g56S65L6L77ya5Y+R6YCB5YiwIEdvb2dsZSBBbmFseXRpY3NcbiAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmICh3aW5kb3cgYXMgYW55KS5ndGFnKSB7XG4gICAgKHdpbmRvdyBhcyBhbnkpLmd0YWcoJ2V2ZW50JywgbWV0cmljLm5hbWUsIHtcbiAgICAgIGV2ZW50X2NhdGVnb3J5OiAnV2ViIFZpdGFscycsXG4gICAgICBldmVudF9sYWJlbDogbWV0cmljLmlkLFxuICAgICAgdmFsdWU6IE1hdGgucm91bmQobWV0cmljLm5hbWUgPT09ICdDTFMnID8gbWV0cmljLnZhbHVlICogMTAwMCA6IG1ldHJpYy52YWx1ZSksXG4gICAgICBub25faW50ZXJhY3Rpb246IHRydWUsXG4gICAgfSlcbiAgfVxufVxuXG4vLyBXZWIgVml0YWxzIOebkeaOp+e7hOS7tlxuZXhwb3J0IGZ1bmN0aW9uIFdlYlZpdGFscygpIHtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyDntK/np6/luIPlsYDlgY/np7sgKEN1bXVsYXRpdmUgTGF5b3V0IFNoaWZ0KVxuICAgIG9uQ0xTKHNlbmRUb0FuYWx5dGljcylcblxuICAgIC8vIOS6pOS6kuWIsOS4i+asoee7mOWItiAoSW50ZXJhY3Rpb24gdG8gTmV4dCBQYWludClcbiAgICBvbklOUChzZW5kVG9BbmFseXRpY3MpXG5cbiAgICAvLyDpppbmrKHlhoXlrrnnu5jliLYgKEZpcnN0IENvbnRlbnRmdWwgUGFpbnQpXG4gICAgb25GQ1Aoc2VuZFRvQW5hbHl0aWNzKVxuXG4gICAgLy8g5pyA5aSn5YaF5a6557uY5Yi2IChMYXJnZXN0IENvbnRlbnRmdWwgUGFpbnQpXG4gICAgb25MQ1Aoc2VuZFRvQW5hbHl0aWNzKVxuXG4gICAgLy8g6aaW5a2X6IqC5pe26Ze0IChUaW1lIHRvIEZpcnN0IEJ5dGUpXG4gICAgb25UVEZCKHNlbmRUb0FuYWx5dGljcylcbiAgfSwgW10pXG5cbiAgcmV0dXJuIG51bGxcbn1cblxuLy8g5oCn6IO955uR5o6nIEhvb2tcbmV4cG9ydCBmdW5jdGlvbiB1c2VQZXJmb3JtYW5jZU1vbml0b3JpbmcoKSB7XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8g55uR5o6n6aG16Z2i5Yqg6L295oCn6IO9XG4gICAgY29uc3Qgb2JzZXJ2ZXIgPSBuZXcgUGVyZm9ybWFuY2VPYnNlcnZlcigobGlzdCkgPT4ge1xuICAgICAgZm9yIChjb25zdCBlbnRyeSBvZiBsaXN0LmdldEVudHJpZXMoKSkge1xuICAgICAgICBpZiAoZW50cnkuZW50cnlUeXBlID09PSAnbmF2aWdhdGlvbicpIHtcbiAgICAgICAgICBjb25zdCBuYXZFbnRyeSA9IGVudHJ5IGFzIFBlcmZvcm1hbmNlTmF2aWdhdGlvblRpbWluZ1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdOYXZpZ2F0aW9uIFRpbWluZzonLCB7XG4gICAgICAgICAgICBkb21Db250ZW50TG9hZGVkOiBuYXZFbnRyeS5kb21Db250ZW50TG9hZGVkRXZlbnRFbmQgLSBuYXZFbnRyeS5kb21Db250ZW50TG9hZGVkRXZlbnRTdGFydCxcbiAgICAgICAgICAgIGxvYWRDb21wbGV0ZTogbmF2RW50cnkubG9hZEV2ZW50RW5kIC0gbmF2RW50cnkubG9hZEV2ZW50U3RhcnQsXG4gICAgICAgICAgICBmaXJzdEJ5dGU6IG5hdkVudHJ5LnJlc3BvbnNlU3RhcnQgLSBuYXZFbnRyeS5yZXF1ZXN0U3RhcnQsXG4gICAgICAgICAgICBkb21JbnRlcmFjdGl2ZTogbmF2RW50cnkuZG9tSW50ZXJhY3RpdmUgLSBuYXZFbnRyeS5uYXZpZ2F0aW9uU3RhcnQsXG4gICAgICAgICAgfSlcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgaWYgKGVudHJ5LmVudHJ5VHlwZSA9PT0gJ3Jlc291cmNlJykge1xuICAgICAgICAgIGNvbnN0IHJlc291cmNlRW50cnkgPSBlbnRyeSBhcyBQZXJmb3JtYW5jZVJlc291cmNlVGltaW5nXG4gICAgICAgICAgLy8g55uR5o6n5oWi6LWE5rqQXG4gICAgICAgICAgaWYgKHJlc291cmNlRW50cnkuZHVyYXRpb24gPiAxMDAwKSB7XG4gICAgICAgICAgICBjb25zb2xlLndhcm4oJ1Nsb3cgcmVzb3VyY2U6Jywge1xuICAgICAgICAgICAgICBuYW1lOiByZXNvdXJjZUVudHJ5Lm5hbWUsXG4gICAgICAgICAgICAgIGR1cmF0aW9uOiByZXNvdXJjZUVudHJ5LmR1cmF0aW9uLFxuICAgICAgICAgICAgICBzaXplOiByZXNvdXJjZUVudHJ5LnRyYW5zZmVyU2l6ZSxcbiAgICAgICAgICAgIH0pXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfSlcblxuICAgIG9ic2VydmVyLm9ic2VydmUoeyBlbnRyeVR5cGVzOiBbJ25hdmlnYXRpb24nLCAncmVzb3VyY2UnXSB9KVxuXG4gICAgcmV0dXJuICgpID0+IG9ic2VydmVyLmRpc2Nvbm5lY3QoKVxuICB9LCBbXSlcblxuICAvLyDnm5HmjqflhoXlrZjkvb/nlKhcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBjaGVja01lbW9yeSA9ICgpID0+IHtcbiAgICAgIGlmICgnbWVtb3J5JyBpbiBwZXJmb3JtYW5jZSkge1xuICAgICAgICBjb25zdCBtZW1vcnkgPSAocGVyZm9ybWFuY2UgYXMgYW55KS5tZW1vcnlcbiAgICAgICAgY29uc29sZS5sb2coJ01lbW9yeSBVc2FnZTonLCB7XG4gICAgICAgICAgdXNlZDogTWF0aC5yb3VuZChtZW1vcnkudXNlZEpTSGVhcFNpemUgLyAxMDQ4NTc2KSArICcgTUInLFxuICAgICAgICAgIHRvdGFsOiBNYXRoLnJvdW5kKG1lbW9yeS50b3RhbEpTSGVhcFNpemUgLyAxMDQ4NTc2KSArICcgTUInLFxuICAgICAgICAgIGxpbWl0OiBNYXRoLnJvdW5kKG1lbW9yeS5qc0hlYXBTaXplTGltaXQgLyAxMDQ4NTc2KSArICcgTUInLFxuICAgICAgICB9KVxuICAgICAgfVxuICAgIH1cblxuICAgIGNvbnN0IGludGVydmFsID0gc2V0SW50ZXJ2YWwoY2hlY2tNZW1vcnksIDMwMDAwKSAvLyDmr48zMOenkuajgOafpeS4gOasoVxuICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKGludGVydmFsKVxuICB9LCBbXSlcbn1cblxuLy8g5Zu+54mH5oeS5Yqg6L2955uR5o6nXG5leHBvcnQgZnVuY3Rpb24gdXNlSW1hZ2VMb2FkaW5nTW9uaXRvcmluZygpIHtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBpbWFnZXMgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKCdpbWdbbG9hZGluZz1cImxhenlcIl0nKVxuICAgIFxuICAgIGNvbnN0IG9ic2VydmVyID0gbmV3IEludGVyc2VjdGlvbk9ic2VydmVyKChlbnRyaWVzKSA9PiB7XG4gICAgICBlbnRyaWVzLmZvckVhY2goKGVudHJ5KSA9PiB7XG4gICAgICAgIGlmIChlbnRyeS5pc0ludGVyc2VjdGluZykge1xuICAgICAgICAgIGNvbnN0IGltZyA9IGVudHJ5LnRhcmdldCBhcyBIVE1MSW1hZ2VFbGVtZW50XG4gICAgICAgICAgY29uc3Qgc3RhcnRUaW1lID0gcGVyZm9ybWFuY2Uubm93KClcbiAgICAgICAgICBcbiAgICAgICAgICBpbWcuYWRkRXZlbnRMaXN0ZW5lcignbG9hZCcsICgpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGxvYWRUaW1lID0gcGVyZm9ybWFuY2Uubm93KCkgLSBzdGFydFRpbWVcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdJbWFnZSBsb2FkZWQ6Jywge1xuICAgICAgICAgICAgICBzcmM6IGltZy5zcmMsXG4gICAgICAgICAgICAgIGxvYWRUaW1lOiBNYXRoLnJvdW5kKGxvYWRUaW1lKSxcbiAgICAgICAgICAgICAgbmF0dXJhbFdpZHRoOiBpbWcubmF0dXJhbFdpZHRoLFxuICAgICAgICAgICAgICBuYXR1cmFsSGVpZ2h0OiBpbWcubmF0dXJhbEhlaWdodCxcbiAgICAgICAgICAgIH0pXG4gICAgICAgICAgfSlcbiAgICAgICAgICBcbiAgICAgICAgICBvYnNlcnZlci51bm9ic2VydmUoaW1nKVxuICAgICAgICB9XG4gICAgICB9KVxuICAgIH0pXG5cbiAgICBpbWFnZXMuZm9yRWFjaCgoaW1nKSA9PiBvYnNlcnZlci5vYnNlcnZlKGltZykpXG5cbiAgICByZXR1cm4gKCkgPT4gb2JzZXJ2ZXIuZGlzY29ubmVjdCgpXG4gIH0sIFtdKVxufVxuXG4vLyDplJnor6/nm5HmjqdcbmV4cG9ydCBmdW5jdGlvbiB1c2VFcnJvck1vbml0b3JpbmcoKSB7XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaGFuZGxlRXJyb3IgPSAoZXZlbnQ6IEVycm9yRXZlbnQpID0+IHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0phdmFTY3JpcHQgRXJyb3I6Jywge1xuICAgICAgICBtZXNzYWdlOiBldmVudC5tZXNzYWdlLFxuICAgICAgICBmaWxlbmFtZTogZXZlbnQuZmlsZW5hbWUsXG4gICAgICAgIGxpbmVubzogZXZlbnQubGluZW5vLFxuICAgICAgICBjb2xubzogZXZlbnQuY29sbm8sXG4gICAgICAgIGVycm9yOiBldmVudC5lcnJvcixcbiAgICAgIH0pXG4gICAgICBcbiAgICAgIC8vIOWPkemAgemUmeivr+WIsOebkeaOp+acjeWKoVxuICAgICAgLy8gc2VuZEVycm9yVG9TZXJ2aWNlKGV2ZW50KVxuICAgIH1cblxuICAgIGNvbnN0IGhhbmRsZVVuaGFuZGxlZFJlamVjdGlvbiA9IChldmVudDogUHJvbWlzZVJlamVjdGlvbkV2ZW50KSA9PiB7XG4gICAgICBjb25zb2xlLmVycm9yKCdVbmhhbmRsZWQgUHJvbWlzZSBSZWplY3Rpb246JywgZXZlbnQucmVhc29uKVxuICAgICAgXG4gICAgICAvLyDlj5HpgIHplJnor6/liLDnm5HmjqfmnI3liqFcbiAgICAgIC8vIHNlbmRFcnJvclRvU2VydmljZShldmVudClcbiAgICB9XG5cbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignZXJyb3InLCBoYW5kbGVFcnJvcilcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigndW5oYW5kbGVkcmVqZWN0aW9uJywgaGFuZGxlVW5oYW5kbGVkUmVqZWN0aW9uKVxuXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdlcnJvcicsIGhhbmRsZUVycm9yKVxuICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3VuaGFuZGxlZHJlamVjdGlvbicsIGhhbmRsZVVuaGFuZGxlZFJlamVjdGlvbilcbiAgICB9XG4gIH0sIFtdKVxufVxuXG4vLyDnlKjmiLfkvZPpqoznm5HmjqdcbmV4cG9ydCBmdW5jdGlvbiB1c2VVc2VyRXhwZXJpZW5jZU1vbml0b3JpbmcoKSB7XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8g55uR5o6n6aG16Z2i5Y+v6KeB5oCn5Y+Y5YyWXG4gICAgY29uc3QgaGFuZGxlVmlzaWJpbGl0eUNoYW5nZSA9ICgpID0+IHtcbiAgICAgIGNvbnNvbGUubG9nKCdQYWdlIHZpc2liaWxpdHkgY2hhbmdlZDonLCBkb2N1bWVudC52aXNpYmlsaXR5U3RhdGUpXG4gICAgfVxuXG4gICAgLy8g55uR5o6n572R57uc54q25oCB5Y+Y5YyWXG4gICAgY29uc3QgaGFuZGxlT25saW5lID0gKCkgPT4gY29uc29sZS5sb2coJ05ldHdvcms6IE9ubGluZScpXG4gICAgY29uc3QgaGFuZGxlT2ZmbGluZSA9ICgpID0+IGNvbnNvbGUubG9nKCdOZXR3b3JrOiBPZmZsaW5lJylcblxuICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ3Zpc2liaWxpdHljaGFuZ2UnLCBoYW5kbGVWaXNpYmlsaXR5Q2hhbmdlKVxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdvbmxpbmUnLCBoYW5kbGVPbmxpbmUpXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ29mZmxpbmUnLCBoYW5kbGVPZmZsaW5lKVxuXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Zpc2liaWxpdHljaGFuZ2UnLCBoYW5kbGVWaXNpYmlsaXR5Q2hhbmdlKVxuICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ29ubGluZScsIGhhbmRsZU9ubGluZSlcbiAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdvZmZsaW5lJywgaGFuZGxlT2ZmbGluZSlcbiAgICB9XG4gIH0sIFtdKVxufVxuXG4vLyDnu7zlkIjmgKfog73nm5Hmjqfnu4Tku7ZcbmV4cG9ydCBmdW5jdGlvbiBQZXJmb3JtYW5jZU1vbml0b3IoKSB7XG4gIHVzZVBlcmZvcm1hbmNlTW9uaXRvcmluZygpXG4gIHVzZUltYWdlTG9hZGluZ01vbml0b3JpbmcoKVxuICB1c2VFcnJvck1vbml0b3JpbmcoKVxuICB1c2VVc2VyRXhwZXJpZW5jZU1vbml0b3JpbmcoKVxuXG4gIHJldHVybiA8V2ViVml0YWxzIC8+XG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0Iiwib25DTFMiLCJvbklOUCIsIm9uRkNQIiwib25MQ1AiLCJvblRURkIiLCJzZW5kVG9BbmFseXRpY3MiLCJtZXRyaWMiLCJjb25zb2xlIiwibG9nIiwid2luZG93IiwiZ3RhZyIsIm5hbWUiLCJldmVudF9jYXRlZ29yeSIsImV2ZW50X2xhYmVsIiwiaWQiLCJ2YWx1ZSIsIk1hdGgiLCJyb3VuZCIsIm5vbl9pbnRlcmFjdGlvbiIsIldlYlZpdGFscyIsInVzZVBlcmZvcm1hbmNlTW9uaXRvcmluZyIsIm9ic2VydmVyIiwiUGVyZm9ybWFuY2VPYnNlcnZlciIsImxpc3QiLCJlbnRyeSIsImdldEVudHJpZXMiLCJlbnRyeVR5cGUiLCJuYXZFbnRyeSIsImRvbUNvbnRlbnRMb2FkZWQiLCJkb21Db250ZW50TG9hZGVkRXZlbnRFbmQiLCJkb21Db250ZW50TG9hZGVkRXZlbnRTdGFydCIsImxvYWRDb21wbGV0ZSIsImxvYWRFdmVudEVuZCIsImxvYWRFdmVudFN0YXJ0IiwiZmlyc3RCeXRlIiwicmVzcG9uc2VTdGFydCIsInJlcXVlc3RTdGFydCIsImRvbUludGVyYWN0aXZlIiwibmF2aWdhdGlvblN0YXJ0IiwicmVzb3VyY2VFbnRyeSIsImR1cmF0aW9uIiwid2FybiIsInNpemUiLCJ0cmFuc2ZlclNpemUiLCJvYnNlcnZlIiwiZW50cnlUeXBlcyIsImRpc2Nvbm5lY3QiLCJjaGVja01lbW9yeSIsInBlcmZvcm1hbmNlIiwibWVtb3J5IiwidXNlZCIsInVzZWRKU0hlYXBTaXplIiwidG90YWwiLCJ0b3RhbEpTSGVhcFNpemUiLCJsaW1pdCIsImpzSGVhcFNpemVMaW1pdCIsImludGVydmFsIiwic2V0SW50ZXJ2YWwiLCJjbGVhckludGVydmFsIiwidXNlSW1hZ2VMb2FkaW5nTW9uaXRvcmluZyIsImltYWdlcyIsImRvY3VtZW50IiwicXVlcnlTZWxlY3RvckFsbCIsIkludGVyc2VjdGlvbk9ic2VydmVyIiwiZW50cmllcyIsImZvckVhY2giLCJpc0ludGVyc2VjdGluZyIsImltZyIsInRhcmdldCIsInN0YXJ0VGltZSIsIm5vdyIsImFkZEV2ZW50TGlzdGVuZXIiLCJsb2FkVGltZSIsInNyYyIsIm5hdHVyYWxXaWR0aCIsIm5hdHVyYWxIZWlnaHQiLCJ1bm9ic2VydmUiLCJ1c2VFcnJvck1vbml0b3JpbmciLCJoYW5kbGVFcnJvciIsImV2ZW50IiwiZXJyb3IiLCJtZXNzYWdlIiwiZmlsZW5hbWUiLCJsaW5lbm8iLCJjb2xubyIsImhhbmRsZVVuaGFuZGxlZFJlamVjdGlvbiIsInJlYXNvbiIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJ1c2VVc2VyRXhwZXJpZW5jZU1vbml0b3JpbmciLCJoYW5kbGVWaXNpYmlsaXR5Q2hhbmdlIiwidmlzaWJpbGl0eVN0YXRlIiwiaGFuZGxlT25saW5lIiwiaGFuZGxlT2ZmbGluZSIsIlBlcmZvcm1hbmNlTW9uaXRvciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/performance/web-vitals.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ Button auto */ \n\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className = \"\", variant = \"default\", size = \"default\", asChild = false, loading = false, icon, rightIcon, children, disabled, ...props }, ref)=>{\n    const baseClasses = \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden group\";\n    const variantClasses = {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90 hover:shadow-lg hover:scale-105 active:scale-95\",\n        outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-primary/50 hover:shadow-md hover:scale-105 active:scale-95\",\n        secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:shadow-md hover:scale-105 active:scale-95\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground hover:scale-105 active:scale-95\",\n        link: \"text-primary underline-offset-4 hover:underline hover:scale-105 active:scale-95\",\n        gradient: \"bg-gradient-to-r from-primary to-accent text-primary-foreground hover:from-primary/90 hover:to-accent/90 hover:shadow-lg hover:scale-105 active:scale-95\",\n        shine: \"bg-primary text-primary-foreground hover:shadow-lg hover:scale-105 active:scale-95 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-700\"\n    };\n    const sizeClasses = {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        xl: \"h-12 rounded-lg px-10 text-base\",\n        icon: \"h-10 w-10\"\n    };\n    const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`.trim();\n    if (asChild && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.isValidElement(children)) {\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.cloneElement(children, {\n            className: classes,\n            ref,\n            ...props\n        });\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: classes,\n        ref: ref,\n        disabled: disabled || loading,\n        ...props,\n        children: [\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 13\n                    }, undefined),\n                    \"加载中...\"\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"mr-2 transition-transform group-hover:scale-110\",\n                        children: icon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 22\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"transition-transform group-hover:translate-x-0.5\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 13\n                    }, undefined),\n                    rightIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 transition-transform group-hover:scale-110 group-hover:translate-x-0.5\",\n                        children: rightIcon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 27\n                    }, undefined)\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute inset-0 overflow-hidden rounded-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"absolute inset-0 bg-white/20 scale-0 group-active:scale-100 transition-transform duration-300 rounded-full\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 47,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Card,CardHeader,CardFooter,CardTitle,CardDescription,CardContent auto */ \n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, hover = false, interactive = false, gradient = false, ...props }, ref)=>{\n    const baseClasses = \"rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-300\";\n    const hoverClasses = hover ? \"hover:shadow-lg hover:scale-105 hover:-translate-y-1\" : \"\";\n    const interactiveClasses = interactive ? \"cursor-pointer hover:shadow-xl hover:scale-105 hover:-translate-y-2 active:scale-95 group\" : \"\";\n    const gradientClasses = gradient ? \"bg-gradient-to-br from-card to-card/80 border-primary/20\" : \"\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseClasses, hoverClasses, interactiveClasses, gradientClasses, className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 21,\n        columnNumber: 7\n    }, undefined);\n});\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 35,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 74,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\python_study\\\\tony_project\\\\barbershop-website\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 82,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatPhoneNumber: () => (/* binding */ formatPhoneNumber),\n/* harmony export */   formatTime: () => (/* binding */ formatTime)\n/* harmony export */ });\nfunction cn(...inputs) {\n    return inputs.filter(Boolean).join(' ');\n}\nfunction formatPhoneNumber(phone) {\n    const cleaned = phone.replace(/\\D/g, '');\n    const match = cleaned.match(/^(\\d{3})(\\d{3})(\\d{4})$/);\n    if (match) {\n        return `(${match[1]}) ${match[2]}-${match[3]}`;\n    }\n    return phone;\n}\nfunction formatTime(time) {\n    const [hours, minutes] = time.split(':');\n    const hour = parseInt(hours, 10);\n    const ampm = hour >= 12 ? 'PM' : 'AM';\n    const displayHour = hour % 12 || 12;\n    return `${displayHour}:${minutes} ${ampm}`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/styled-jsx","vendor-chunks/web-vitals","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5Czhaosihao%5CDesktop%5Cpython_study%5Ctony_project%5Cbarbershop-website%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Czhaosihao%5CDesktop%5Cpython_study%5Ctony_project%5Cbarbershop-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();