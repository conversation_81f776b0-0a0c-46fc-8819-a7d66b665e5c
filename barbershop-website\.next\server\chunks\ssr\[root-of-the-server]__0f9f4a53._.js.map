{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/lib/utils.ts"], "sourcesContent": ["export function cn(...inputs: (string | undefined | null | boolean)[]) {\n  return inputs.filter(Boolean).join(' ')\n}\n\nexport function formatPhoneNumber(phone: string): string {\n  const cleaned = phone.replace(/\\D/g, '')\n  const match = cleaned.match(/^(\\d{3})(\\d{3})(\\d{4})$/)\n  if (match) {\n    return `(${match[1]}) ${match[2]}-${match[3]}`\n  }\n  return phone\n}\n\nexport function formatTime(time: string): string {\n  const [hours, minutes] = time.split(':')\n  const hour = parseInt(hours, 10)\n  const ampm = hour >= 12 ? 'PM' : 'AM'\n  const displayHour = hour % 12 || 12\n  return `${displayHour}:${minutes} ${ampm}`\n}\n"], "names": [], "mappings": ";;;;;AAAO,SAAS,GAAG,GAAG,MAA+C;IACnE,OAAO,OAAO,MAAM,CAAC,SAAS,IAAI,CAAC;AACrC;AAEO,SAAS,kBAAkB,KAAa;IAC7C,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO;IACrC,MAAM,QAAQ,QAAQ,KAAK,CAAC;IAC5B,IAAI,OAAO;QACT,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE;IAChD;IACA,OAAO;AACT;AAEO,SAAS,WAAW,IAAY;IACrC,MAAM,CAAC,OAAO,QAAQ,GAAG,KAAK,KAAK,CAAC;IACpC,MAAM,OAAO,SAAS,OAAO;IAC7B,MAAM,OAAO,QAAQ,KAAK,OAAO;IACjC,MAAM,cAAc,OAAO,MAAM;IACjC,OAAO,GAAG,YAAY,CAAC,EAAE,QAAQ,CAAC,EAAE,MAAM;AAC5C", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/components/ui/button.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: \"default\" | \"outline\" | \"secondary\" | \"ghost\" | \"link\" | \"gradient\" | \"shine\"\n  size?: \"default\" | \"sm\" | \"lg\" | \"xl\" | \"icon\"\n  asChild?: boolean\n  loading?: boolean\n  icon?: React.ReactNode\n  rightIcon?: React.ReactNode\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className = \"\", variant = \"default\", size = \"default\", asChild = false, loading = false, icon, rightIcon, children, disabled, ...props }, ref) => {\n    const baseClasses = \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden group\"\n\n    const variantClasses = {\n      default: \"bg-primary text-primary-foreground hover:bg-primary/90 hover:shadow-lg hover:scale-105 active:scale-95\",\n      outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-primary/50 hover:shadow-md hover:scale-105 active:scale-95\",\n      secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:shadow-md hover:scale-105 active:scale-95\",\n      ghost: \"hover:bg-accent hover:text-accent-foreground hover:scale-105 active:scale-95\",\n      link: \"text-primary underline-offset-4 hover:underline hover:scale-105 active:scale-95\",\n      gradient: \"bg-gradient-to-r from-primary to-accent text-primary-foreground hover:from-primary/90 hover:to-accent/90 hover:shadow-lg hover:scale-105 active:scale-95\",\n      shine: \"bg-primary text-primary-foreground hover:shadow-lg hover:scale-105 active:scale-95 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-700\",\n    }\n\n    const sizeClasses = {\n      default: \"h-10 px-4 py-2\",\n      sm: \"h-9 rounded-md px-3\",\n      lg: \"h-11 rounded-md px-8\",\n      xl: \"h-12 rounded-lg px-10 text-base\",\n      icon: \"h-10 w-10\",\n    }\n\n    const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`.trim()\n\n    if (asChild && React.isValidElement(children)) {\n      return React.cloneElement(children, {\n        className: classes,\n        ref,\n        ...props,\n      })\n    }\n\n    return (\n      <button\n        className={classes}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading ? (\n          <>\n            <div className=\"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2\" />\n            加载中...\n          </>\n        ) : (\n          <>\n            {icon && <span className=\"mr-2 transition-transform group-hover:scale-110\">{icon}</span>}\n            <span className=\"transition-transform group-hover:translate-x-0.5\">{children}</span>\n            {rightIcon && <span className=\"ml-2 transition-transform group-hover:scale-110 group-hover:translate-x-0.5\">{rightIcon}</span>}\n          </>\n        )}\n\n        {/* Ripple effect */}\n        <span className=\"absolute inset-0 overflow-hidden rounded-md\">\n          <span className=\"absolute inset-0 bg-white/20 scale-0 group-active:scale-100 transition-transform duration-300 rounded-full\" />\n        </span>\n      </button>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAaA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,YAAY,EAAE,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,UAAU,KAAK,EAAE,UAAU,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC3I,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,SAAS;QACT,WAAW;QACX,OAAO;QACP,MAAM;QACN,UAAU;QACV,OAAO;IACT;IAEA,MAAM,cAAc;QAClB,SAAS;QACT,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,MAAM,UAAU,GAAG,YAAY,CAAC,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC,IAAI;IAElG,IAAI,yBAAW,CAAA,GAAA,qMAAA,CAAA,iBAAoB,AAAD,EAAE,WAAW;QAC7C,qBAAO,CAAA,GAAA,qMAAA,CAAA,eAAkB,AAAD,EAAE,UAAU;YAClC,WAAW;YACX;YACA,GAAG,KAAK;QACV;IACF;IAEA,qBACE,8OAAC;QACC,WAAW;QACX,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,wBACC;;kCACE,8OAAC;wBAAI,WAAU;;;;;;oBAAwF;;6CAIzG;;oBACG,sBAAQ,8OAAC;wBAAK,WAAU;kCAAmD;;;;;;kCAC5E,8OAAC;wBAAK,WAAU;kCAAoD;;;;;;oBACnE,2BAAa,8OAAC;wBAAK,WAAU;kCAA+E;;;;;;;;0BAKjH,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAK,WAAU;;;;;;;;;;;;;;;;;AAIxB;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/components/layout/navbar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport Link from \"next/link\"\nimport { usePathname } from \"next/navigation\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Button } from \"@/components/ui/button\"\n\nconst navigation = [\n  { name: \"首页\", href: \"/\" },\n  { name: \"服务\", href: \"/services\" },\n  { name: \"关于我们\", href: \"/about\" },\n  { name: \"作品展示\", href: \"/gallery\" },\n  { name: \"联系我们\", href: \"/contact\" },\n]\n\nexport function Navbar() {\n  const [isOpen, setIsOpen] = React.useState(false)\n  const pathname = usePathname()\n\n  return (\n    <nav className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <span className=\"text-2xl text-primary\">✂️</span>\n              <span className=\"text-xl font-bold text-primary\">Classic Cuts</span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:block\">\n            <div className=\"ml-10 flex items-baseline space-x-4\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={cn(\n                    \"px-3 py-2 rounded-md text-sm font-medium transition-colors hover:text-primary\",\n                    pathname === item.href\n                      ? \"text-primary bg-primary/10\"\n                      : \"text-muted-foreground hover:text-primary\"\n                  )}\n                >\n                  {item.name}\n                </Link>\n              ))}\n            </div>\n          </div>\n\n          {/* Book Appointment Button */}\n          <div className=\"hidden md:block\">\n            <Button asChild>\n              <Link href=\"/booking\">立即预约</Link>\n            </Button>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={() => setIsOpen(!isOpen)}\n              aria-expanded=\"false\"\n            >\n              <span className=\"sr-only\">打开主菜单</span>\n              {isOpen ? (\n                <span className=\"text-xl\" aria-hidden=\"true\">✕</span>\n              ) : (\n                <span className=\"text-xl\" aria-hidden=\"true\">☰</span>\n              )}\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      {isOpen && (\n        <div className=\"md:hidden\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-background border-t\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={cn(\n                  \"block px-3 py-2 rounded-md text-base font-medium transition-colors\",\n                  pathname === item.href\n                    ? \"text-primary bg-primary/10\"\n                    : \"text-muted-foreground hover:text-primary hover:bg-primary/5\"\n                )}\n                onClick={() => setIsOpen(false)}\n              >\n                {item.name}\n              </Link>\n            ))}\n            <div className=\"px-3 py-2\">\n              <Button asChild className=\"w-full\">\n                <Link href=\"/booking\">立即预约</Link>\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AAPA;;;;;;;AASA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAM,MAAM;IAAI;IACxB;QAAE,MAAM;QAAM,MAAM;IAAY;IAChC;QAAE,MAAM;QAAQ,MAAM;IAAS;IAC/B;QAAE,MAAM;QAAQ,MAAM;IAAW;IACjC;QAAE,MAAM;QAAQ,MAAM;IAAW;CAClC;AAEM,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAC3C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;wCAAK,WAAU;kDAAiC;;;;;;;;;;;;;;;;;sCAKrD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iFACA,aAAa,KAAK,IAAI,GAClB,+BACA;kDAGL,KAAK,IAAI;uCATL,KAAK,IAAI;;;;;;;;;;;;;;;sCAgBtB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAW;;;;;;;;;;;;;;;;sCAK1B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,UAAU,CAAC;gCAC1B,iBAAc;;kDAEd,8OAAC;wCAAK,WAAU;kDAAU;;;;;;oCACzB,uBACC,8OAAC;wCAAK,WAAU;wCAAU,eAAY;kDAAO;;;;;6DAE7C,8OAAC;wCAAK,WAAU;wCAAU,eAAY;kDAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQtD,wBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sEACA,aAAa,KAAK,IAAI,GAClB,+BACA;gCAEN,SAAS,IAAM,UAAU;0CAExB,KAAK,IAAI;+BAVL,KAAK,IAAI;;;;;sCAalB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAC,WAAU;0CACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtC", "debugId": null}}, {"offset": {"line": 420, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/components/animations/fade-in.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect, useRef, useState } from \"react\"\n\ninterface FadeInProps {\n  children: React.ReactNode\n  delay?: number\n  duration?: number\n  direction?: \"up\" | \"down\" | \"left\" | \"right\" | \"none\"\n  distance?: number\n  className?: string\n  threshold?: number\n}\n\nexport function FadeIn({\n  children,\n  delay = 0,\n  duration = 600,\n  direction = \"up\",\n  distance = 30,\n  className = \"\",\n  threshold = 0.1\n}: FadeInProps) {\n  const [isVisible, setIsVisible] = useState(false)\n  const elementRef = useRef<HTMLDivElement>(null)\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setTimeout(() => {\n            setIsVisible(true)\n          }, delay)\n        }\n      },\n      {\n        threshold,\n        rootMargin: \"0px 0px -50px 0px\"\n      }\n    )\n\n    const currentElement = elementRef.current\n    if (currentElement) {\n      observer.observe(currentElement)\n    }\n\n    return () => {\n      if (currentElement) {\n        observer.unobserve(currentElement)\n      }\n    }\n  }, [delay, threshold])\n\n  const getTransform = () => {\n    if (isVisible) return \"translate3d(0, 0, 0)\"\n    \n    switch (direction) {\n      case \"up\":\n        return `translate3d(0, ${distance}px, 0)`\n      case \"down\":\n        return `translate3d(0, -${distance}px, 0)`\n      case \"left\":\n        return `translate3d(${distance}px, 0, 0)`\n      case \"right\":\n        return `translate3d(-${distance}px, 0, 0)`\n      default:\n        return \"translate3d(0, 0, 0)\"\n    }\n  }\n\n  return (\n    <div\n      ref={elementRef}\n      className={className}\n      style={{\n        opacity: isVisible ? 1 : 0,\n        transform: getTransform(),\n        transition: `opacity ${duration}ms ease-out, transform ${duration}ms ease-out`,\n        willChange: \"opacity, transform\"\n      }}\n    >\n      {children}\n    </div>\n  )\n}\n\ninterface StaggeredFadeInProps {\n  children: React.ReactNode[]\n  delay?: number\n  staggerDelay?: number\n  duration?: number\n  direction?: \"up\" | \"down\" | \"left\" | \"right\" | \"none\"\n  distance?: number\n  className?: string\n}\n\nexport function StaggeredFadeIn({\n  children,\n  delay = 0,\n  staggerDelay = 100,\n  duration = 600,\n  direction = \"up\",\n  distance = 30,\n  className = \"\"\n}: StaggeredFadeInProps) {\n  return (\n    <>\n      {children.map((child, index) => (\n        <FadeIn\n          key={index}\n          delay={delay + index * staggerDelay}\n          duration={duration}\n          direction={direction}\n          distance={distance}\n          className={className}\n        >\n          {child}\n        </FadeIn>\n      ))}\n    </>\n  )\n}\n\ninterface ScaleInProps {\n  children: React.ReactNode\n  delay?: number\n  duration?: number\n  scale?: number\n  className?: string\n  threshold?: number\n}\n\nexport function ScaleIn({\n  children,\n  delay = 0,\n  duration = 600,\n  scale = 0.8,\n  className = \"\",\n  threshold = 0.1\n}: ScaleInProps) {\n  const [isVisible, setIsVisible] = useState(false)\n  const elementRef = useRef<HTMLDivElement>(null)\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setTimeout(() => {\n            setIsVisible(true)\n          }, delay)\n        }\n      },\n      {\n        threshold,\n        rootMargin: \"0px 0px -50px 0px\"\n      }\n    )\n\n    const currentElement = elementRef.current\n    if (currentElement) {\n      observer.observe(currentElement)\n    }\n\n    return () => {\n      if (currentElement) {\n        observer.unobserve(currentElement)\n      }\n    }\n  }, [delay, threshold])\n\n  return (\n    <div\n      ref={elementRef}\n      className={className}\n      style={{\n        opacity: isVisible ? 1 : 0,\n        transform: isVisible ? \"scale(1)\" : `scale(${scale})`,\n        transition: `opacity ${duration}ms ease-out, transform ${duration}ms ease-out`,\n        willChange: \"opacity, transform\"\n      }}\n    >\n      {children}\n    </div>\n  )\n}\n\ninterface SlideInProps {\n  children: React.ReactNode\n  delay?: number\n  duration?: number\n  direction?: \"left\" | \"right\"\n  distance?: number\n  className?: string\n  threshold?: number\n}\n\nexport function SlideIn({\n  children,\n  delay = 0,\n  duration = 800,\n  direction = \"left\",\n  distance = 100,\n  className = \"\",\n  threshold = 0.1\n}: SlideInProps) {\n  const [isVisible, setIsVisible] = useState(false)\n  const elementRef = useRef<HTMLDivElement>(null)\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setTimeout(() => {\n            setIsVisible(true)\n          }, delay)\n        }\n      },\n      {\n        threshold,\n        rootMargin: \"0px 0px -50px 0px\"\n      }\n    )\n\n    const currentElement = elementRef.current\n    if (currentElement) {\n      observer.observe(currentElement)\n    }\n\n    return () => {\n      if (currentElement) {\n        observer.unobserve(currentElement)\n      }\n    }\n  }, [delay, threshold])\n\n  const getTransform = () => {\n    if (isVisible) return \"translateX(0)\"\n    return direction === \"left\" ? `translateX(-${distance}px)` : `translateX(${distance}px)`\n  }\n\n  return (\n    <div\n      ref={elementRef}\n      className={className}\n      style={{\n        opacity: isVisible ? 1 : 0,\n        transform: getTransform(),\n        transition: `opacity ${duration}ms ease-out, transform ${duration}ms ease-out`,\n        willChange: \"opacity, transform\"\n      }}\n    >\n      {children}\n    </div>\n  )\n}\n\ninterface CountUpProps {\n  end: number\n  start?: number\n  duration?: number\n  delay?: number\n  suffix?: string\n  prefix?: string\n  className?: string\n}\n\nexport function CountUp({\n  end,\n  start = 0,\n  duration = 2000,\n  delay = 0,\n  suffix = \"\",\n  prefix = \"\",\n  className = \"\"\n}: CountUpProps) {\n  const [count, setCount] = useState(start)\n  const [isVisible, setIsVisible] = useState(false)\n  const elementRef = useRef<HTMLDivElement>(null)\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting && !isVisible) {\n          setIsVisible(true)\n          setTimeout(() => {\n            const startTime = Date.now()\n            const startValue = start\n            const endValue = end\n            const totalDuration = duration\n\n            const updateCount = () => {\n              const elapsed = Date.now() - startTime\n              const progress = Math.min(elapsed / totalDuration, 1)\n              \n              // Easing function for smooth animation\n              const easeOutQuart = 1 - Math.pow(1 - progress, 4)\n              const currentValue = Math.round(startValue + (endValue - startValue) * easeOutQuart)\n              \n              setCount(currentValue)\n\n              if (progress < 1) {\n                requestAnimationFrame(updateCount)\n              }\n            }\n\n            requestAnimationFrame(updateCount)\n          }, delay)\n        }\n      },\n      {\n        threshold: 0.5\n      }\n    )\n\n    const currentElement = elementRef.current\n    if (currentElement) {\n      observer.observe(currentElement)\n    }\n\n    return () => {\n      if (currentElement) {\n        observer.unobserve(currentElement)\n      }\n    }\n  }, [start, end, duration, delay, isVisible])\n\n  return (\n    <span ref={elementRef} className={className}>\n      {prefix}{count.toLocaleString()}{suffix}\n    </span>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AAFA;;;AAcO,SAAS,OAAO,EACrB,QAAQ,EACR,QAAQ,CAAC,EACT,WAAW,GAAG,EACd,YAAY,IAAI,EAChB,WAAW,EAAE,EACb,YAAY,EAAE,EACd,YAAY,GAAG,EACH;IACZ,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC,CAAC,MAAM;YACN,IAAI,MAAM,cAAc,EAAE;gBACxB,WAAW;oBACT,aAAa;gBACf,GAAG;YACL;QACF,GACA;YACE;YACA,YAAY;QACd;QAGF,MAAM,iBAAiB,WAAW,OAAO;QACzC,IAAI,gBAAgB;YAClB,SAAS,OAAO,CAAC;QACnB;QAEA,OAAO;YACL,IAAI,gBAAgB;gBAClB,SAAS,SAAS,CAAC;YACrB;QACF;IACF,GAAG;QAAC;QAAO;KAAU;IAErB,MAAM,eAAe;QACnB,IAAI,WAAW,OAAO;QAEtB,OAAQ;YACN,KAAK;gBACH,OAAO,CAAC,eAAe,EAAE,SAAS,MAAM,CAAC;YAC3C,KAAK;gBACH,OAAO,CAAC,gBAAgB,EAAE,SAAS,MAAM,CAAC;YAC5C,KAAK;gBACH,OAAO,CAAC,YAAY,EAAE,SAAS,SAAS,CAAC;YAC3C,KAAK;gBACH,OAAO,CAAC,aAAa,EAAE,SAAS,SAAS,CAAC;YAC5C;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW;QACX,OAAO;YACL,SAAS,YAAY,IAAI;YACzB,WAAW;YACX,YAAY,CAAC,QAAQ,EAAE,SAAS,uBAAuB,EAAE,SAAS,WAAW,CAAC;YAC9E,YAAY;QACd;kBAEC;;;;;;AAGP;AAYO,SAAS,gBAAgB,EAC9B,QAAQ,EACR,QAAQ,CAAC,EACT,eAAe,GAAG,EAClB,WAAW,GAAG,EACd,YAAY,IAAI,EAChB,WAAW,EAAE,EACb,YAAY,EAAE,EACO;IACrB,qBACE;kBACG,SAAS,GAAG,CAAC,CAAC,OAAO,sBACpB,8OAAC;gBAEC,OAAO,QAAQ,QAAQ;gBACvB,UAAU;gBACV,WAAW;gBACX,UAAU;gBACV,WAAW;0BAEV;eAPI;;;;;;AAYf;AAWO,SAAS,QAAQ,EACtB,QAAQ,EACR,QAAQ,CAAC,EACT,WAAW,GAAG,EACd,QAAQ,GAAG,EACX,YAAY,EAAE,EACd,YAAY,GAAG,EACF;IACb,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC,CAAC,MAAM;YACN,IAAI,MAAM,cAAc,EAAE;gBACxB,WAAW;oBACT,aAAa;gBACf,GAAG;YACL;QACF,GACA;YACE;YACA,YAAY;QACd;QAGF,MAAM,iBAAiB,WAAW,OAAO;QACzC,IAAI,gBAAgB;YAClB,SAAS,OAAO,CAAC;QACnB;QAEA,OAAO;YACL,IAAI,gBAAgB;gBAClB,SAAS,SAAS,CAAC;YACrB;QACF;IACF,GAAG;QAAC;QAAO;KAAU;IAErB,qBACE,8OAAC;QACC,KAAK;QACL,WAAW;QACX,OAAO;YACL,SAAS,YAAY,IAAI;YACzB,WAAW,YAAY,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YACrD,YAAY,CAAC,QAAQ,EAAE,SAAS,uBAAuB,EAAE,SAAS,WAAW,CAAC;YAC9E,YAAY;QACd;kBAEC;;;;;;AAGP;AAYO,SAAS,QAAQ,EACtB,QAAQ,EACR,QAAQ,CAAC,EACT,WAAW,GAAG,EACd,YAAY,MAAM,EAClB,WAAW,GAAG,EACd,YAAY,EAAE,EACd,YAAY,GAAG,EACF;IACb,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC,CAAC,MAAM;YACN,IAAI,MAAM,cAAc,EAAE;gBACxB,WAAW;oBACT,aAAa;gBACf,GAAG;YACL;QACF,GACA;YACE;YACA,YAAY;QACd;QAGF,MAAM,iBAAiB,WAAW,OAAO;QACzC,IAAI,gBAAgB;YAClB,SAAS,OAAO,CAAC;QACnB;QAEA,OAAO;YACL,IAAI,gBAAgB;gBAClB,SAAS,SAAS,CAAC;YACrB;QACF;IACF,GAAG;QAAC;QAAO;KAAU;IAErB,MAAM,eAAe;QACnB,IAAI,WAAW,OAAO;QACtB,OAAO,cAAc,SAAS,CAAC,YAAY,EAAE,SAAS,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,SAAS,GAAG,CAAC;IAC1F;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW;QACX,OAAO;YACL,SAAS,YAAY,IAAI;YACzB,WAAW;YACX,YAAY,CAAC,QAAQ,EAAE,SAAS,uBAAuB,EAAE,SAAS,WAAW,CAAC;YAC9E,YAAY;QACd;kBAEC;;;;;;AAGP;AAYO,SAAS,QAAQ,EACtB,GAAG,EACH,QAAQ,CAAC,EACT,WAAW,IAAI,EACf,QAAQ,CAAC,EACT,SAAS,EAAE,EACX,SAAS,EAAE,EACX,YAAY,EAAE,EACD;IACb,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC,CAAC,MAAM;YACN,IAAI,MAAM,cAAc,IAAI,CAAC,WAAW;gBACtC,aAAa;gBACb,WAAW;oBACT,MAAM,YAAY,KAAK,GAAG;oBAC1B,MAAM,aAAa;oBACnB,MAAM,WAAW;oBACjB,MAAM,gBAAgB;oBAEtB,MAAM,cAAc;wBAClB,MAAM,UAAU,KAAK,GAAG,KAAK;wBAC7B,MAAM,WAAW,KAAK,GAAG,CAAC,UAAU,eAAe;wBAEnD,uCAAuC;wBACvC,MAAM,eAAe,IAAI,KAAK,GAAG,CAAC,IAAI,UAAU;wBAChD,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa,CAAC,WAAW,UAAU,IAAI;wBAEvE,SAAS;wBAET,IAAI,WAAW,GAAG;4BAChB,sBAAsB;wBACxB;oBACF;oBAEA,sBAAsB;gBACxB,GAAG;YACL;QACF,GACA;YACE,WAAW;QACb;QAGF,MAAM,iBAAiB,WAAW,OAAO;QACzC,IAAI,gBAAgB;YAClB,SAAS,OAAO,CAAC;QACnB;QAEA,OAAO;YACL,IAAI,gBAAgB;gBAClB,SAAS,SAAS,CAAC;YACrB;QACF;IACF,GAAG;QAAC;QAAO;QAAK;QAAU;QAAO;KAAU;IAE3C,qBACE,8OAAC;QAAK,KAAK;QAAY,WAAW;;YAC/B;YAAQ,MAAM,cAAc;YAAI;;;;;;;AAGvC", "debugId": null}}, {"offset": {"line": 662, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/components/animations/page-transition.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect, useState } from \"react\"\nimport { usePathname } from \"next/navigation\"\n\ninterface PageTransitionProps {\n  children: React.ReactNode\n}\n\nexport function PageTransition({ children }: PageTransitionProps) {\n  const pathname = usePathname()\n  const [isLoading, setIsLoading] = useState(false)\n  const [displayChildren, setDisplayChildren] = useState(children)\n\n  useEffect(() => {\n    setIsLoading(true)\n    \n    const timer = setTimeout(() => {\n      setDisplayChildren(children)\n      setIsLoading(false)\n    }, 300)\n\n    return () => clearTimeout(timer)\n  }, [pathname, children])\n\n  return (\n    <div className=\"relative\">\n      {/* Loading overlay */}\n      <div\n        className={`fixed inset-0 z-50 bg-background transition-opacity duration-300 ${\n          isLoading ? \"opacity-100\" : \"opacity-0 pointer-events-none\"\n        }`}\n      >\n        <div className=\"flex items-center justify-center h-full\">\n          <div className=\"flex flex-col items-center space-y-4\">\n            {/* Animated logo/spinner */}\n            <div className=\"relative\">\n              <div className=\"w-16 h-16 border-4 border-primary/20 rounded-full\"></div>\n              <div className=\"absolute inset-0 w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin\"></div>\n            </div>\n            <div className=\"text-lg font-medium text-muted-foreground\">\n              ✂️ Classic Cuts\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Page content */}\n      <div\n        className={`transition-opacity duration-500 ${\n          isLoading ? \"opacity-0\" : \"opacity-100\"\n        }`}\n      >\n        {displayChildren}\n      </div>\n    </div>\n  )\n}\n\ninterface SmoothScrollProps {\n  children: React.ReactNode\n}\n\nexport function SmoothScroll({ children }: SmoothScrollProps) {\n  useEffect(() => {\n    // Add smooth scrolling behavior\n    document.documentElement.style.scrollBehavior = \"smooth\"\n    \n    return () => {\n      document.documentElement.style.scrollBehavior = \"auto\"\n    }\n  }, [])\n\n  return <>{children}</>\n}\n\ninterface ParallaxProps {\n  children: React.ReactNode\n  speed?: number\n  className?: string\n}\n\nexport function Parallax({ children, speed = 0.5, className = \"\" }: ParallaxProps) {\n  const [offset, setOffset] = useState(0)\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setOffset(window.pageYOffset * speed)\n    }\n\n    window.addEventListener(\"scroll\", handleScroll, { passive: true })\n    return () => window.removeEventListener(\"scroll\", handleScroll)\n  }, [speed])\n\n  return (\n    <div\n      className={className}\n      style={{\n        transform: `translateY(${offset}px)`,\n        willChange: \"transform\"\n      }}\n    >\n      {children}\n    </div>\n  )\n}\n\ninterface FloatingElementProps {\n  children: React.ReactNode\n  amplitude?: number\n  duration?: number\n  delay?: number\n  className?: string\n}\n\nexport function FloatingElement({\n  children,\n  amplitude = 10,\n  duration = 3000,\n  delay = 0,\n  className = \"\"\n}: FloatingElementProps) {\n  return (\n    <div\n      className={className}\n      style={{\n        animation: `float ${duration}ms ease-in-out infinite`,\n        animationDelay: `${delay}ms`,\n        animationFillMode: \"both\"\n      }}\n    >\n      {children}\n      <style jsx>{`\n        @keyframes float {\n          0%, 100% {\n            transform: translateY(0px);\n          }\n          50% {\n            transform: translateY(-${amplitude}px);\n          }\n        }\n      `}</style>\n    </div>\n  )\n}\n\ninterface PulseProps {\n  children: React.ReactNode\n  scale?: number\n  duration?: number\n  className?: string\n}\n\nexport function Pulse({ children, scale = 1.05, duration = 2000, className = \"\" }: PulseProps) {\n  return (\n    <div\n      className={className}\n      style={{\n        animation: `pulse ${duration}ms ease-in-out infinite`\n      }}\n    >\n      {children}\n      <style jsx>{`\n        @keyframes pulse {\n          0%, 100% {\n            transform: scale(1);\n          }\n          50% {\n            transform: scale(${scale});\n          }\n        }\n      `}</style>\n    </div>\n  )\n}\n\ninterface TypewriterProps {\n  text: string\n  speed?: number\n  delay?: number\n  className?: string\n  onComplete?: () => void\n}\n\nexport function Typewriter({\n  text,\n  speed = 50,\n  delay = 0,\n  className = \"\",\n  onComplete\n}: TypewriterProps) {\n  const [displayText, setDisplayText] = useState(\"\")\n  const [currentIndex, setCurrentIndex] = useState(0)\n  const [isStarted, setIsStarted] = useState(false)\n\n  useEffect(() => {\n    const startTimer = setTimeout(() => {\n      setIsStarted(true)\n    }, delay)\n\n    return () => clearTimeout(startTimer)\n  }, [delay])\n\n  useEffect(() => {\n    if (!isStarted) return\n\n    if (currentIndex < text.length) {\n      const timer = setTimeout(() => {\n        setDisplayText(prev => prev + text[currentIndex])\n        setCurrentIndex(prev => prev + 1)\n      }, speed)\n\n      return () => clearTimeout(timer)\n    } else if (onComplete) {\n      onComplete()\n    }\n  }, [currentIndex, text, speed, isStarted, onComplete])\n\n  return (\n    <span className={className}>\n      {displayText}\n      <span className=\"animate-pulse\">|</span>\n    </span>\n  )\n}\n\ninterface RevealProps {\n  children: React.ReactNode\n  direction?: \"horizontal\" | \"vertical\"\n  duration?: number\n  delay?: number\n  className?: string\n}\n\nexport function Reveal({\n  children,\n  direction = \"horizontal\",\n  duration = 800,\n  delay = 0,\n  className = \"\"\n}: RevealProps) {\n  const [isVisible, setIsVisible] = useState(false)\n\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setIsVisible(true)\n    }, delay)\n\n    return () => clearTimeout(timer)\n  }, [delay])\n\n  return (\n    <div className={`relative overflow-hidden ${className}`}>\n      <div\n        className={`transition-transform duration-${duration} ease-out ${\n          isVisible ? \"translate-x-0 translate-y-0\" : \n          direction === \"horizontal\" ? \"translate-x-full\" : \"translate-y-full\"\n        }`}\n      >\n        {children}\n      </div>\n      <div\n        className={`absolute inset-0 bg-primary transition-transform duration-${duration} ease-out ${\n          isVisible ? \n          (direction === \"horizontal\" ? \"translate-x-full\" : \"translate-y-full\") :\n          \"translate-x-0 translate-y-0\"\n        }`}\n        style={{ transitionDelay: `${delay}ms` }}\n      />\n    </div>\n  )\n}\n\ninterface MagneticProps {\n  children: React.ReactNode\n  strength?: number\n  className?: string\n}\n\nexport function Magnetic({ children, strength = 0.3, className = \"\" }: MagneticProps) {\n  const [position, setPosition] = useState({ x: 0, y: 0 })\n\n  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {\n    const rect = e.currentTarget.getBoundingClientRect()\n    const centerX = rect.left + rect.width / 2\n    const centerY = rect.top + rect.height / 2\n    \n    const deltaX = (e.clientX - centerX) * strength\n    const deltaY = (e.clientY - centerY) * strength\n    \n    setPosition({ x: deltaX, y: deltaY })\n  }\n\n  const handleMouseLeave = () => {\n    setPosition({ x: 0, y: 0 })\n  }\n\n  return (\n    <div\n      className={className}\n      onMouseMove={handleMouseMove}\n      onMouseLeave={handleMouseLeave}\n      style={{\n        transform: `translate(${position.x}px, ${position.y}px)`,\n        transition: \"transform 0.3s ease-out\"\n      }}\n    >\n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAEA;AACA;AAHA;;;;;AASO,SAAS,eAAe,EAAE,QAAQ,EAAuB;IAC9D,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;QAEb,MAAM,QAAQ,WAAW;YACvB,mBAAmB;YACnB,aAAa;QACf,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAU;KAAS;IAEvB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,WAAW,CAAC,iEAAiE,EAC3E,YAAY,gBAAgB,iCAC5B;0BAEF,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;0CAEjB,8OAAC;gCAAI,WAAU;0CAA4C;;;;;;;;;;;;;;;;;;;;;;0BAQjE,8OAAC;gBACC,WAAW,CAAC,gCAAgC,EAC1C,YAAY,cAAc,eAC1B;0BAED;;;;;;;;;;;;AAIT;AAMO,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gCAAgC;QAChC,SAAS,eAAe,CAAC,KAAK,CAAC,cAAc,GAAG;QAEhD,OAAO;YACL,SAAS,eAAe,CAAC,KAAK,CAAC,cAAc,GAAG;QAClD;IACF,GAAG,EAAE;IAEL,qBAAO;kBAAG;;AACZ;AAQO,SAAS,SAAS,EAAE,QAAQ,EAAE,QAAQ,GAAG,EAAE,YAAY,EAAE,EAAiB;IAC/E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,UAAU,OAAO,WAAW,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU,cAAc;YAAE,SAAS;QAAK;QAChE,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG;QAAC;KAAM;IAEV,qBACE,8OAAC;QACC,WAAW;QACX,OAAO;YACL,WAAW,CAAC,WAAW,EAAE,OAAO,GAAG,CAAC;YACpC,YAAY;QACd;kBAEC;;;;;;AAGP;AAUO,SAAS,gBAAgB,EAC9B,QAAQ,EACR,YAAY,EAAE,EACd,WAAW,IAAI,EACf,QAAQ,CAAC,EACT,YAAY,EAAE,EACO;IACrB,qBACE,8OAAC;QAEC,OAAO;YACL,WAAW,CAAC,MAAM,EAAE,SAAS,uBAAuB,CAAC;YACrD,gBAAgB,GAAG,MAAM,EAAE,CAAC;YAC5B,mBAAmB;QACrB;;;;;oBAS+B;;;oBAdpB;;YAOV;;;;oBAO8B;;sGAAA;;;;;;;;AAMrC;AASO,SAAS,MAAM,EAAE,QAAQ,EAAE,QAAQ,IAAI,EAAE,WAAW,IAAI,EAAE,YAAY,EAAE,EAAc;IAC3F,qBACE,8OAAC;QAEC,OAAO;YACL,WAAW,CAAC,MAAM,EAAE,SAAS,uBAAuB,CAAC;QACvD;;;;;oBASyB;;;oBAZd;;YAKV;;;;oBAOwB;;2FAAA;;;;;;;;AAM/B;AAUO,SAAS,WAAW,EACzB,IAAI,EACJ,QAAQ,EAAE,EACV,QAAQ,CAAC,EACT,YAAY,EAAE,EACd,UAAU,EACM;IAChB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,WAAW;YAC5B,aAAa;QACf,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAM;IAEV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW;QAEhB,IAAI,eAAe,KAAK,MAAM,EAAE;YAC9B,MAAM,QAAQ,WAAW;gBACvB,eAAe,CAAA,OAAQ,OAAO,IAAI,CAAC,aAAa;gBAChD,gBAAgB,CAAA,OAAQ,OAAO;YACjC,GAAG;YAEH,OAAO,IAAM,aAAa;QAC5B,OAAO,IAAI,YAAY;YACrB;QACF;IACF,GAAG;QAAC;QAAc;QAAM;QAAO;QAAW;KAAW;IAErD,qBACE,8OAAC;QAAK,WAAW;;YACd;0BACD,8OAAC;gBAAK,WAAU;0BAAgB;;;;;;;;;;;;AAGtC;AAUO,SAAS,OAAO,EACrB,QAAQ,EACR,YAAY,YAAY,EACxB,WAAW,GAAG,EACd,QAAQ,CAAC,EACT,YAAY,EAAE,EACF;IACZ,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW;YACvB,aAAa;QACf,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAM;IAEV,qBACE,8OAAC;QAAI,WAAW,CAAC,yBAAyB,EAAE,WAAW;;0BACrD,8OAAC;gBACC,WAAW,CAAC,8BAA8B,EAAE,SAAS,UAAU,EAC7D,YAAY,gCACZ,cAAc,eAAe,qBAAqB,oBAClD;0BAED;;;;;;0BAEH,8OAAC;gBACC,WAAW,CAAC,0DAA0D,EAAE,SAAS,UAAU,EACzF,YACC,cAAc,eAAe,qBAAqB,qBACnD,+BACA;gBACF,OAAO;oBAAE,iBAAiB,GAAG,MAAM,EAAE,CAAC;gBAAC;;;;;;;;;;;;AAI/C;AAQO,SAAS,SAAS,EAAE,QAAQ,EAAE,WAAW,GAAG,EAAE,YAAY,EAAE,EAAiB;IAClF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAEtD,MAAM,kBAAkB,CAAC;QACvB,MAAM,OAAO,EAAE,aAAa,CAAC,qBAAqB;QAClD,MAAM,UAAU,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG;QACzC,MAAM,UAAU,KAAK,GAAG,GAAG,KAAK,MAAM,GAAG;QAEzC,MAAM,SAAS,CAAC,EAAE,OAAO,GAAG,OAAO,IAAI;QACvC,MAAM,SAAS,CAAC,EAAE,OAAO,GAAG,OAAO,IAAI;QAEvC,YAAY;YAAE,GAAG;YAAQ,GAAG;QAAO;IACrC;IAEA,MAAM,mBAAmB;QACvB,YAAY;YAAE,GAAG;YAAG,GAAG;QAAE;IAC3B;IAEA,qBACE,8OAAC;QACC,WAAW;QACX,aAAa;QACb,cAAc;QACd,OAAO;YACL,WAAW,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,GAAG,CAAC;YACxD,YAAY;QACd;kBAEC;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 996, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/components/sections/hero-section.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from \"next/link\"\nimport Image from \"next/image\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { FadeIn, StaggeredFadeIn, ScaleIn } from \"@/components/animations/fade-in\"\nimport { FloatingElement, Magnetic } from \"@/components/animations/page-transition\"\n\nexport function HeroSection() {\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background Image */}\n      <div className=\"absolute inset-0 z-0\">\n        <div className=\"absolute inset-0 bg-gradient-to-r from-black/70 to-black/50 z-10\" />\n        <Image\n          src=\"/hero-barbershop.jpg\"\n          alt=\"Classic Cuts Barbershop Interior\"\n          fill\n          className=\"object-cover\"\n          priority\n          placeholder=\"blur\"\n          blurDataURL=\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==\"\n        />\n      </div>\n\n      {/* Content */}\n      <div className=\"relative z-20 container mx-auto px-4 text-center text-white\">\n        <div className=\"max-w-4xl mx-auto\">\n          {/* Badge */}\n          <FadeIn delay={200}>\n            <FloatingElement amplitude={8} duration={4000}>\n              <div className=\"inline-flex items-center space-x-2 bg-accent/20 backdrop-blur-sm border border-accent/30 rounded-full px-4 py-2 mb-6 hover:bg-accent/30 transition-all duration-300\">\n                <span className=\"text-accent\">✂️</span>\n                <span className=\"text-sm font-medium text-accent\">专业理发服务</span>\n              </div>\n            </FloatingElement>\n          </FadeIn>\n\n          {/* Main Heading */}\n          <FadeIn delay={400} direction=\"up\" distance={50}>\n            <h1 className=\"text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight\">\n              <span className=\"block\">经典剪裁</span>\n              <span className=\"block text-accent\">现代风格</span>\n            </h1>\n          </FadeIn>\n\n          {/* Subtitle */}\n          <FadeIn delay={600} direction=\"up\" distance={30}>\n            <p className=\"text-xl md:text-2xl mb-8 text-gray-200 max-w-2xl mx-auto\">\n              传承经典理发工艺，融合现代时尚元素，为您打造完美造型\n            </p>\n          </FadeIn>\n\n          {/* Stats */}\n          <StaggeredFadeIn delay={800} staggerDelay={150}>\n            {[\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"text-accent\">⭐</span>\n                <span className=\"text-lg font-semibold\">4.9/5 评分</span>\n              </div>,\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"text-accent\">🕐</span>\n                <span className=\"text-lg font-semibold\">15年经验</span>\n              </div>,\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"text-accent\">📍</span>\n                <span className=\"text-lg font-semibold\">市中心位置</span>\n              </div>\n            ]}\n          </StaggeredFadeIn>\n          <div className=\"flex flex-wrap justify-center items-center gap-8 mb-10\">\n          </div>\n\n          {/* CTA Buttons */}\n          <FadeIn delay={1200} direction=\"up\" distance={20}>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n              <Magnetic strength={0.2}>\n                <Button asChild size=\"lg\" variant=\"gradient\" className=\"px-8 py-3 text-lg\" icon=\"✂️\" rightIcon=\"→\">\n                  <Link href=\"/booking\">立即预约</Link>\n                </Button>\n              </Magnetic>\n              <Magnetic strength={0.15}>\n                <Button asChild variant=\"outline\" size=\"lg\" className=\"border-white text-white hover:bg-white hover:text-black px-8 py-3 text-lg\" icon=\"👁️\">\n                  <Link href=\"/services\">查看服务</Link>\n                </Button>\n              </Magnetic>\n            </div>\n          </FadeIn>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20\">\n        <div className=\"animate-bounce\">\n          <div className=\"w-6 h-10 border-2 border-white rounded-full flex justify-center\">\n            <div className=\"w-1 h-3 bg-white rounded-full mt-2 animate-pulse\"></div>\n          </div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,IAAI;wBACJ,WAAU;wBACV,QAAQ;wBACR,aAAY;wBACZ,aAAY;;;;;;;;;;;;0BAKhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,8IAAA,CAAA,SAAM;4BAAC,OAAO;sCACb,cAAA,8OAAC,sJAAA,CAAA,kBAAe;gCAAC,WAAW;gCAAG,UAAU;0CACvC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAc;;;;;;sDAC9B,8OAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;;;;;;;;;;;sCAMxD,8OAAC,8IAAA,CAAA,SAAM;4BAAC,OAAO;4BAAK,WAAU;4BAAK,UAAU;sCAC3C,cAAA,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAAQ;;;;;;kDACxB,8OAAC;wCAAK,WAAU;kDAAoB;;;;;;;;;;;;;;;;;sCAKxC,8OAAC,8IAAA,CAAA,SAAM;4BAAC,OAAO;4BAAK,WAAU;4BAAK,UAAU;sCAC3C,cAAA,8OAAC;gCAAE,WAAU;0CAA2D;;;;;;;;;;;sCAM1E,8OAAC,8IAAA,CAAA,kBAAe;4BAAC,OAAO;4BAAK,cAAc;sCACxC;8CACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAc;;;;;;sDAC9B,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;8CAE1C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAc;;;;;;sDAC9B,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;8CAE1C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAc;;;;;;sDAC9B,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;6BAE3C;;;;;;sCAEH,8OAAC;4BAAI,WAAU;;;;;;sCAIf,8OAAC,8IAAA,CAAA,SAAM;4BAAC,OAAO;4BAAM,WAAU;4BAAK,UAAU;sCAC5C,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sJAAA,CAAA,WAAQ;wCAAC,UAAU;kDAClB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAC,MAAK;4CAAK,SAAQ;4CAAW,WAAU;4CAAoB,MAAK;4CAAK,WAAU;sDAC7F,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAW;;;;;;;;;;;;;;;;kDAG1B,8OAAC,sJAAA,CAAA,WAAQ;wCAAC,UAAU;kDAClB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;4CAA4E,MAAK;sDACrI,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASnC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B", "debugId": null}}, {"offset": {"line": 1356, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/components/ui/card.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  hover?: boolean\n  interactive?: boolean\n  gradient?: boolean\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, hover = false, interactive = false, gradient = false, ...props }, ref) => {\n    const baseClasses = \"rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-300\"\n    const hoverClasses = hover ? \"hover:shadow-lg hover:scale-105 hover:-translate-y-1\" : \"\"\n    const interactiveClasses = interactive ? \"cursor-pointer hover:shadow-xl hover:scale-105 hover:-translate-y-2 active:scale-95 group\" : \"\"\n    const gradientClasses = gradient ? \"bg-gradient-to-br from-card to-card/80 border-primary/20\" : \"\"\n\n    return (\n      <div\n        ref={ref}\n        className={cn(baseClasses, hoverClasses, interactiveClasses, gradientClasses, className)}\n        {...props}\n      />\n    )\n  }\n)\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AAEA;AAJA;;;;AAYA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,QAAQ,KAAK,EAAE,cAAc,KAAK,EAAE,WAAW,KAAK,EAAE,GAAG,OAAO,EAAE;IAC9E,MAAM,cAAc;IACpB,MAAM,eAAe,QAAQ,yDAAyD;IACtF,MAAM,qBAAqB,cAAc,8FAA8F;IACvI,MAAM,kBAAkB,WAAW,6DAA6D;IAEhG,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa,cAAc,oBAAoB,iBAAiB;QAC7E,GAAG,KAAK;;;;;;AAGf;AAEF,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1444, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/components/sections/services-preview.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from \"next/link\"\nimport { Button } from \"@/components/ui/button\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { FadeIn, StaggeredFadeIn, ScaleIn } from \"@/components/animations/fade-in\"\n\nconst services = [\n  {\n    icon: \"✂️\",\n    title: \"经典理发\",\n    description: \"专业理发师为您提供精准的剪发服务，打造适合您的完美发型\",\n    price: \"¥80-120\",\n    duration: \"45分钟\",\n  },\n  {\n    icon: \"⚡\",\n    title: \"胡须修剪\",\n    description: \"精细的胡须造型和修剪，让您的面部轮廓更加立体有型\",\n    price: \"¥60-80\",\n    duration: \"30分钟\",\n  },\n  {\n    icon: \"✨\",\n    title: \"头发造型\",\n    description: \"使用专业造型产品，为您打造时尚个性的发型造型\",\n    price: \"¥50-70\",\n    duration: \"20分钟\",\n  },\n  {\n    icon: \"🧴\",\n    title: \"洗发护理\",\n    description: \"深层清洁和滋养护理，让您的头发健康有光泽\",\n    price: \"¥40-60\",\n    duration: \"25分钟\",\n  },\n]\n\nexport function ServicesPreview() {\n  return (\n    <section className=\"py-20 bg-muted/50\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <FadeIn direction=\"up\" distance={30}>\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">\n              专业服务项目\n            </h2>\n            <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n              我们提供全方位的男士理发和护理服务，每一项服务都体现我们对品质的追求\n            </p>\n          </div>\n        </FadeIn>\n\n        {/* Services Grid */}\n        <StaggeredFadeIn delay={200} staggerDelay={150} direction=\"up\" distance={30}>\n          {services.map((service, index) => (\n            <ScaleIn delay={index * 100}>\n              <Card key={index} hover interactive className=\"group\">\n                <CardHeader className=\"text-center\">\n                  <div className=\"mx-auto mb-4 p-3 bg-primary/10 rounded-full w-fit group-hover:bg-primary/20 transition-colors\">\n                    <span className=\"text-3xl\">{service.icon}</span>\n                  </div>\n                  <CardTitle className=\"text-xl\">{service.title}</CardTitle>\n                  <CardDescription className=\"text-sm\">\n                    {service.description}\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"text-center\">\n                  <div className=\"space-y-2\">\n                    <div className=\"text-2xl font-bold text-primary\">\n                      {service.price}\n                    </div>\n                    <div className=\"text-sm text-muted-foreground\">\n                      {service.duration}\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </ScaleIn>\n          ))}\n        </StaggeredFadeIn>\n\n        {/* CTA */}\n        <FadeIn delay={800} direction=\"up\" distance={20}>\n          <div className=\"text-center\">\n            <Button asChild size=\"lg\" variant=\"shine\" className=\"px-8\" icon=\"👁️\" rightIcon=\"→\">\n              <Link href=\"/services\">查看所有服务</Link>\n            </Button>\n          </div>\n        </FadeIn>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,MAAM,WAAW;IACf;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;IACZ;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;IACZ;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;IACZ;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;IACZ;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,8IAAA,CAAA,SAAM;oBAAC,WAAU;oBAAK,UAAU;8BAC/B,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,8OAAC;gCAAE,WAAU;0CAAkD;;;;;;;;;;;;;;;;;8BAOnE,8OAAC,8IAAA,CAAA,kBAAe;oBAAC,OAAO;oBAAK,cAAc;oBAAK,WAAU;oBAAK,UAAU;8BACtE,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,8IAAA,CAAA,UAAO;4BAAC,OAAO,QAAQ;sCACtB,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAa,KAAK;gCAAC,WAAW;gCAAC,WAAU;;kDAC5C,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAY,QAAQ,IAAI;;;;;;;;;;;0DAE1C,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAW,QAAQ,KAAK;;;;;;0DAC7C,8OAAC,gIAAA,CAAA,kBAAe;gDAAC,WAAU;0DACxB,QAAQ,WAAW;;;;;;;;;;;;kDAGxB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,QAAQ,KAAK;;;;;;8DAEhB,8OAAC;oDAAI,WAAU;8DACZ,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;+BAhBd;;;;;;;;;;;;;;;8BA0BjB,8OAAC,8IAAA,CAAA,SAAM;oBAAC,OAAO;oBAAK,WAAU;oBAAK,UAAU;8BAC3C,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,OAAO;4BAAC,MAAK;4BAAK,SAAQ;4BAAQ,WAAU;4BAAO,MAAK;4BAAM,WAAU;sCAC9E,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrC", "debugId": null}}, {"offset": {"line": 1681, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/components/sections/testimonials-section.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { Card, CardContent } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport { FadeIn, ScaleIn } from \"@/components/animations/fade-in\"\n\nconst testimonials = [\n  {\n    name: \"张先生\",\n    rating: 5,\n    comment: \"服务非常专业，理发师技术精湛，店内环境也很舒适。已经是这里的老客户了，强烈推荐！\",\n    service: \"经典理发\",\n  },\n  {\n    name: \"李先生\",\n    rating: 5,\n    comment: \"胡须修剪得很精细，理发师很有耐心，会根据我的脸型给出专业建议。价格也很合理。\",\n    service: \"胡须修剪\",\n  },\n  {\n    name: \"王先生\",\n    rating: 5,\n    comment: \"第一次来就被专业的服务打动了，理发师很细心，剪出来的发型很满意。环境也很干净。\",\n    service: \"时尚造型\",\n  },\n  {\n    name: \"陈先生\",\n    rating: 5,\n    comment: \"朋友推荐来的，果然没有失望。理发师技术很好，而且会给出很好的建议。会继续光顾的。\",\n    service: \"商务造型\",\n  },\n  {\n    name: \"刘先生\",\n    rating: 5,\n    comment: \"预约很方便，服务很周到。理发师会仔细询问我的需求，剪出来的效果超出预期。\",\n    service: \"洗发护理\",\n  },\n]\n\nexport function TestimonialsSection() {\n  const [currentIndex, setCurrentIndex] = useState(0)\n\n  const nextTestimonial = () => {\n    setCurrentIndex((prevIndex) => \n      prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1\n    )\n  }\n\n  const prevTestimonial = () => {\n    setCurrentIndex((prevIndex) => \n      prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1\n    )\n  }\n\n  // Auto-rotate testimonials\n  useEffect(() => {\n    const interval = setInterval(nextTestimonial, 5000)\n    return () => clearInterval(interval)\n  }, [])\n\n  return (\n    <section className=\"py-20 bg-muted/50\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <FadeIn>\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">\n              客户评价\n            </h2>\n            <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n              听听我们客户的真实反馈，他们的满意是我们最大的动力\n            </p>\n          </div>\n        </FadeIn>\n\n        {/* Testimonials Carousel */}\n        <ScaleIn delay={300}>\n          <div className=\"max-w-4xl mx-auto\">\n            <Card className=\"bg-background/50 backdrop-blur-sm border-2\">\n              <CardContent className=\"p-8 md:p-12\">\n                <div className=\"text-center\">\n                  {/* Stars */}\n                  <div className=\"flex justify-center mb-6\">\n                    {[...Array(testimonials[currentIndex].rating)].map((_, i) => (\n                      <span key={i} className=\"text-2xl text-yellow-400\">⭐</span>\n                    ))}\n                  </div>\n                  \n                  {/* Comment */}\n                  <blockquote className=\"text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed\">\n                    \"{testimonials[currentIndex].comment}\"\n                  </blockquote>\n                  \n                  {/* Customer Info */}\n                  <div className=\"space-y-2\">\n                    <div className=\"text-lg font-semibold\">\n                      {testimonials[currentIndex].name}\n                    </div>\n                    <div className=\"text-sm text-primary\">\n                      {testimonials[currentIndex].service}\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Navigation */}\n            <div className=\"flex justify-center items-center mt-8 space-x-4\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={prevTestimonial}\n                className=\"rounded-full w-10 h-10 p-0\"\n              >\n                ←\n              </Button>\n              \n              {/* Dots */}\n              <div className=\"flex space-x-2\">\n                {testimonials.map((_, index) => (\n                  <button\n                    key={index}\n                    onClick={() => setCurrentIndex(index)}\n                    className={`w-3 h-3 rounded-full transition-all duration-300 ${\n                      index === currentIndex \n                        ? 'bg-primary scale-125' \n                        : 'bg-muted-foreground/30 hover:bg-muted-foreground/50'\n                    }`}\n                  />\n                ))}\n              </div>\n              \n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={nextTestimonial}\n                className=\"rounded-full w-10 h-10 p-0\"\n              >\n                →\n              </Button>\n            </div>\n          </div>\n        </ScaleIn>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,MAAM,eAAe;IACnB;QACE,MAAM;QACN,QAAQ;QACR,SAAS;QACT,SAAS;IACX;IACA;QACE,MAAM;QACN,QAAQ;QACR,SAAS;QACT,SAAS;IACX;IACA;QACE,MAAM;QACN,QAAQ;QACR,SAAS;QACT,SAAS;IACX;IACA;QACE,MAAM;QACN,QAAQ;QACR,SAAS;QACT,SAAS;IACX;IACA;QACE,MAAM;QACN,QAAQ;QACR,SAAS;QACT,SAAS;IACX;CACD;AAEM,SAAS;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,kBAAkB;QACtB,gBAAgB,CAAC,YACf,cAAc,aAAa,MAAM,GAAG,IAAI,IAAI,YAAY;IAE5D;IAEA,MAAM,kBAAkB;QACtB,gBAAgB,CAAC,YACf,cAAc,IAAI,aAAa,MAAM,GAAG,IAAI,YAAY;IAE5D;IAEA,2BAA2B;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY,iBAAiB;QAC9C,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,8IAAA,CAAA,SAAM;8BACL,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,8OAAC;gCAAE,WAAU;0CAAkD;;;;;;;;;;;;;;;;;8BAOnE,8OAAC,8IAAA,CAAA,UAAO;oBAAC,OAAO;8BACd,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM,YAAY,CAAC,aAAa,CAAC,MAAM;iDAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrD,8OAAC;wDAAa,WAAU;kEAA2B;uDAAxC;;;;;;;;;;0DAKf,8OAAC;gDAAW,WAAU;;oDAAiE;oDACnF,YAAY,CAAC,aAAa,CAAC,OAAO;oDAAC;;;;;;;0DAIvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,YAAY,CAAC,aAAa,CAAC,IAAI;;;;;;kEAElC,8OAAC;wDAAI,WAAU;kEACZ,YAAY,CAAC,aAAa,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQ7C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;kDAKD,8OAAC;wCAAI,WAAU;kDACZ,aAAa,GAAG,CAAC,CAAC,GAAG,sBACpB,8OAAC;gDAEC,SAAS,IAAM,gBAAgB;gDAC/B,WAAW,CAAC,iDAAiD,EAC3D,UAAU,eACN,yBACA,uDACJ;+CANG;;;;;;;;;;kDAWX,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 1933, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/components/sections/about-preview.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from \"next/link\"\nimport Image from \"next/image\"\nimport { Button } from \"@/components/ui/button\"\nimport { FadeIn, StaggeredFadeIn, ScaleIn, CountUp } from \"@/components/animations/fade-in\"\nimport { FloatingElement } from \"@/components/animations/page-transition\"\n\nconst stats = [\n  {\n    icon: \"🏆\",\n    number: \"15+\",\n    label: \"年专业经验\",\n  },\n  {\n    icon: \"👥\",\n    number: \"5000+\",\n    label: \"满意客户\",\n  },\n  {\n    icon: \"🕐\",\n    number: \"24/7\",\n    label: \"在线预约\",\n  },\n  {\n    icon: \"✂️\",\n    number: \"100%\",\n    label: \"专业保证\",\n  },\n]\n\nexport function AboutPreview() {\n  return (\n    <section className=\"py-20\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          {/* Content */}\n          <FadeIn>\n            <div className=\"space-y-6\">\n              <div className=\"space-y-4\">\n                <h2 className=\"text-3xl md:text-4xl font-bold\">\n                  传承经典，创新未来\n                </h2>\n                <p className=\"text-lg text-muted-foreground\">\n                  Classic Cuts 成立于2009年，我们致力于为每一位客户提供最专业的理发服务。\n                  我们的理发师团队拥有丰富的经验和精湛的技艺，结合传统理发工艺与现代时尚元素。\n                </p>\n                <p className=\"text-muted-foreground\">\n                  在这里，您不仅能享受到专业的理发服务，更能体验到传统理发店的温馨氛围。\n                  我们相信，每一次剪发都是一次艺术创作，每一位客户都值得我们用心对待。\n                </p>\n              </div>\n\n              {/* Stats */}\n              <StaggeredFadeIn className=\"grid grid-cols-2 gap-6 py-6\">\n                {stats.map((stat, index) => {\n                  return (\n                    <ScaleIn key={index} delay={index * 100}>\n                      <div className=\"text-center\">\n                        <div className=\"mx-auto mb-2 p-2 bg-primary/10 rounded-full w-fit\">\n                          <span className=\"text-2xl\">{stat.icon}</span>\n                        </div>\n                        <div className=\"text-2xl font-bold text-primary\">\n                          {stat.number}\n                        </div>\n                        <div className=\"text-sm text-muted-foreground\">\n                          {stat.label}\n                        </div>\n                      </div>\n                    </ScaleIn>\n                  )\n                })}\n              </StaggeredFadeIn>\n\n              <FadeIn delay={400}>\n                <Button asChild size=\"lg\">\n                  <Link href=\"/about\">了解更多</Link>\n                </Button>\n              </FadeIn>\n            </div>\n          </FadeIn>\n\n          {/* Image */}\n          <FadeIn delay={200}>\n            <div className=\"relative\">\n              <div className=\"relative h-96 lg:h-[500px] rounded-lg overflow-hidden\">\n                <Image\n                  src=\"/about-barbershop.jpg\"\n                  alt=\"Classic Cuts Barbershop Team\"\n                  fill\n                  className=\"object-cover\"\n                  placeholder=\"blur\"\n                  blurDataURL=\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==\"\n                />\n              </div>\n\n              {/* Decorative Elements */}\n              <FloatingElement delay={1000}>\n                <div className=\"absolute -top-4 -left-4 w-24 h-24 bg-accent/20 rounded-full blur-xl\"></div>\n              </FloatingElement>\n              <FloatingElement delay={1500}>\n                <div className=\"absolute -bottom-4 -right-4 w-32 h-32 bg-secondary/20 rounded-full blur-xl\"></div>\n              </FloatingElement>\n            </div>\n          </FadeIn>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,QAAQ;IACZ;QACE,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IACA;QACE,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IACA;QACE,MAAM;QACN,QAAQ;QACR,OAAO;IACT;IACA;QACE,MAAM;QACN,QAAQ;QACR,OAAO;IACT;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,8IAAA,CAAA,SAAM;kCACL,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAG/C,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;sDAI7C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAOvC,8OAAC,8IAAA,CAAA,kBAAe;oCAAC,WAAU;8CACxB,MAAM,GAAG,CAAC,CAAC,MAAM;wCAChB,qBACE,8OAAC,8IAAA,CAAA,UAAO;4CAAa,OAAO,QAAQ;sDAClC,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAAY,KAAK,IAAI;;;;;;;;;;;kEAEvC,8OAAC;wDAAI,WAAU;kEACZ,KAAK,MAAM;;;;;;kEAEd,8OAAC;wDAAI,WAAU;kEACZ,KAAK,KAAK;;;;;;;;;;;;2CATH;;;;;oCAclB;;;;;;8CAGF,8OAAC,8IAAA,CAAA,SAAM;oCAAC,OAAO;8CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAC,MAAK;kDACnB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO5B,8OAAC,8IAAA,CAAA,SAAM;wBAAC,OAAO;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,IAAI;wCACJ,WAAU;wCACV,aAAY;wCACZ,aAAY;;;;;;;;;;;8CAKhB,8OAAC,sJAAA,CAAA,kBAAe;oCAAC,OAAO;8CACtB,cAAA,8OAAC;wCAAI,WAAU;;;;;;;;;;;8CAEjB,8OAAC,sJAAA,CAAA,kBAAe;oCAAC,OAAO;8CACtB,cAAA,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/B", "debugId": null}}, {"offset": {"line": 2192, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/components/sections/cta-section.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from \"next/link\"\nimport { Button } from \"@/components/ui/button\"\nimport { FadeIn, StaggeredFadeIn } from \"@/components/animations/fade-in\"\nimport { FloatingElement, Pulse } from \"@/components/animations/page-transition\"\n\nexport function CTASection() {\n  return (\n    <section className=\"py-20 bg-primary text-primary-foreground\">\n      <div className=\"container mx-auto px-4 text-center\">\n        <div className=\"max-w-3xl mx-auto\">\n          {/* Main Heading */}\n          <FadeIn>\n            <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">\n              准备好体验专业理发服务了吗？\n            </h2>\n          </FadeIn>\n\n          {/* Subtitle */}\n          <FadeIn delay={200}>\n            <p className=\"text-xl mb-8 text-primary-foreground/90\">\n              立即预约，让我们的专业理发师为您打造完美造型\n            </p>\n          </FadeIn>\n\n          {/* Contact Options */}\n          <StaggeredFadeIn className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-10\" delay={400}>\n            <div className=\"flex flex-col items-center space-y-2\">\n              <Pulse>\n                <div className=\"p-3 bg-accent/20 rounded-full\">\n                  <span className=\"text-2xl text-accent\">📞</span>\n                </div>\n              </Pulse>\n              <div className=\"text-sm font-medium\">电话预约</div>\n              <div className=\"text-sm text-primary-foreground/80\">\n                (555) 123-4567\n              </div>\n            </div>\n\n            <div className=\"flex flex-col items-center space-y-2\">\n              <Pulse delay={200}>\n                <div className=\"p-3 bg-accent/20 rounded-full\">\n                  <span className=\"text-2xl text-accent\">📅</span>\n                </div>\n              </Pulse>\n              <div className=\"text-sm font-medium\">在线预约</div>\n              <div className=\"text-sm text-primary-foreground/80\">\n                24/7 随时预约\n              </div>\n            </div>\n\n            <div className=\"flex flex-col items-center space-y-2\">\n              <Pulse delay={400}>\n                <div className=\"p-3 bg-accent/20 rounded-full\">\n                  <span className=\"text-2xl text-accent\">📍</span>\n                </div>\n              </Pulse>\n              <div className=\"text-sm font-medium\">到店服务</div>\n              <div className=\"text-sm text-primary-foreground/80\">\n                市中心便利位置\n              </div>\n            </div>\n          </StaggeredFadeIn>\n\n          {/* CTA Buttons */}\n          <FadeIn delay={800}>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n            <Button \n              asChild \n              size=\"lg\" \n              className=\"bg-accent hover:bg-accent/90 text-black font-semibold px-8 py-3 text-lg\"\n            >\n              <Link href=\"/booking\">立即在线预约</Link>\n            </Button>\n            \n            <Button \n              asChild \n              variant=\"outline\" \n              size=\"lg\" \n              className=\"border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary px-8 py-3 text-lg\"\n            >\n              <Link href=\"tel:+15551234567\">电话预约</Link>\n            </Button>\n            </div>\n          </FadeIn>\n\n          {/* Additional Info */}\n          <FadeIn delay={1000}>\n            <div className=\"mt-8 text-sm text-primary-foreground/80\">\n              <p>营业时间：周一至周五 9:00-20:00 | 周六 8:00-18:00 | 周日 10:00-16:00</p>\n            </div>\n          </FadeIn>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,8IAAA,CAAA,SAAM;kCACL,cAAA,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;;;;;;kCAMtD,8OAAC,8IAAA,CAAA,SAAM;wBAAC,OAAO;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;kCAMzD,8OAAC,8IAAA,CAAA,kBAAe;wBAAC,WAAU;wBAA8C,OAAO;;0CAC9E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sJAAA,CAAA,QAAK;kDACJ,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAuB;;;;;;;;;;;;;;;;kDAG3C,8OAAC;wCAAI,WAAU;kDAAsB;;;;;;kDACrC,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;0CAKtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sJAAA,CAAA,QAAK;wCAAC,OAAO;kDACZ,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAuB;;;;;;;;;;;;;;;;kDAG3C,8OAAC;wCAAI,WAAU;kDAAsB;;;;;;kDACrC,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;0CAKtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sJAAA,CAAA,QAAK;wCAAC,OAAO;kDACZ,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAuB;;;;;;;;;;;;;;;;kDAG3C,8OAAC;wCAAI,WAAU;kDAAsB;;;;;;kDACrC,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;;;;;;;kCAOxD,8OAAC,8IAAA,CAAA,SAAM;wBAAC,OAAO;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACf,8OAAC,kIAAA,CAAA,SAAM;oCACL,OAAO;oCACP,MAAK;oCACL,WAAU;8CAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDAAW;;;;;;;;;;;8CAGxB,8OAAC,kIAAA,CAAA,SAAM;oCACL,OAAO;oCACP,SAAQ;oCACR,MAAK;oCACL,WAAU;8CAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDAAmB;;;;;;;;;;;;;;;;;;;;;;kCAMlC,8OAAC,8IAAA,CAAA,SAAM;wBAAC,OAAO;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB", "debugId": null}}]}