"use client"

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { AdminLayout, PageContainer, CardContainer } from '@/components/admin/layout/admin-layout'
import { CustomerForm } from '@/components/admin/customers/customer-form'
import { Edit } from 'lucide-react'
import { customerStore } from '@/lib/admin/storage'
import { Customer } from '@/lib/types/admin'

export default function EditCustomerPage() {
  const params = useParams()
  const [customer, setCustomer] = useState<Customer | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (params.id) {
      loadCustomer(params.id as string)
    }
  }, [params.id])

  const loadCustomer = (id: string) => {
    setLoading(true)
    try {
      const data = customerStore.getById(id)
      setCustomer(data || null)
    } catch (error) {
      console.error('Failed to load customer:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <AdminLayout>
        <PageContainer>
          <div className="flex items-center justify-center py-12">
            <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin" />
            <span className="ml-2">加载中...</span>
          </div>
        </PageContainer>
      </AdminLayout>
    )
  }

  if (!customer) {
    return (
      <AdminLayout>
        <PageContainer>
          <div className="text-center py-12">
            <p className="text-muted-foreground">客户不存在</p>
          </div>
        </PageContainer>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <PageContainer
        title="编辑客户"
        description={`编辑客户信息 - ${customer.name}`}
      >
        <CardContainer
          title="编辑客户信息"
          description="修改客户的基本信息和服务偏好"
        >
          <CustomerForm customer={customer} />
        </CardContainer>
      </PageContainer>
    </AdminLayout>
  )
}
