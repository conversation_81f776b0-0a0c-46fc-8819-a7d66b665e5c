import Link from "next/link"
import { Button } from "@/components/ui/button"

export function CTASection() {
  return (
    <section className="py-20 bg-primary text-primary-foreground">
      <div className="container mx-auto px-4 text-center">
        <div className="max-w-3xl mx-auto">
          {/* Main Heading */}
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            准备好体验专业理发服务了吗？
          </h2>
          
          {/* Subtitle */}
          <p className="text-xl mb-8 text-primary-foreground/90">
            立即预约，让我们的专业理发师为您打造完美造型
          </p>

          {/* Contact Options */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10">
            <div className="flex flex-col items-center space-y-2">
              <div className="p-3 bg-accent/20 rounded-full">
                <span className="text-2xl text-accent">📞</span>
              </div>
              <div className="text-sm font-medium">电话预约</div>
              <div className="text-sm text-primary-foreground/80">
                (555) 123-4567
              </div>
            </div>

            <div className="flex flex-col items-center space-y-2">
              <div className="p-3 bg-accent/20 rounded-full">
                <span className="text-2xl text-accent">📅</span>
              </div>
              <div className="text-sm font-medium">在线预约</div>
              <div className="text-sm text-primary-foreground/80">
                24/7 随时预约
              </div>
            </div>

            <div className="flex flex-col items-center space-y-2">
              <div className="p-3 bg-accent/20 rounded-full">
                <span className="text-2xl text-accent">📍</span>
              </div>
              <div className="text-sm font-medium">到店服务</div>
              <div className="text-sm text-primary-foreground/80">
                市中心便利位置
              </div>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button 
              asChild 
              size="lg" 
              className="bg-accent hover:bg-accent/90 text-black font-semibold px-8 py-3 text-lg"
            >
              <Link href="/booking">立即在线预约</Link>
            </Button>
            
            <Button 
              asChild 
              variant="outline" 
              size="lg" 
              className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary px-8 py-3 text-lg"
            >
              <Link href="tel:+15551234567">电话预约</Link>
            </Button>
          </div>

          {/* Additional Info */}
          <div className="mt-8 text-sm text-primary-foreground/80">
            <p>营业时间：周一至周五 9:00-20:00 | 周六 8:00-18:00 | 周日 10:00-16:00</p>
          </div>
        </div>
      </div>
    </section>
  )
}
