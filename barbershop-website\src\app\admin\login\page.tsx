"use client"

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card } from '@/components/ui/card'
import { authService } from '@/lib/admin/auth'
import { initializeData } from '@/lib/admin/storage'

export default function AdminLoginPage() {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()

  useEffect(() => {
    // 初始化数据
    initializeData()
    
    // 检查是否已登录
    const authState = authService.getAuthState()
    if (authState.isAuthenticated) {
      router.push('/admin')
    }
  }, [router])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    try {
      const result = await authService.login(formData.username, formData.password)
      
      if (result.success) {
        router.push('/admin')
      } else {
        setError(result.message || '登录失败')
      }
    } catch (error) {
      setError('登录失败，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-muted/20 to-accent/10 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Logo和标题 */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <span className="text-4xl">✂️</span>
          </div>
          <h1 className="text-3xl font-bold text-primary mb-2">Tony's Barbershop</h1>
          <p className="text-muted-foreground">管理后台系统</p>
        </div>

        {/* 登录表单 */}
        <Card className="p-6 shadow-lg border-0 bg-card/80 backdrop-blur-sm">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <label htmlFor="username" className="text-sm font-medium text-foreground">
                用户名
              </label>
              <Input
                id="username"
                name="username"
                type="text"
                value={formData.username}
                onChange={handleInputChange}
                placeholder="请输入用户名"
                required
                className="h-11"
                disabled={isLoading}
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="password" className="text-sm font-medium text-foreground">
                密码
              </label>
              <Input
                id="password"
                name="password"
                type="password"
                value={formData.password}
                onChange={handleInputChange}
                placeholder="请输入密码"
                required
                className="h-11"
                disabled={isLoading}
              />
            </div>

            {error && (
              <div className="p-3 rounded-md bg-destructive/10 border border-destructive/20">
                <p className="text-sm text-destructive">{error}</p>
              </div>
            )}

            <Button
              type="submit"
              className="w-full h-11 bg-primary hover:bg-primary/90"
              disabled={isLoading}
              loading={isLoading}
            >
              {isLoading ? '登录中...' : '登录'}
            </Button>
          </form>

          {/* 默认账户提示 */}
          <div className="mt-6 p-4 rounded-md bg-accent/10 border border-accent/20">
            <h3 className="text-sm font-medium text-accent-foreground mb-2">默认管理员账户</h3>
            <div className="text-xs text-muted-foreground space-y-1">
              <p>用户名: <span className="font-mono bg-muted px-1 rounded">admin</span></p>
              <p>密码: <span className="font-mono bg-muted px-1 rounded">barbershop2024</span></p>
            </div>
          </div>
        </Card>

        {/* 版权信息 */}
        <div className="text-center mt-8 text-sm text-muted-foreground">
          <p>&copy; 2024 Tony's Barbershop. All rights reserved.</p>
        </div>
      </div>
    </div>
  )
}
