"use client"

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { AdminLayout, PageContainer, CardContainer } from '@/components/admin/layout/admin-layout'
import { ServiceForm } from '@/components/admin/services/service-form'
import { Edit } from 'lucide-react'
import { serviceStore } from '@/lib/admin/storage'
import { Service } from '@/lib/types/admin'

export default function EditServicePage() {
  const params = useParams()
  const [service, setService] = useState<Service | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (params.id) {
      loadService(params.id as string)
    }
  }, [params.id])

  const loadService = (id: string) => {
    setLoading(true)
    try {
      const data = serviceStore.getById(id)
      setService(data || null)
    } catch (error) {
      console.error('Failed to load service:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <AdminLayout>
        <PageContainer>
          <div className="flex items-center justify-center py-12">
            <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin" />
            <span className="ml-2">加载中...</span>
          </div>
        </PageContainer>
      </AdminLayout>
    )
  }

  if (!service) {
    return (
      <AdminLayout>
        <PageContainer>
          <div className="text-center py-12">
            <p className="text-muted-foreground">服务不存在</p>
          </div>
        </PageContainer>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <PageContainer
        title="编辑服务"
        description={`编辑服务信息 - ${service.name}`}
      >
        <CardContainer
          title="编辑服务信息"
          description="修改服务的基本信息、价格和时长设置"
        >
          <ServiceForm service={service} />
        </CardContainer>
      </PageContainer>
    </AdminLayout>
  )
}
