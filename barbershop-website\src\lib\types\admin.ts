// <PERSON>'s Barbershop - 管理后台类型定义

export interface User {
  id: string
  username: string
  password: string
  role: 'admin' | 'super_admin'
  name: string
  createdAt: string
  lastLogin?: string
}

export interface Customer {
  id: string
  name: string
  phone: string
  email?: string
  gender?: 'male' | 'female' | 'other'
  birthDate?: string
  address?: string
  notes?: string
  preferences: ServicePreference[]
  totalVisits: number
  totalSpent: number
  lastVisit?: string
  createdAt: string
  updatedAt: string
}

export interface ServicePreference {
  serviceId: string
  preferredStaffId?: string
  notes?: string
}

export interface Appointment {
  id: string
  customerId: string
  customerName: string
  customerPhone: string
  staffId: string
  staffName: string
  serviceIds: string[]
  services: AppointmentService[]
  date: string // YYYY-MM-DD
  startTime: string // HH:mm
  endTime: string // HH:mm
  duration: number // 总时长(分钟)
  totalPrice: number // 总价格
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show'
  notes?: string
  createdAt: string
  updatedAt: string
}

export interface AppointmentService {
  serviceId: string
  serviceName: string
  duration: number
  price: number
}

export interface Service {
  id: string
  name: string
  description: string
  category: string
  duration: number // 分钟
  price: number
  isActive: boolean
  popularity: number // 受欢迎程度 (0-100)
  createdAt: string
  updatedAt: string
}

export interface ServiceCategory {
  id: string
  name: string
  description?: string
  order: number
  isActive: boolean
}

export interface Staff {
  id: string
  name: string
  phone: string
  email?: string
  specialties: string[] // 专业技能
  workingHours: string[] // 工作日 ['1', '2', '3', '4', '5', '6', '7']
  startTime: string // 开始工作时间 '09:00'
  endTime: string // 结束工作时间 '18:00'
  isManager: boolean // 是否为管理员
  isActive: boolean
  notes?: string // 备注信息
  rating: number // 评分 (0-5)
  createdAt: string
  updatedAt: string
}

export interface DaySchedule {
  isWorking: boolean
  startTime?: string // HH:mm
  endTime?: string // HH:mm
  breakStart?: string // HH:mm
  breakEnd?: string // HH:mm
}

export interface Analytics {
  revenue: RevenueData
  appointments: AppointmentStats
  customers: CustomerStats
  services: ServiceStats
  staff: StaffStats
}

export interface RevenueData {
  daily: DailyRevenue[]
  weekly: WeeklyRevenue[]
  monthly: MonthlyRevenue[]
  yearly: YearlyRevenue[]
}

export interface DailyRevenue {
  date: string
  revenue: number
  appointments: number
}

export interface WeeklyRevenue {
  week: string // YYYY-WW
  revenue: number
  appointments: number
}

export interface MonthlyRevenue {
  month: string // YYYY-MM
  revenue: number
  appointments: number
}

export interface YearlyRevenue {
  year: string // YYYY
  revenue: number
  appointments: number
}

export interface AppointmentStats {
  total: number
  pending: number
  confirmed: number
  completed: number
  cancelled: number
  noShow: number
  todayTotal: number
  weekTotal: number
  monthTotal: number
}

export interface CustomerStats {
  total: number
  new: number // 本月新客户
  returning: number // 回头客
  averageSpent: number
  topCustomers: TopCustomer[]
}

export interface TopCustomer {
  customerId: string
  customerName: string
  totalSpent: number
  totalVisits: number
}

export interface ServiceStats {
  total: number
  popular: PopularService[]
  revenue: ServiceRevenue[]
}

export interface PopularService {
  serviceId: string
  serviceName: string
  bookings: number
  revenue: number
}

export interface ServiceRevenue {
  serviceId: string
  serviceName: string
  revenue: number
  percentage: number
}

export interface StaffStats {
  total: number
  active: number
  performance: StaffPerformance[]
}

export interface StaffPerformance {
  staffId: string
  staffName: string
  appointments: number
  revenue: number
  rating: number
}

export interface WorkingHours {
  monday: DaySchedule
  tuesday: DaySchedule
  wednesday: DaySchedule
  thursday: DaySchedule
  friday: DaySchedule
  saturday: DaySchedule
  sunday: DaySchedule
}

export interface SystemSettings {
  businessHours: WorkingHours
  appointmentSettings: AppointmentSettings
  notificationSettings: NotificationSettings
  generalSettings: GeneralSettings
}

export interface AppointmentSettings {
  defaultDuration: number
  bufferTime: number // 预约间隔时间
  advanceBookingDays: number // 提前预约天数
  cancellationHours: number // 取消预约时限
  reminderHours: number // 提醒时间
}

export interface NotificationSettings {
  emailEnabled: boolean
  smsEnabled: boolean
  reminderEnabled: boolean
  confirmationEnabled: boolean
}

export interface GeneralSettings {
  businessName: string
  businessPhone: string
  businessEmail: string
  businessAddress: string
  currency: string
  timezone: string
  language: string
}

// API 响应类型
export interface ApiResponse<T> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

// 分页类型
export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// 查询参数类型
export interface QueryParams {
  page?: number
  limit?: number
  search?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  filter?: Record<string, any>
}

// 表单状态类型
export interface FormState {
  isLoading: boolean
  errors: Record<string, string>
  isDirty: boolean
  isValid: boolean
}

// 导航菜单类型
export interface NavItem {
  id: string
  label: string
  href: string
  icon: string
  badge?: number
  children?: NavItem[]
}

// 仪表板统计卡片类型
export interface StatCard {
  title: string
  value: string | number
  change?: number
  changeType?: 'increase' | 'decrease' | 'neutral'
  icon: string
  color: string
}

// 日历事件类型
export interface CalendarEvent {
  id: string
  title: string
  start: Date
  end: Date
  color: string
  data: Appointment
}

// 时间段类型
export interface TimeSlot {
  time: string
  available: boolean
  staffId?: string
  appointmentId?: string
}

// 导出类型
export type AppointmentStatus = Appointment['status']
export type UserRole = User['role']
export type CustomerGender = Customer['gender']
