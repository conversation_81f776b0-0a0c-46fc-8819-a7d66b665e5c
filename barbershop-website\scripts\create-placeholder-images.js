#!/usr/bin/env node

/**
 * <PERSON>'s Barbershop - 创建高质量占位符图片
 * 为网站创建专业的占位符图片，直到真实图片生成完成
 */

const fs = require('fs');
const path = require('path');

// 创建SVG占位符图片
function createSVGPlaceholder(width, height, text, filename) {
  const svg = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#d4af37;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#b8860b;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#8b7355;stop-opacity:1" />
    </linearGradient>
    <pattern id="barbershop" patternUnits="userSpaceOnUse" width="40" height="40">
      <rect width="40" height="40" fill="url(#grad1)" opacity="0.1"/>
      <path d="M20 5 L35 20 L20 35 L5 20 Z" fill="#d4af37" opacity="0.2"/>
    </pattern>
  </defs>
  
  <!-- Background -->
  <rect width="100%" height="100%" fill="url(#barbershop)"/>
  
  <!-- Overlay -->
  <rect width="100%" height="100%" fill="rgba(0,0,0,0.3)"/>
  
  <!-- Barbershop Icon -->
  <g transform="translate(${width/2}, ${height/2 - 40})">
    <circle cx="0" cy="0" r="30" fill="#d4af37" opacity="0.8"/>
    <text x="0" y="8" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" fill="#000">✂️</text>
  </g>
  
  <!-- Text -->
  <text x="${width/2}" y="${height/2 + 20}" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#d4af37">${text}</text>
  <text x="${width/2}" y="${height/2 + 45}" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#ffffff" opacity="0.8">Tony's Barbershop</text>
  
  <!-- Decorative elements -->
  <rect x="10" y="10" width="3" height="20" fill="#d4af37" opacity="0.6"/>
  <rect x="10" y="35" width="3" height="20" fill="#d4af37" opacity="0.6"/>
  <rect x="${width-13}" y="10" width="3" height="20" fill="#d4af37" opacity="0.6"/>
  <rect x="${width-13}" y="35" width="3" height="20" fill="#d4af37" opacity="0.6"/>
</svg>`;

  const filepath = path.join(__dirname, '..', 'public', filename);
  fs.writeFileSync(filepath, svg);
  console.log(`✅ 创建占位符: ${filename}`);
}

// 图片配置
const placeholders = [
  {
    filename: 'hero-barbershop.jpg',
    width: 1920,
    height: 1080,
    text: '现代理发店内部'
  },
  {
    filename: 'about-story.jpg',
    width: 800,
    height: 600,
    text: '理发店历史'
  },
  {
    filename: 'about-barbershop.jpg',
    width: 800,
    height: 600,
    text: '团队工作环境'
  },
  {
    filename: 'team-member-1.jpg',
    width: 400,
    height: 400,
    text: '李师傅'
  },
  {
    filename: 'team-member-2.jpg',
    width: 400,
    height: 400,
    text: '王师傅'
  },
  {
    filename: 'team-member-3.jpg',
    width: 400,
    height: 400,
    text: '张师傅'
  },
  // Gallery 图片
  {
    filename: 'gallery-haircut-1.jpg',
    width: 400,
    height: 300,
    text: '经典商务短发'
  },
  {
    filename: 'gallery-haircut-2.jpg',
    width: 400,
    height: 300,
    text: '时尚渐变发型'
  },
  {
    filename: 'gallery-haircut-3.jpg',
    width: 400,
    height: 300,
    text: '个性创意发型'
  },
  {
    filename: 'gallery-haircut-4.jpg',
    width: 400,
    height: 300,
    text: '经典油头造型'
  },
  {
    filename: 'gallery-haircut-5.jpg',
    width: 400,
    height: 300,
    text: '青年时尚发型'
  },
  {
    filename: 'gallery-haircut-6.jpg',
    width: 400,
    height: 300,
    text: '婚礼造型'
  },
  {
    filename: 'gallery-beard-1.jpg',
    width: 400,
    height: 300,
    text: '精致胡须造型'
  },
  {
    filename: 'gallery-beard-2.jpg',
    width: 400,
    height: 300,
    text: '胡须精细修剪'
  },
  {
    filename: 'gallery-beard-3.jpg',
    width: 400,
    height: 300,
    text: '经典胡须搭配'
  },
  {
    filename: 'gallery-styling-1.jpg',
    width: 400,
    height: 300,
    text: '复古油头造型'
  },
  {
    filename: 'gallery-styling-2.jpg',
    width: 400,
    height: 300,
    text: '特殊场合造型'
  },
  {
    filename: 'gallery-styling-3.jpg',
    width: 400,
    height: 300,
    text: '现代产品造型'
  },
  {
    filename: 'gallery-interior-1.jpg',
    width: 400,
    height: 300,
    text: '等候区域'
  },
  {
    filename: 'gallery-interior-2.jpg',
    width: 400,
    height: 300,
    text: '专业设备'
  },
  {
    filename: 'gallery-interior-3.jpg',
    width: 400,
    height: 300,
    text: '店内环境'
  }
];

// 主函数
function main() {
  console.log('🎨 创建 Tony\'s Barbershop 占位符图片...\n');
  
  // 确保 public 目录存在
  const publicDir = path.join(__dirname, '..', 'public');
  if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir, { recursive: true });
  }

  // 创建所有占位符
  placeholders.forEach(config => {
    createSVGPlaceholder(config.width, config.height, config.text, config.filename);
  });

  console.log(`\n🎉 占位符创建完成! 共创建 ${placeholders.length} 个图片文件`);
  console.log('💡 这些是临时占位符，您可以稍后使用 Replicate API 生成真实图片');
  console.log('💡 运行 node scripts/generate-images.js 来生成真实图片');
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { createSVGPlaceholder, placeholders };
