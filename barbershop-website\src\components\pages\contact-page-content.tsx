"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { FadeIn, StaggeredFadeIn, SlideIn } from "@/components/animations/fade-in"
import { FloatingElement } from "@/components/animations/page-transition"


interface FormData {
  name: string
  email: string
  phone: string
  service: string
  message: string
}

interface FormErrors {
  name?: string
  email?: string
  phone?: string
  service?: string
  message?: string
}

export function ContactPageContent() {
  const [formData, setFormData] = useState<FormData>({
    name: "",
    email: "",
    phone: "",
    service: "",
    message: ""
  })

  const [errors, setErrors] = useState<FormErrors>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<"idle" | "success" | "error">("idle")

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    if (!formData.name.trim()) {
      newErrors.name = "请输入您的姓名"
    }

    if (!formData.email.trim()) {
      newErrors.email = "请输入您的邮箱"
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "请输入有效的邮箱地址"
    }

    if (!formData.phone.trim()) {
      newErrors.phone = "请输入您的电话号码"
    } else if (!/^1[3-9]\d{9}$/.test(formData.phone.replace(/\s|-/g, ""))) {
      newErrors.phone = "请输入有效的手机号码"
    }

    if (!formData.service) {
      newErrors.service = "请选择您需要的服务"
    }

    if (!formData.message.trim()) {
      newErrors.message = "请输入您的留言"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    setSubmitStatus("idle")

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // In a real application, you would send the data to your backend
      console.log("Form submitted:", formData)
      
      setSubmitStatus("success")
      setFormData({
        name: "",
        email: "",
        phone: "",
        service: "",
        message: ""
      })
      setErrors({})
    } catch (error) {
      console.error("Error submitting form:", error)
      setSubmitStatus("error")
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  const services = [
    { value: "", label: "请选择服务类型" },
    { value: "haircut", label: "理发服务" },
    { value: "beard", label: "胡须造型" },
    { value: "styling", label: "造型设计" },
    { value: "consultation", label: "形象咨询" },
    { value: "other", label: "其他服务" }
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-primary to-primary/80 text-primary-foreground">
        <FloatingElement delay={0}>
          <div className="container mx-auto px-4 text-center">
            <div className="max-w-3xl mx-auto">
              <FadeIn delay={0.2}>
                <h1 className="text-4xl md:text-5xl font-bold mb-6">
                  联系我们
                </h1>
              </FadeIn>
              <FadeIn delay={0.4}>
                <p className="text-xl mb-8 text-primary-foreground/90">
                  有任何问题或需要预约服务？我们随时为您提供专业的咨询和服务。
                </p>
              </FadeIn>
              <div className="flex flex-wrap justify-center gap-6 text-sm">
                <StaggeredFadeIn delay={0.6}>
                  <div className="flex items-center space-x-2">
                    <span className="text-accent text-lg">📞</span>
                    <span>电话咨询</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-accent text-lg">💬</span>
                    <span>在线留言</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-accent text-lg">📍</span>
                    <span>到店咨询</span>
                  </div>
                </StaggeredFadeIn>
              </div>
            </div>
          </div>
        </FloatingElement>
      </section>

      {/* Contact Form and Info */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <SlideIn direction="left">
              <Card className="h-fit">
                <CardHeader>
                  <CardTitle className="text-2xl">在线留言</CardTitle>
                  <p className="text-muted-foreground">
                    填写下方表单，我们会尽快回复您的咨询
                  </p>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                          姓名 *
                        </label>
                        <input
                          type="text"
                          id="name"
                          name="name"
                          value={formData.name}
                          onChange={(e) => handleInputChange("name", e.target.value)}
                          placeholder="请输入您的姓名"
                          required
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                          aria-describedby={errors.name ? "name-error" : undefined}
                        />
                        {errors.name && (
                          <p id="name-error" className="mt-1 text-sm text-red-600">
                            {errors.name}
                          </p>
                        )}
                      </div>
                      <div>
                        <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                          电话 *
                        </label>
                        <input
                          type="tel"
                          id="phone"
                          name="phone"
                          value={formData.phone}
                          onChange={(e) => handleInputChange("phone", e.target.value)}
                          placeholder="请输入您的电话号码"
                          required
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                          aria-describedby={errors.phone ? "phone-error" : undefined}
                        />
                        {errors.phone && (
                          <p id="phone-error" className="mt-1 text-sm text-red-600">
                            {errors.phone}
                          </p>
                        )}
                      </div>
                    </div>

                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                        邮箱 *
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange("email", e.target.value)}
                        placeholder="请输入您的邮箱地址"
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                        aria-describedby={errors.email ? "email-error" : undefined}
                      />
                      {errors.email && (
                        <p id="email-error" className="mt-1 text-sm text-red-600">
                          {errors.email}
                        </p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="service" className="block text-sm font-medium text-gray-700 mb-2">
                        服务类型 *
                      </label>
                      <select
                        id="service"
                        name="service"
                        value={formData.service}
                        onChange={(e) => handleInputChange("service", e.target.value)}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                        aria-describedby={errors.service ? "service-error" : undefined}
                      >
                        <option value="">请选择服务类型</option>
                        {services.map((service) => (
                          <option key={service.value} value={service.value}>
                            {service.label}
                          </option>
                        ))}
                      </select>
                      {errors.service && (
                        <p id="service-error" className="mt-1 text-sm text-red-600">
                          {errors.service}
                        </p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                        留言内容 *
                      </label>
                      <textarea
                        id="message"
                        name="message"
                        value={formData.message}
                        onChange={(e) => handleInputChange("message", e.target.value)}
                        placeholder="请详细描述您的需求或问题..."
                        rows={4}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                        aria-describedby={errors.message ? "message-error" : undefined}
                      />
                      {errors.message && (
                        <p id="message-error" className="mt-1 text-sm text-red-600">
                          {errors.message}
                        </p>
                      )}
                    </div>

                    {submitStatus === "success" && (
                      <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                        <div className="flex items-center space-x-2">
                          <span className="text-green-600">✓</span>
                          <span className="text-green-800">留言提交成功！我们会尽快回复您。</span>
                        </div>
                      </div>
                    )}

                    {submitStatus === "error" && (
                      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                        <div className="flex items-center space-x-2">
                          <span className="text-red-600">✗</span>
                          <span className="text-red-800">提交失败，请稍后重试。</span>
                        </div>
                      </div>
                    )}

                    <Button
                      type="submit"
                      className="w-full"
                      disabled={isSubmitting}
                      size="lg"
                    >
                      {isSubmitting ? "提交中..." : "发送留言"}
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </SlideIn>

            {/* Contact Information */}
            <SlideIn direction="right">
              <div className="space-y-8">
                {/* Contact Details */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-2xl">联系方式</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="flex items-start space-x-4">
                      <div className="text-2xl">📍</div>
                      <div>
                        <h3 className="font-semibold mb-1">店铺地址</h3>
                        <p className="text-muted-foreground">
                          北京市朝阳区三里屯太古里南区<br />
                          S8-32号 Tony's Barbershop
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-4">
                      <div className="text-2xl">📞</div>
                      <div>
                        <h3 className="font-semibold mb-1">联系电话</h3>
                        <p className="text-muted-foreground">
                          <a href="tel:+8610-8888-8888" className="hover:text-primary transition-colors">
                            010-8888-8888
                          </a>
                        </p>
                        <p className="text-muted-foreground">
                          <a href="tel:+86138-0013-8000" className="hover:text-primary transition-colors">
                            138-0013-8000
                          </a>
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-4">
                      <div className="text-2xl">📧</div>
                      <div>
                        <h3 className="font-semibold mb-1">邮箱地址</h3>
                        <p className="text-muted-foreground">
                          <a href="mailto:<EMAIL>" className="hover:text-primary transition-colors">
                            <EMAIL>
                          </a>
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-4">
                      <div className="text-2xl">🕒</div>
                      <div>
                        <h3 className="font-semibold mb-1">营业时间</h3>
                        <div className="text-muted-foreground space-y-1">
                          <p>周一至周五：10:00 - 21:00</p>
                          <p>周六至周日：09:00 - 22:00</p>
                          <p className="text-sm text-primary">节假日正常营业</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Map Placeholder */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-xl">店铺位置</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="aspect-video bg-muted rounded-lg flex items-center justify-center">
                      <div className="text-center">
                        <div className="text-4xl mb-2">🗺️</div>
                        <p className="text-muted-foreground">地图位置</p>
                        <p className="text-sm text-muted-foreground mt-1">
                          三里屯太古里南区 S8-32号
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Social Media */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-xl">关注我们</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4">
                      <Button variant="outline" className="flex items-center space-x-2">
                        <span>💬</span>
                        <span>微信公众号</span>
                      </Button>
                      <Button variant="outline" className="flex items-center space-x-2">
                        <span>📱</span>
                        <span>小红书</span>
                      </Button>
                      <Button variant="outline" className="flex items-center space-x-2">
                        <span>📸</span>
                        <span>Instagram</span>
                      </Button>
                      <Button variant="outline" className="flex items-center space-x-2">
                        <span>🎵</span>
                        <span>抖音</span>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </SlideIn>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <FadeIn>
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                常见问题
              </h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                以下是客户经常询问的问题，希望能帮助您更好地了解我们的服务
              </p>
            </div>
          </FadeIn>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
            <StaggeredFadeIn>
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">需要提前预约吗？</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  建议提前预约以确保您的时间安排。您可以通过电话、微信或在线预约系统进行预约。
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">服务价格如何？</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  我们提供多种价位的服务套餐，具体价格请查看服务页面或到店咨询。
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">可以刷卡支付吗？</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  支持现金、刷卡、微信支付、支付宝等多种支付方式。
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">有停车位吗？</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  太古里有地下停车场，前2小时免费，之后按标准收费。
                </p>
              </CardContent>
            </Card>
            </StaggeredFadeIn>
          </div>
        </div>
      </section>
    </div>
  )
}
