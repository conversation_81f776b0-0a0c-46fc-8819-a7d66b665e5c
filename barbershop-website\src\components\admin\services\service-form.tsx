"use client"

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/admin/ui/badge'
import { 
  Scissors, 
  DollarSign, 
  Clock,
  Tag,
  Save,
  ArrowLeft,
  Plus
} from 'lucide-react'
import { serviceStore, categoryStore } from '@/lib/admin/storage'
import { Service, ServiceCategory } from '@/lib/types/admin'

interface ServiceFormProps {
  service?: Service
  onSubmit?: (service: Service) => void
  onCancel?: () => void
}

export function ServiceForm({ service, onSubmit, onCancel }: ServiceFormProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [categories, setCategories] = useState<ServiceCategory[]>([])
  
  const [formData, setFormData] = useState({
    name: service?.name || '',
    description: service?.description || '',
    price: service?.price || 0,
    duration: service?.duration || 30,
    categoryId: service?.category || '',
    isActive: service?.isActive ?? true
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    loadCategories()
  }, [])

  const loadCategories = () => {
    const allCategories = categoryStore.getAll()
    setCategories(allCategories.filter(c => c.isActive))
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = '请输入服务名称'
    }

    if (formData.price <= 0) {
      newErrors.price = '价格必须大于0'
    }

    if (formData.duration <= 0) {
      newErrors.duration = '时长必须大于0'
    }

    if (!formData.categoryId) {
      newErrors.categoryId = '请选择服务分类'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)
    try {
      let result: Service | null

      if (service) {
        // 更新服务
        result = serviceStore.update(service.id, {
          name: formData.name.trim(),
          description: formData.description.trim() || '',
          price: formData.price,
          duration: formData.duration,
          category: formData.categoryId,
          isActive: formData.isActive,
          popularity: service.popularity
        })
      } else {
        // 创建新服务
        result = serviceStore.create({
          name: formData.name.trim(),
          description: formData.description.trim() || '',
          price: formData.price,
          duration: formData.duration,
          category: formData.categoryId,
          isActive: formData.isActive,
          popularity: 0
        })
      }

      if (result) {
        if (onSubmit) {
          onSubmit(result)
        } else {
          // 跳转到列表页面并显示成功状态
          const successType = service ? 'updated' : 'created'
          router.push(`/admin/services?success=${successType}`)
        }
      }
    } catch (error) {
      console.error('Failed to save service:', error)
      setErrors({ submit: '保存失败，请重试' })
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    if (onCancel) {
      onCancel()
    } else {
      router.back()
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes}分钟`
    }
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`
  }

  const durationOptions = [
    { value: 15, label: '15分钟' },
    { value: 20, label: '20分钟' },
    { value: 30, label: '30分钟' },
    { value: 45, label: '45分钟' },
    { value: 60, label: '1小时' },
    { value: 90, label: '1.5小时' },
    { value: 120, label: '2小时' },
    { value: 150, label: '2.5小时' },
    { value: 180, label: '3小时' }
  ]

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* 基本信息 */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium flex items-center">
          <Scissors className="h-5 w-5 mr-2" />
          基本信息
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              服务名称 <span className="text-red-500">*</span>
            </label>
            <Input
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="请输入服务名称"
              className={errors.name ? 'border-red-500' : ''}
            />
            {errors.name && (
              <p className="text-sm text-red-500 mt-1">{errors.name}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              服务分类 <span className="text-red-500">*</span>
            </label>
            <select
              value={formData.categoryId}
              onChange={(e) => handleInputChange('categoryId', e.target.value)}
              className={`w-full px-3 py-2 border border-border rounded-md bg-background ${
                errors.categoryId ? 'border-red-500' : ''
              }`}
            >
              <option value="">请选择分类</option>
              {categories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
            {errors.categoryId && (
              <p className="text-sm text-red-500 mt-1">{errors.categoryId}</p>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">
            服务描述
          </label>
          <Textarea
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="请输入服务描述（可选）"
            rows={3}
          />
        </div>
      </div>

      {/* 价格和时长 */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium flex items-center">
          <DollarSign className="h-5 w-5 mr-2" />
          价格设置
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              服务价格 <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">¥</span>
              <Input
                type="number"
                value={formData.price}
                onChange={(e) => handleInputChange('price', Number(e.target.value))}
                placeholder="0"
                className={`pl-8 ${errors.price ? 'border-red-500' : ''}`}
                min="0"
                step="1"
              />
            </div>
            {errors.price && (
              <p className="text-sm text-red-500 mt-1">{errors.price}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              服务时长 <span className="text-red-500">*</span>
            </label>
            <div className="space-y-2">
              <select
                value={formData.duration}
                onChange={(e) => handleInputChange('duration', Number(e.target.value))}
                className={`w-full px-3 py-2 border border-border rounded-md bg-background ${
                  errors.duration ? 'border-red-500' : ''
                }`}
              >
                {durationOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">
                  或自定义：
                </span>
                <Input
                  type="number"
                  value={formData.duration}
                  onChange={(e) => handleInputChange('duration', Number(e.target.value))}
                  placeholder="分钟"
                  className="w-20"
                  min="1"
                  step="1"
                />
                <span className="text-sm text-muted-foreground">分钟</span>
              </div>
            </div>
            {errors.duration && (
              <p className="text-sm text-red-500 mt-1">{errors.duration}</p>
            )}
          </div>
        </div>

        {/* 预览 */}
        <div className="p-4 bg-muted/20 rounded-lg border border-border">
          <h4 className="font-medium mb-2">服务预览</h4>
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">{formData.name || '服务名称'}</p>
              <p className="text-sm text-muted-foreground">
                {formData.description || '暂无描述'}
              </p>
            </div>
            <div className="text-right">
              <p className="text-lg font-semibold text-green-600">¥{formData.price}</p>
              <p className="text-sm text-muted-foreground">
                {formatDuration(formData.duration)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 状态设置 */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">状态设置</h3>
        
        <div className="flex items-center space-x-3">
          <input
            type="checkbox"
            id="isActive"
            checked={formData.isActive}
            onChange={(e) => handleInputChange('isActive', e.target.checked)}
            className="w-4 h-4 text-primary border-border rounded focus:ring-primary"
          />
          <label htmlFor="isActive" className="text-sm font-medium">
            启用此服务
          </label>
          <Badge variant={formData.isActive ? 'success' : 'destructive'} size="sm">
            {formData.isActive ? '启用' : '停用'}
          </Badge>
        </div>
        <p className="text-sm text-muted-foreground">
          停用的服务将不会在预约时显示，但不会影响已有的预约记录。
        </p>
      </div>

      {/* 错误信息 */}
      {errors.submit && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{errors.submit}</p>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="flex justify-end space-x-3 pt-6 border-t">
        <Button
          type="button"
          variant="outline"
          onClick={handleCancel}
          disabled={loading}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          取消
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? (
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
          ) : (
            <Save className="h-4 w-4 mr-2" />
          )}
          {service ? '更新服务' : '创建服务'}
        </Button>
      </div>
    </form>
  )
}
