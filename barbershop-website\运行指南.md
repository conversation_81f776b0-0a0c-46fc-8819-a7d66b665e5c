# Tony's Barbershop 项目运行指南

## 📋 项目概述

这是一个完整的理发店网站项目，包含：
- 🌐 **前台网站**：展示理发店信息、服务、团队等
- 🔧 **管理后台**：完整的业务管理系统

## 🛠️ 技术栈

- **框架**: Next.js 15 + React 19
- **语言**: TypeScript
- **样式**: Tailwind CSS 4 + shadcn/ui
- **图标**: Lucide React + Emoji占位符
- **存储**: 本地JSON存储（localStorage）

## 🚀 快速开始

### 1. 环境要求

确保您的系统已安装：
- **Node.js** 18.0 或更高版本
- **npm** 或 **yarn** 包管理器

### 2. 安装依赖

```bash
# 进入项目目录
cd barbershop-website

# 安装依赖（如果还没有安装）
npm install
```

### 3. 启动开发服务器

```bash
# 启动开发服务器
npm run dev
```

服务器启动后，您将看到类似以下的输出：
```
▲ Next.js 15.3.4
- Local:        http://localhost:3000
- Ready in 2.1s
```

### 4. 访问网站

打开浏览器访问：
- **前台网站**: http://localhost:3000
- **管理后台**: http://localhost:3000/admin

## 🔐 管理后台登录

### 默认管理员账号
- **用户名**: `admin`
- **密码**: `admin123`

### 管理后台功能模块

1. **📊 数据分析** - 收入统计、趋势分析、员工绩效
2. **📅 预约管理** - 预约CRUD、状态管理、日历视图
3. **👤 客户管理** - 客户信息、等级系统、服务偏好
4. **✂️ 服务管理** - 服务项目、分类管理、价格设置
5. **👨‍💼 员工管理** - 员工信息、技能管理、工作时间

## 📱 功能特点

### 前台网站
- ✅ 响应式设计（支持手机、平板、桌面）
- ✅ 现代化UI设计
- ✅ SEO优化
- ✅ 服务展示
- ✅ 团队介绍
- ✅ 联系方式
- ✅ 图片画廊

### 管理后台
- ✅ 完整的CRUD操作
- ✅ 数据关联管理
- ✅ 实时数据统计
- ✅ 响应式管理界面
- ✅ 本地数据持久化
- ✅ 用户权限管理

## 🎯 使用说明

### 首次使用
1. 启动项目后，先访问管理后台
2. 使用默认账号登录
3. 添加员工信息
4. 设置服务项目和分类
5. 开始管理客户和预约

### 数据管理
- 所有数据存储在浏览器的localStorage中
- 数据在同一浏览器中持久保存
- 支持数据导出（CSV格式）

## 🔧 开发命令

```bash
# 开发模式
npm run dev

# 构建生产版本
npm run build

# 启动生产服务器
npm run start

# 代码检查
npm run lint
```

## 📂 项目结构

```
barbershop-website/
├── src/
│   ├── app/                 # Next.js App Router页面
│   │   ├── admin/          # 管理后台页面
│   │   ├── about/          # 关于我们页面
│   │   ├── contact/        # 联系我们页面
│   │   ├── services/       # 服务页面
│   │   └── page.tsx        # 首页
│   ├── components/         # React组件
│   │   ├── admin/          # 管理后台组件
│   │   ├── pages/          # 页面组件
│   │   ├── sections/       # 页面区块组件
│   │   └── ui/             # UI基础组件
│   └── lib/                # 工具库
│       ├── admin/          # 管理后台逻辑
│       └── types/          # TypeScript类型定义
├── public/                 # 静态资源
└── scripts/               # 脚本文件
```

## 🎨 自定义配置

### 主题颜色
项目使用自定义颜色方案：
- **主色**: 黑色 (#000000)
- **次色**: 红色 (#dc2626)
- **强调色**: 金色 (#d4af37)

### 修改配置
主要配置文件：
- `tailwind.config.ts` - Tailwind CSS配置
- `next.config.ts` - Next.js配置
- `tsconfig.json` - TypeScript配置

## 🚨 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查找占用3000端口的进程
   netstat -ano | findstr :3000
   # 或使用其他端口
   npm run dev -- -p 3001
   ```

2. **依赖安装失败**
   ```bash
   # 清除缓存重新安装
   npm cache clean --force
   npm install
   ```

3. **构建失败**
   ```bash
   # 清除Next.js缓存
   rm -rf .next
   npm run build
   ```

## 📞 技术支持

如果您在运行过程中遇到问题，请检查：
1. Node.js版本是否符合要求
2. 网络连接是否正常
3. 端口3000是否被占用
4. 依赖是否正确安装

## 🎉 开始使用

现在您可以开始使用Tony's Barbershop管理系统了！

1. 启动开发服务器：`npm run dev`
2. 访问前台：http://localhost:3000
3. 访问后台：http://localhost:3000/admin
4. 使用admin/admin123登录管理后台

祝您使用愉快！✂️💈
