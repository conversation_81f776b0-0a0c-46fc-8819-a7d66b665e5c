{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/lib/utils.ts"], "sourcesContent": ["export function cn(...inputs: (string | undefined | null | boolean)[]) {\n  return inputs.filter(Boolean).join(' ')\n}\n\nexport function formatPhoneNumber(phone: string): string {\n  const cleaned = phone.replace(/\\D/g, '')\n  const match = cleaned.match(/^(\\d{3})(\\d{3})(\\d{4})$/)\n  if (match) {\n    return `(${match[1]}) ${match[2]}-${match[3]}`\n  }\n  return phone\n}\n\nexport function formatTime(time: string): string {\n  const [hours, minutes] = time.split(':')\n  const hour = parseInt(hours, 10)\n  const ampm = hour >= 12 ? 'PM' : 'AM'\n  const displayHour = hour % 12 || 12\n  return `${displayHour}:${minutes} ${ampm}`\n}\n"], "names": [], "mappings": ";;;;;AAAO,SAAS,GAAG,GAAG,MAA+C;IACnE,OAAO,OAAO,MAAM,CAAC,SAAS,IAAI,CAAC;AACrC;AAEO,SAAS,kBAAkB,KAAa;IAC7C,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO;IACrC,MAAM,QAAQ,QAAQ,KAAK,CAAC;IAC5B,IAAI,OAAO;QACT,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE;IAChD;IACA,OAAO;AACT;AAEO,SAAS,WAAW,IAAY;IACrC,MAAM,CAAC,OAAO,QAAQ,GAAG,KAAK,KAAK,CAAC;IACpC,MAAM,OAAO,SAAS,OAAO;IAC7B,MAAM,OAAO,QAAQ,KAAK,OAAO;IACjC,MAAM,cAAc,OAAO,MAAM;IACjC,OAAO,GAAG,YAAY,CAAC,EAAE,QAAQ,CAAC,EAAE,MAAM;AAC5C", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: \"default\" | \"outline\" | \"secondary\" | \"ghost\" | \"link\"\n  size?: \"default\" | \"sm\" | \"lg\" | \"icon\"\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className = \"\", variant = \"default\", size = \"default\", asChild = false, children, ...props }, ref) => {\n    const baseClasses = \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\"\n\n    const variantClasses = {\n      default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n      outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n      secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n      ghost: \"hover:bg-accent hover:text-accent-foreground\",\n      link: \"text-primary underline-offset-4 hover:underline\",\n    }\n\n    const sizeClasses = {\n      default: \"h-10 px-4 py-2\",\n      sm: \"h-9 rounded-md px-3\",\n      lg: \"h-11 rounded-md px-8\",\n      icon: \"h-10 w-10\",\n    }\n\n    const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`.trim()\n\n    if (asChild && React.isValidElement(children)) {\n      return React.cloneElement(children, {\n        className: classes,\n        ref,\n        ...props,\n      })\n    }\n\n    return (\n      <button\n        className={classes}\n        ref={ref}\n        {...props}\n      >\n        {children}\n      </button>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;;;AAQA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,YAAY,EAAE,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC/F,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,SAAS;QACT,WAAW;QACX,OAAO;QACP,MAAM;IACR;IAEA,MAAM,cAAc;QAClB,SAAS;QACT,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,MAAM,UAAU,GAAG,YAAY,CAAC,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC,IAAI;IAElG,IAAI,yBAAW,CAAA,GAAA,qMAAA,CAAA,iBAAoB,AAAD,EAAE,WAAW;QAC7C,qBAAO,CAAA,GAAA,qMAAA,CAAA,eAAkB,AAAD,EAAE,UAAU;YAClC,WAAW;YACX;YACA,GAAG,KAAK;QACV;IACF;IAEA,qBACE,8OAAC;QACC,WAAW;QACX,KAAK;QACJ,GAAG,KAAK;kBAER;;;;;;AAGP;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/python_study/tony_project/barbershop-website/src/app/contact/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON>ada<PERSON> } from \"next\"\nimport { useState } from \"react\"\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\n\ninterface FormData {\n  name: string\n  email: string\n  phone: string\n  service: string\n  message: string\n}\n\ninterface FormErrors {\n  name?: string\n  email?: string\n  phone?: string\n  service?: string\n  message?: string\n}\n\nconst services = [\n  { value: \"\", label: \"请选择服务类型\" },\n  { value: \"haircut\", label: \"理发服务\" },\n  { value: \"beard\", label: \"胡须造型\" },\n  { value: \"styling\", label: \"造型设计\" },\n  { value: \"wash\", label: \"洗发护理\" },\n  { value: \"facial\", label: \"面部护理\" },\n  { value: \"package\", label: \"套餐服务\" },\n  { value: \"consultation\", label: \"造型咨询\" },\n  { value: \"other\", label: \"其他服务\" }\n]\n\nconst businessHours = [\n  { day: \"周一\", hours: \"9:00 - 20:00\", isOpen: true },\n  { day: \"周二\", hours: \"9:00 - 20:00\", isOpen: true },\n  { day: \"周三\", hours: \"9:00 - 20:00\", isOpen: true },\n  { day: \"周四\", hours: \"9:00 - 20:00\", isOpen: true },\n  { day: \"周五\", hours: \"9:00 - 21:00\", isOpen: true },\n  { day: \"周六\", hours: \"8:00 - 21:00\", isOpen: true },\n  { day: \"周日\", hours: \"10:00 - 18:00\", isOpen: true }\n]\n\nexport default function ContactPage() {\n  const [formData, setFormData] = useState<FormData>({\n    name: \"\",\n    email: \"\",\n    phone: \"\",\n    service: \"\",\n    message: \"\"\n  })\n  \n  const [errors, setErrors] = useState<FormErrors>({})\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [submitStatus, setSubmitStatus] = useState<\"idle\" | \"success\" | \"error\">(\"idle\")\n\n  const validateForm = (): boolean => {\n    const newErrors: FormErrors = {}\n\n    if (!formData.name.trim()) {\n      newErrors.name = \"请输入您的姓名\"\n    }\n\n    if (!formData.email.trim()) {\n      newErrors.email = \"请输入邮箱地址\"\n    } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.email)) {\n      newErrors.email = \"请输入有效的邮箱地址\"\n    }\n\n    if (!formData.phone.trim()) {\n      newErrors.phone = \"请输入联系电话\"\n    } else if (!/^1[3-9]\\d{9}$/.test(formData.phone.replace(/\\s|-/g, \"\"))) {\n      newErrors.phone = \"请输入有效的手机号码\"\n    }\n\n    if (!formData.service) {\n      newErrors.service = \"请选择服务类型\"\n    }\n\n    if (!formData.message.trim()) {\n      newErrors.message = \"请输入留言内容\"\n    } else if (formData.message.trim().length < 10) {\n      newErrors.message = \"留言内容至少需要10个字符\"\n    }\n\n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n  const handleInputChange = (field: keyof FormData, value: string) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: undefined }))\n    }\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!validateForm()) {\n      return\n    }\n\n    setIsSubmitting(true)\n    setSubmitStatus(\"idle\")\n\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 2000))\n      \n      // In a real application, you would send the data to your backend\n      console.log(\"Form submitted:\", formData)\n      \n      setSubmitStatus(\"success\")\n      setFormData({\n        name: \"\",\n        email: \"\",\n        phone: \"\",\n        service: \"\",\n        message: \"\"\n      })\n    } catch (error) {\n      setSubmitStatus(\"error\")\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen\">\n      {/* Hero Section */}\n      <section className=\"relative py-20 bg-gradient-to-r from-primary to-primary/80 text-primary-foreground\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <div className=\"max-w-3xl mx-auto\">\n            <h1 className=\"text-4xl md:text-5xl font-bold mb-6\">\n              联系我们\n            </h1>\n            <p className=\"text-xl mb-8 text-primary-foreground/90\">\n              有任何问题或需要预约服务？我们随时为您提供专业的咨询和服务。期待与您的交流！\n            </p>\n            <div className=\"flex flex-wrap justify-center gap-6 text-sm\">\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"text-accent text-lg\">📞</span>\n                <span>专业咨询</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"text-accent text-lg\">📍</span>\n                <span>便利位置</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"text-accent text-lg\">⏰</span>\n                <span>灵活时间</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Contact Form and Info Section */}\n      <section className=\"py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n            {/* Contact Form */}\n            <div>\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-2xl flex items-center space-x-2\">\n                    <span>📝</span>\n                    <span>联系表单</span>\n                  </CardTitle>\n                  <p className=\"text-muted-foreground\">\n                    填写下方表单，我们会在24小时内回复您\n                  </p>\n                </CardHeader>\n                <CardContent>\n                  <form onSubmit={handleSubmit} className=\"space-y-6\">\n                    {/* Name Field */}\n                    <div>\n                      <label htmlFor=\"name\" className=\"block text-sm font-medium mb-2\">\n                        姓名 *\n                      </label>\n                      <input\n                        type=\"text\"\n                        id=\"name\"\n                        value={formData.name}\n                        onChange={(e) => handleInputChange(\"name\", e.target.value)}\n                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary ${\n                          errors.name ? \"border-red-500\" : \"border-input\"\n                        }`}\n                        placeholder=\"请输入您的姓名\"\n                      />\n                      {errors.name && (\n                        <p className=\"text-red-500 text-sm mt-1\">{errors.name}</p>\n                      )}\n                    </div>\n\n                    {/* Email Field */}\n                    <div>\n                      <label htmlFor=\"email\" className=\"block text-sm font-medium mb-2\">\n                        邮箱地址 *\n                      </label>\n                      <input\n                        type=\"email\"\n                        id=\"email\"\n                        value={formData.email}\n                        onChange={(e) => handleInputChange(\"email\", e.target.value)}\n                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary ${\n                          errors.email ? \"border-red-500\" : \"border-input\"\n                        }`}\n                        placeholder=\"请输入您的邮箱地址\"\n                      />\n                      {errors.email && (\n                        <p className=\"text-red-500 text-sm mt-1\">{errors.email}</p>\n                      )}\n                    </div>\n\n                    {/* Phone Field */}\n                    <div>\n                      <label htmlFor=\"phone\" className=\"block text-sm font-medium mb-2\">\n                        联系电话 *\n                      </label>\n                      <input\n                        type=\"tel\"\n                        id=\"phone\"\n                        value={formData.phone}\n                        onChange={(e) => handleInputChange(\"phone\", e.target.value)}\n                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary ${\n                          errors.phone ? \"border-red-500\" : \"border-input\"\n                        }`}\n                        placeholder=\"请输入您的手机号码\"\n                      />\n                      {errors.phone && (\n                        <p className=\"text-red-500 text-sm mt-1\">{errors.phone}</p>\n                      )}\n                    </div>\n\n                    {/* Service Selection */}\n                    <div>\n                      <label htmlFor=\"service\" className=\"block text-sm font-medium mb-2\">\n                        服务类型 *\n                      </label>\n                      <select\n                        id=\"service\"\n                        value={formData.service}\n                        onChange={(e) => handleInputChange(\"service\", e.target.value)}\n                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary ${\n                          errors.service ? \"border-red-500\" : \"border-input\"\n                        }`}\n                      >\n                        {services.map((service) => (\n                          <option key={service.value} value={service.value}>\n                            {service.label}\n                          </option>\n                        ))}\n                      </select>\n                      {errors.service && (\n                        <p className=\"text-red-500 text-sm mt-1\">{errors.service}</p>\n                      )}\n                    </div>\n\n                    {/* Message Field */}\n                    <div>\n                      <label htmlFor=\"message\" className=\"block text-sm font-medium mb-2\">\n                        留言内容 *\n                      </label>\n                      <textarea\n                        id=\"message\"\n                        rows={4}\n                        value={formData.message}\n                        onChange={(e) => handleInputChange(\"message\", e.target.value)}\n                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary resize-none ${\n                          errors.message ? \"border-red-500\" : \"border-input\"\n                        }`}\n                        placeholder=\"请详细描述您的需求或问题...\"\n                      />\n                      {errors.message && (\n                        <p className=\"text-red-500 text-sm mt-1\">{errors.message}</p>\n                      )}\n                    </div>\n\n                    {/* Submit Button */}\n                    <Button\n                      type=\"submit\"\n                      disabled={isSubmitting}\n                      className=\"w-full bg-primary hover:bg-primary/90 text-primary-foreground font-semibold py-3\"\n                    >\n                      {isSubmitting ? (\n                        <span className=\"flex items-center space-x-2\">\n                          <span>⏳</span>\n                          <span>提交中...</span>\n                        </span>\n                      ) : (\n                        <span className=\"flex items-center space-x-2\">\n                          <span>📤</span>\n                          <span>发送消息</span>\n                        </span>\n                      )}\n                    </Button>\n\n                    {/* Submit Status Messages */}\n                    {submitStatus === \"success\" && (\n                      <div className=\"bg-green-50 border border-green-200 rounded-md p-4 text-green-800\">\n                        <div className=\"flex items-center space-x-2\">\n                          <span>✅</span>\n                          <span>消息发送成功！我们会尽快回复您。</span>\n                        </div>\n                      </div>\n                    )}\n\n                    {submitStatus === \"error\" && (\n                      <div className=\"bg-red-50 border border-red-200 rounded-md p-4 text-red-800\">\n                        <div className=\"flex items-center space-x-2\">\n                          <span>❌</span>\n                          <span>发送失败，请稍后重试或直接联系我们。</span>\n                        </div>\n                      </div>\n                    )}\n                  </form>\n                </CardContent>\n              </Card>\n            </div>\n\n            {/* Contact Information */}\n            <div className=\"space-y-6\">\n              {/* Business Hours */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-xl flex items-center space-x-2\">\n                    <span>⏰</span>\n                    <span>营业时间</span>\n                  </CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"space-y-3\">\n                    {businessHours.map((schedule) => (\n                      <div key={schedule.day} className=\"flex justify-between items-center\">\n                        <span className=\"font-medium\">{schedule.day}</span>\n                        <span className={`${schedule.isOpen ? \"text-green-600\" : \"text-red-600\"}`}>\n                          {schedule.hours}\n                        </span>\n                      </div>\n                    ))}\n                  </div>\n                  <div className=\"mt-4 p-3 bg-accent/10 rounded-md\">\n                    <p className=\"text-sm text-muted-foreground\">\n                      💡 建议提前预约，确保您的专属时间\n                    </p>\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* Contact Details */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-xl flex items-center space-x-2\">\n                    <span>📞</span>\n                    <span>联系方式</span>\n                  </CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div className=\"flex items-center space-x-3\">\n                    <span className=\"text-lg\">📱</span>\n                    <div>\n                      <p className=\"font-medium\">电话预约</p>\n                      <p className=\"text-muted-foreground\">+86 138-0000-0000</p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <span className=\"text-lg\">📧</span>\n                    <div>\n                      <p className=\"font-medium\">邮箱咨询</p>\n                      <p className=\"text-muted-foreground\"><EMAIL></p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <span className=\"text-lg\">💬</span>\n                    <div>\n                      <p className=\"font-medium\">微信客服</p>\n                      <p className=\"text-muted-foreground\">ClassicCuts2024</p>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* Location */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-xl flex items-center space-x-2\">\n                    <span>📍</span>\n                    <span>店铺位置</span>\n                  </CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div>\n                    <p className=\"font-medium mb-2\">Classic Cuts 理发店</p>\n                    <p className=\"text-muted-foreground mb-4\">\n                      上海市黄浦区南京东路123号<br />\n                      新世界大厦2楼201室\n                    </p>\n                  </div>\n                  \n                  {/* Map Placeholder */}\n                  <div className=\"aspect-video bg-muted rounded-lg flex items-center justify-center\">\n                    <div className=\"text-center\">\n                      <div className=\"text-4xl mb-2\">🗺️</div>\n                      <p className=\"text-muted-foreground\">地图位置</p>\n                      <p className=\"text-sm text-muted-foreground mt-1\">\n                        点击查看详细路线\n                      </p>\n                    </div>\n                  </div>\n\n                  {/* Transportation */}\n                  <div className=\"space-y-3\">\n                    <h4 className=\"font-medium\">🚇 交通指南</h4>\n                    <div className=\"space-y-2 text-sm text-muted-foreground\">\n                      <p>• 地铁1号线、2号线人民广场站 (3号出口步行5分钟)</p>\n                      <p>• 地铁8号线人民广场站 (1号出口步行3分钟)</p>\n                      <p>• 公交20、37、49路人民广场站</p>\n                    </div>\n                  </div>\n\n                  {/* Parking */}\n                  <div className=\"space-y-3\">\n                    <h4 className=\"font-medium\">🅿️ 停车信息</h4>\n                    <div className=\"space-y-2 text-sm text-muted-foreground\">\n                      <p>• 新世界大厦地下停车场 (B1-B3层)</p>\n                      <p>• 人民广场地下停车场 (步行2分钟)</p>\n                      <p>• 周边路边停车位 (收费标准：10元/小时)</p>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-20 bg-muted/50\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <div className=\"max-w-3xl mx-auto\">\n            <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">\n              准备好开始您的造型之旅了吗？\n            </h2>\n            <p className=\"text-lg text-muted-foreground mb-8\">\n              立即预约或联系我们，让专业理发师为您打造完美造型\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n              <Button asChild size=\"lg\" className=\"bg-primary hover:bg-primary/90 text-primary-foreground font-semibold px-8 py-3 text-lg\">\n                <a href=\"/booking\">在线预约</a>\n              </Button>\n              <Button asChild variant=\"outline\" size=\"lg\" className=\"px-8 py-3 text-lg\">\n                <a href=\"tel:+8613800000000\">电话预约</a>\n              </Button>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAuBA,MAAM,WAAW;IACf;QAAE,OAAO;QAAI,OAAO;IAAU;IAC9B;QAAE,OAAO;QAAW,OAAO;IAAO;IAClC;QAAE,OAAO;QAAS,OAAO;IAAO;IAChC;QAAE,OAAO;QAAW,OAAO;IAAO;IAClC;QAAE,OAAO;QAAQ,OAAO;IAAO;IAC/B;QAAE,OAAO;QAAU,OAAO;IAAO;IACjC;QAAE,OAAO;QAAW,OAAO;IAAO;IAClC;QAAE,OAAO;QAAgB,OAAO;IAAO;IACvC;QAAE,OAAO;QAAS,OAAO;IAAO;CACjC;AAED,MAAM,gBAAgB;IACpB;QAAE,KAAK;QAAM,OAAO;QAAgB,QAAQ;IAAK;IACjD;QAAE,KAAK;QAAM,OAAO;QAAgB,QAAQ;IAAK;IACjD;QAAE,KAAK;QAAM,OAAO;QAAgB,QAAQ;IAAK;IACjD;QAAE,KAAK;QAAM,OAAO;QAAgB,QAAQ;IAAK;IACjD;QAAE,KAAK;QAAM,OAAO;QAAgB,QAAQ;IAAK;IACjD;QAAE,KAAK;QAAM,OAAO;QAAgB,QAAQ;IAAK;IACjD;QAAE,KAAK;QAAM,OAAO;QAAiB,QAAQ;IAAK;CACnD;AAEc,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,SAAS;IACX;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,CAAC;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgC;IAE/E,MAAM,eAAe;QACnB,MAAM,YAAwB,CAAC;QAE/B,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB;QAEA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB,OAAO,IAAI,CAAC,6BAA6B,IAAI,CAAC,SAAS,KAAK,GAAG;YAC7D,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB,OAAO,IAAI,CAAC,gBAAgB,IAAI,CAAC,SAAS,KAAK,CAAC,OAAO,CAAC,SAAS,MAAM;YACrE,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,UAAU,OAAO,GAAG;QACtB;QAEA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,UAAU,OAAO,GAAG;QACtB,OAAO,IAAI,SAAS,OAAO,CAAC,IAAI,GAAG,MAAM,GAAG,IAAI;YAC9C,UAAU,OAAO,GAAG;QACtB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,oBAAoB,CAAC,OAAuB;QAChD,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAChD,sCAAsC;QACtC,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAU,CAAC;QACpD;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,gBAAgB;QAChB,gBAAgB;QAEhB,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,iEAAiE;YACjE,QAAQ,GAAG,CAAC,mBAAmB;YAE/B,gBAAgB;YAChB,YAAY;gBACV,MAAM;gBACN,OAAO;gBACP,OAAO;gBACP,SAAS;gBACT,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,gBAAgB;QAClB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;0CAGvD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;0DACtC,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;0DACtC,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;0DACtC,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;0CACC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;;8DACT,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,8OAAC;sEAAK;;;;;;sEACN,8OAAC;sEAAK;;;;;;;;;;;;8DAER,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAIvC,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAK,UAAU;gDAAc,WAAU;;kEAEtC,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAO,WAAU;0EAAiC;;;;;;0EAGjE,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,OAAO,SAAS,IAAI;gEACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;gEACzD,WAAW,CAAC,sFAAsF,EAChG,OAAO,IAAI,GAAG,mBAAmB,gBACjC;gEACF,aAAY;;;;;;4DAEb,OAAO,IAAI,kBACV,8OAAC;gEAAE,WAAU;0EAA6B,OAAO,IAAI;;;;;;;;;;;;kEAKzD,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAQ,WAAU;0EAAiC;;;;;;0EAGlE,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,OAAO,SAAS,KAAK;gEACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;gEAC1D,WAAW,CAAC,sFAAsF,EAChG,OAAO,KAAK,GAAG,mBAAmB,gBAClC;gEACF,aAAY;;;;;;4DAEb,OAAO,KAAK,kBACX,8OAAC;gEAAE,WAAU;0EAA6B,OAAO,KAAK;;;;;;;;;;;;kEAK1D,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAQ,WAAU;0EAAiC;;;;;;0EAGlE,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,OAAO,SAAS,KAAK;gEACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;gEAC1D,WAAW,CAAC,sFAAsF,EAChG,OAAO,KAAK,GAAG,mBAAmB,gBAClC;gEACF,aAAY;;;;;;4DAEb,OAAO,KAAK,kBACX,8OAAC;gEAAE,WAAU;0EAA6B,OAAO,KAAK;;;;;;;;;;;;kEAK1D,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAU,WAAU;0EAAiC;;;;;;0EAGpE,8OAAC;gEACC,IAAG;gEACH,OAAO,SAAS,OAAO;gEACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;gEAC5D,WAAW,CAAC,sFAAsF,EAChG,OAAO,OAAO,GAAG,mBAAmB,gBACpC;0EAED,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;wEAA2B,OAAO,QAAQ,KAAK;kFAC7C,QAAQ,KAAK;uEADH,QAAQ,KAAK;;;;;;;;;;4DAK7B,OAAO,OAAO,kBACb,8OAAC;gEAAE,WAAU;0EAA6B,OAAO,OAAO;;;;;;;;;;;;kEAK5D,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAU,WAAU;0EAAiC;;;;;;0EAGpE,8OAAC;gEACC,IAAG;gEACH,MAAM;gEACN,OAAO,SAAS,OAAO;gEACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;gEAC5D,WAAW,CAAC,kGAAkG,EAC5G,OAAO,OAAO,GAAG,mBAAmB,gBACpC;gEACF,aAAY;;;;;;4DAEb,OAAO,OAAO,kBACb,8OAAC;gEAAE,WAAU;0EAA6B,OAAO,OAAO;;;;;;;;;;;;kEAK5D,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,UAAU;wDACV,WAAU;kEAET,6BACC,8OAAC;4DAAK,WAAU;;8EACd,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;;;;;;iFAGR,8OAAC;4DAAK,WAAU;;8EACd,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;;;;;;;;;;;;oDAMX,iBAAiB,2BAChB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;;;;;;;;;;;;oDAKX,iBAAiB,yBAChB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAUpB,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;0DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,8OAAC;sEAAK;;;;;;sEACN,8OAAC;sEAAK;;;;;;;;;;;;;;;;;0DAGV,8OAAC,gIAAA,CAAA,cAAW;;kEACV,8OAAC;wDAAI,WAAU;kEACZ,cAAc,GAAG,CAAC,CAAC,yBAClB,8OAAC;gEAAuB,WAAU;;kFAChC,8OAAC;wEAAK,WAAU;kFAAe,SAAS,GAAG;;;;;;kFAC3C,8OAAC;wEAAK,WAAW,GAAG,SAAS,MAAM,GAAG,mBAAmB,gBAAgB;kFACtE,SAAS,KAAK;;;;;;;+DAHT,SAAS,GAAG;;;;;;;;;;kEAQ1B,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;;;;;;kDAQnD,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;0DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,8OAAC;sEAAK;;;;;;sEACN,8OAAC;sEAAK;;;;;;;;;;;;;;;;;0DAGV,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAU;;;;;;0EAC1B,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAAc;;;;;;kFAC3B,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAGzC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAU;;;;;;0EAC1B,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAAc;;;;;;kFAC3B,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;kEAGzC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAU;;;;;;0EAC1B,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAAc;;;;;;kFAC3B,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAO7C,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;0DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,8OAAC;sEAAK;;;;;;sEACN,8OAAC;sEAAK;;;;;;;;;;;;;;;;;0DAGV,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAmB;;;;;;0EAChC,8OAAC;gEAAE,WAAU;;oEAA6B;kFAC1B,8OAAC;;;;;oEAAK;;;;;;;;;;;;;kEAMxB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAgB;;;;;;8EAC/B,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,8OAAC;oEAAE,WAAU;8EAAqC;;;;;;;;;;;;;;;;;kEAOtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAc;;;;;;0EAC5B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;kFAAE;;;;;;kFACH,8OAAC;kFAAE;;;;;;kFACH,8OAAC;kFAAE;;;;;;;;;;;;;;;;;;kEAKP,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAc;;;;;;0EAC5B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;kFAAE;;;;;;kFACH,8OAAC;kFAAE;;;;;;kFACH,8OAAC;kFAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWnB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;0CAGlD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAC,MAAK;wCAAK,WAAU;kDAClC,cAAA,8OAAC;4CAAE,MAAK;sDAAW;;;;;;;;;;;kDAErB,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDACpD,cAAA,8OAAC;4CAAE,MAAK;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7C", "debugId": null}}]}