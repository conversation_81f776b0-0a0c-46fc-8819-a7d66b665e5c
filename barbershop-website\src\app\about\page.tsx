import { Metadata } from "next"
import Image from "next/image"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export const metadata: Metadata = {
  title: "关于我们 - Classic Cuts Barbershop",
  description: "了解Classic Cuts Barbershop的历史、团队和价值观。我们致力于传承经典理发工艺，为每位客户提供专业的男士美容服务。",
  keywords: "理发店历史,专业理发师,男士美容,传统理发工艺,团队介绍",
}

const stats = [
  {
    icon: "🏆",
    number: "15+",
    label: "年专业经验",
    description: "传承经典理发工艺"
  },
  {
    icon: "👥",
    number: "10000+",
    label: "满意客户",
    description: "口碑见证品质"
  },
  {
    icon: "⭐",
    number: "4.9",
    label: "客户评分",
    description: "五星级服务标准"
  },
  {
    icon: "🏅",
    number: "50+",
    label: "行业奖项",
    description: "专业技能认可"
  }
]

const team = [
  {
    name: "张师傅",
    position: "首席理发师",
    experience: "20年经验",
    specialty: "经典油头、商务造型",
    description: "拥有20年丰富经验的资深理发师，擅长经典油头和商务造型，曾获得多项行业大奖。",
    avatar: "👨‍💼"
  },
  {
    name: "李师傅",
    position: "高级理发师",
    experience: "15年经验",
    specialty: "时尚造型、胡须设计",
    description: "专注于时尚造型和胡须设计，善于根据客户脸型打造个性化发型。",
    avatar: "👨‍🎨"
  },
  {
    name: "王师傅",
    position: "资深理发师",
    experience: "12年经验",
    specialty: "渐变技术、精细修剪",
    description: "精通各种渐变技术，注重细节，每一次修剪都力求完美。",
    avatar: "👨‍🔧"
  },
  {
    name: "陈师傅",
    position: "造型师",
    experience: "8年经验",
    specialty: "创意造型、色彩搭配",
    description: "年轻有活力的造型师，擅长创意造型和色彩搭配，深受年轻客户喜爱。",
    avatar: "👨‍🎤"
  }
]

const values = [
  {
    icon: "🎯",
    title: "专业至上",
    description: "我们坚持使用最专业的技术和工具，确保每一次服务都达到最高标准。"
  },
  {
    icon: "❤️",
    title: "用心服务",
    description: "每位客户都是独一无二的，我们用心倾听需求，量身定制最适合的造型。"
  },
  {
    icon: "🌟",
    title: "追求卓越",
    description: "不断学习新技术，追求完美的服务体验，让每位客户都满意而归。"
  },
  {
    icon: "🤝",
    title: "诚信经营",
    description: "以诚待客，公平定价，建立长期的信任关系，成为客户的首选理发店。"
  }
]

const milestones = [
  {
    year: "2008",
    title: "创立之初",
    description: "Classic Cuts Barbershop在市中心开设第一家店铺，开始我们的理发之旅。"
  },
  {
    year: "2012",
    title: "技术革新",
    description: "引进国际先进理发技术和设备，提升服务质量和客户体验。"
  },
  {
    year: "2016",
    title: "团队扩大",
    description: "招募更多专业理发师，形成了经验丰富的专业团队。"
  },
  {
    year: "2020",
    title: "数字化转型",
    description: "推出在线预约系统，让客户享受更便捷的服务体验。"
  },
  {
    year: "2024",
    title: "持续发展",
    description: "继续秉承专业精神，为更多客户提供优质的理发服务。"
  }
]

export default function AboutPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-primary to-primary/80 text-primary-foreground">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              关于 Classic Cuts
            </h1>
            <p className="text-xl mb-8 text-primary-foreground/90">
              传承经典理发工艺，打造专业男士美容体验。我们不仅仅是一家理发店，更是您形象管理的专业伙伴。
            </p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-4xl mb-2">{stat.icon}</div>
                  <div className="text-3xl font-bold text-accent mb-1">{stat.number}</div>
                  <div className="text-lg font-semibold mb-1">{stat.label}</div>
                  <div className="text-sm text-primary-foreground/80">{stat.description}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Story Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                我们的故事
              </h2>
              <p className="text-lg text-muted-foreground">
                从一个小小的理发店到专业的男士美容中心，这是我们的成长历程
              </p>
            </div>

            <div className="prose prose-lg max-w-none text-center mb-16">
              <p className="text-lg leading-relaxed mb-6">
                Classic Cuts Barbershop成立于2008年，由一群热爱理发艺术的专业人士创立。
                我们的初心很简单：为每一位走进店里的客户提供最专业、最贴心的理发服务。
              </p>
              <p className="text-lg leading-relaxed mb-6">
                十五年来，我们始终坚持传承经典理发工艺，同时不断学习和引进现代技术。
                我们相信，真正的理发艺术在于对细节的把控和对客户需求的深度理解。
              </p>
              <p className="text-lg leading-relaxed">
                今天的Classic Cuts不仅是一家理发店，更是一个让男士们放松身心、
                提升形象的专业空间。我们为能够成为您形象管理路上的伙伴而感到自豪。
              </p>
            </div>

            {/* Timeline */}
            <div className="space-y-8">
              <h3 className="text-2xl font-bold text-center mb-12">发展历程</h3>
              <div className="relative">
                <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-primary/20"></div>
                {milestones.map((milestone, index) => (
                  <div key={index} className={`flex items-center mb-8 ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}>
                    <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                      <Card className="hover:shadow-lg transition-all duration-300">
                        <CardHeader>
                          <CardTitle className="text-xl">{milestone.title}</CardTitle>
                          <CardDescription className="text-primary font-semibold">
                            {milestone.year}
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-muted-foreground">{milestone.description}</p>
                        </CardContent>
                      </Card>
                    </div>
                    <div className="relative z-10 w-4 h-4 bg-primary rounded-full border-4 border-background shadow-lg"></div>
                    <div className="w-1/2"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              专业团队
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              我们的理发师团队拥有丰富的经验和精湛的技艺，致力于为每位客户提供个性化的专业服务
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {team.map((member, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-all duration-300">
                <CardHeader>
                  <div className="mx-auto mb-4 text-6xl">{member.avatar}</div>
                  <CardTitle className="text-xl">{member.name}</CardTitle>
                  <CardDescription className="text-primary font-semibold">
                    {member.position}
                  </CardDescription>
                  <div className="text-sm text-muted-foreground">
                    {member.experience}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="mb-3">
                    <div className="text-sm font-medium text-accent mb-1">专长领域</div>
                    <div className="text-sm text-muted-foreground">{member.specialty}</div>
                  </div>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    {member.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              我们的价值观
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              这些核心价值观指导着我们的每一项服务，确保为客户提供最优质的体验
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {values.map((value, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-all duration-300">
                <CardHeader>
                  <div className="mx-auto mb-4 p-4 bg-primary/10 rounded-full w-fit">
                    <span className="text-4xl">{value.icon}</span>
                  </div>
                  <CardTitle className="text-xl">{value.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground leading-relaxed">
                    {value.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    </div>
  )
}
