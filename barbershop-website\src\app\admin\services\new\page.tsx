"use client"

import { <PERSON><PERSON>L<PERSON>out, PageContainer, CardContainer } from '@/components/admin/layout/admin-layout'
import { ServiceForm } from '@/components/admin/services/service-form'
import { Plus } from 'lucide-react'

export default function NewServicePage() {
  return (
    <AdminLayout>
      <PageContainer
        title="新增服务"
        description="添加新的服务项目"
      >
        <CardContainer
          title="服务信息"
          description="请填写服务的基本信息、价格和时长设置"
        >
          <ServiceForm />
        </CardContainer>
      </PageContainer>
    </AdminLayout>
  )
}
