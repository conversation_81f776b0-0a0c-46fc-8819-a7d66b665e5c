"use client"

import { <PERSON><PERSON><PERSON><PERSON>out, PageContainer, CardContainer } from '@/components/admin/layout/admin-layout'
import { StaffForm } from '@/components/admin/staff/staff-form'
import { UserPlus } from 'lucide-react'

export default function NewStaffPage() {
  return (
    <AdminLayout>
      <PageContainer
        title="新增员工"
        description="添加新的员工信息"
      >
        <CardContainer
          title="员工信息"
          description="请填写员工的基本信息、专业技能和工作时间"
        >
          <StaffForm />
        </CardContainer>
      </PageContainer>
    </AdminLayout>
  )
}
