"use client"

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/admin/ui/badge'
import { 
  LayoutDashboard, 
  Calendar, 
  Users, 
  Scissors, 
  UserCheck, 
  BarChart3,
  Menu,
  X,
  LogOut,
  Settings
} from 'lucide-react'
import { authService } from '@/lib/admin/auth'

interface NavItem {
  id: string
  label: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  badge?: number
  description?: string
}

const navItems: NavItem[] = [
  {
    id: 'dashboard',
    label: '仪表板',
    href: '/admin',
    icon: LayoutDashboard,
    description: '总览和快速操作'
  },
  {
    id: 'appointments',
    label: '预约管理',
    href: '/admin/appointments',
    icon: Calendar,
    description: '查看和管理客户预约'
  },
  {
    id: 'customers',
    label: '客户管理',
    href: '/admin/customers',
    icon: Users,
    description: '客户信息和服务历史'
  },
  {
    id: 'services',
    label: '服务项目',
    href: '/admin/services',
    icon: Scissors,
    description: '管理服务项目和价格'
  },
  {
    id: 'staff',
    label: '员工管理',
    href: '/admin/staff',
    icon: UserCheck,
    description: '员工信息和工作安排'
  },
  {
    id: 'analytics',
    label: '数据统计',
    href: '/admin/analytics',
    icon: BarChart3,
    description: '营业数据和分析报告'
  }
]

interface SidebarProps {
  isOpen: boolean
  onToggle: () => void
  className?: string
}

export function Sidebar({ isOpen, onToggle, className }: SidebarProps) {
  const pathname = usePathname()

  const handleLogout = () => {
    authService.logout()
    window.location.href = '/admin/login'
  }

  return (
    <>
      {/* 移动端遮罩 */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={onToggle}
        />
      )}

      {/* 侧边栏 */}
      <aside
        className={cn(
          "fixed left-0 top-0 z-50 h-full w-64 bg-card border-r border-border transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:z-auto",
          isOpen ? "translate-x-0" : "-translate-x-full",
          className
        )}
      >
        <div className="flex h-full flex-col">
          {/* 头部 */}
          <div className="flex items-center justify-between p-4 border-b border-border">
            <div className="flex items-center space-x-2">
              <span className="text-2xl">✂️</span>
              <div>
                <h2 className="font-bold text-foreground">Tony's Barbershop</h2>
                <p className="text-xs text-muted-foreground">管理后台</p>
              </div>
            </div>
            <button
              onClick={onToggle}
              className="lg:hidden p-1 rounded-md hover:bg-accent"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* 导航菜单 */}
          <nav className="flex-1 p-4 space-y-2">
            {navItems.map((item) => {
              const Icon = item.icon
              const isActive = pathname === item.href || 
                (item.href !== '/admin' && pathname.startsWith(item.href))

              return (
                <Link
                  key={item.id}
                  href={item.href}
                  onClick={() => {
                    // 移动端点击后关闭侧边栏
                    if (window.innerWidth < 1024) {
                      onToggle()
                    }
                  }}
                  className={cn(
                    "flex items-center space-x-3 px-3 py-2.5 rounded-lg text-sm font-medium transition-colors group",
                    isActive
                      ? "bg-primary text-primary-foreground"
                      : "text-muted-foreground hover:text-foreground hover:bg-accent"
                  )}
                >
                  <Icon className={cn(
                    "h-5 w-5 transition-colors",
                    isActive ? "text-primary-foreground" : "text-muted-foreground group-hover:text-foreground"
                  )} />
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <span>{item.label}</span>
                      {item.badge && (
                        <Badge variant="secondary" size="sm">
                          {item.badge}
                        </Badge>
                      )}
                    </div>
                    {item.description && (
                      <p className={cn(
                        "text-xs mt-0.5",
                        isActive ? "text-primary-foreground/80" : "text-muted-foreground"
                      )}>
                        {item.description}
                      </p>
                    )}
                  </div>
                </Link>
              )
            })}
          </nav>

          {/* 底部操作 */}
          <div className="p-4 border-t border-border space-y-2">
            <Link
              href="/admin/settings"
              className="flex items-center space-x-3 px-3 py-2.5 rounded-lg text-sm font-medium text-muted-foreground hover:text-foreground hover:bg-accent transition-colors"
            >
              <Settings className="h-5 w-5" />
              <span>系统设置</span>
            </Link>
            
            <button
              onClick={handleLogout}
              className="w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-sm font-medium text-muted-foreground hover:text-foreground hover:bg-accent transition-colors"
            >
              <LogOut className="h-5 w-5" />
              <span>退出登录</span>
            </button>
          </div>
        </div>
      </aside>
    </>
  )
}

// 移动端菜单按钮
export function MobileMenuButton({ onClick }: { onClick: () => void }) {
  return (
    <button
      onClick={onClick}
      className="lg:hidden p-2 rounded-md hover:bg-accent"
    >
      <Menu className="h-5 w-5" />
    </button>
  )
}
