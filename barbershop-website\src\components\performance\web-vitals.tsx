'use client'

import { useEffect } from 'react'
import { onCLS, onINP, onFCP, onLCP, onTTFB } from 'web-vitals'

// Web Vitals 指标类型
interface Metric {
  name: string
  value: number
  rating: 'good' | 'needs-improvement' | 'poor'
  delta: number
  id: string
}

// 发送指标到分析服务
function sendToAnalytics(metric: Metric) {
  // 这里可以发送到 Google Analytics, Vercel Analytics 等
  console.log('Web Vitals:', metric)
  
  // 示例：发送到 Google Analytics
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('event', metric.name, {
      event_category: 'Web Vitals',
      event_label: metric.id,
      value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
      non_interaction: true,
    })
  }
}

// Web Vitals 监控组件
export function WebVitals() {
  useEffect(() => {
    // 累积布局偏移 (Cumulative Layout Shift)
    onCLS(sendToAnalytics)

    // 交互到下次绘制 (Interaction to Next Paint)
    onINP(sendToAnalytics)

    // 首次内容绘制 (First Contentful Paint)
    onFCP(sendToAnalytics)

    // 最大内容绘制 (Largest Contentful Paint)
    onLCP(sendToAnalytics)

    // 首字节时间 (Time to First Byte)
    onTTFB(sendToAnalytics)
  }, [])

  return null
}

// 性能监控 Hook
export function usePerformanceMonitoring() {
  useEffect(() => {
    // 监控页面加载性能
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'navigation') {
          const navEntry = entry as PerformanceNavigationTiming
          console.log('Navigation Timing:', {
            domContentLoaded: navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart,
            loadComplete: navEntry.loadEventEnd - navEntry.loadEventStart,
            firstByte: navEntry.responseStart - navEntry.requestStart,
            domInteractive: navEntry.domInteractive - navEntry.navigationStart,
          })
        }
        
        if (entry.entryType === 'resource') {
          const resourceEntry = entry as PerformanceResourceTiming
          // 监控慢资源
          if (resourceEntry.duration > 1000) {
            console.warn('Slow resource:', {
              name: resourceEntry.name,
              duration: resourceEntry.duration,
              size: resourceEntry.transferSize,
            })
          }
        }
      }
    })

    observer.observe({ entryTypes: ['navigation', 'resource'] })

    return () => observer.disconnect()
  }, [])

  // 监控内存使用
  useEffect(() => {
    const checkMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory
        console.log('Memory Usage:', {
          used: Math.round(memory.usedJSHeapSize / 1048576) + ' MB',
          total: Math.round(memory.totalJSHeapSize / 1048576) + ' MB',
          limit: Math.round(memory.jsHeapSizeLimit / 1048576) + ' MB',
        })
      }
    }

    const interval = setInterval(checkMemory, 30000) // 每30秒检查一次
    return () => clearInterval(interval)
  }, [])
}

// 图片懒加载监控
export function useImageLoadingMonitoring() {
  useEffect(() => {
    const images = document.querySelectorAll('img[loading="lazy"]')
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement
          const startTime = performance.now()
          
          img.addEventListener('load', () => {
            const loadTime = performance.now() - startTime
            console.log('Image loaded:', {
              src: img.src,
              loadTime: Math.round(loadTime),
              naturalWidth: img.naturalWidth,
              naturalHeight: img.naturalHeight,
            })
          })
          
          observer.unobserve(img)
        }
      })
    })

    images.forEach((img) => observer.observe(img))

    return () => observer.disconnect()
  }, [])
}

// 错误监控
export function useErrorMonitoring() {
  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      console.error('JavaScript Error:', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error,
      })
      
      // 发送错误到监控服务
      // sendErrorToService(event)
    }

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('Unhandled Promise Rejection:', event.reason)
      
      // 发送错误到监控服务
      // sendErrorToService(event)
    }

    window.addEventListener('error', handleError)
    window.addEventListener('unhandledrejection', handleUnhandledRejection)

    return () => {
      window.removeEventListener('error', handleError)
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
    }
  }, [])
}

// 用户体验监控
export function useUserExperienceMonitoring() {
  useEffect(() => {
    // 监控页面可见性变化
    const handleVisibilityChange = () => {
      console.log('Page visibility changed:', document.visibilityState)
    }

    // 监控网络状态变化
    const handleOnline = () => console.log('Network: Online')
    const handleOffline = () => console.log('Network: Offline')

    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])
}

// 综合性能监控组件
export function PerformanceMonitor() {
  usePerformanceMonitoring()
  useImageLoadingMonitoring()
  useErrorMonitoring()
  useUserExperienceMonitoring()

  return <WebVitals />
}
