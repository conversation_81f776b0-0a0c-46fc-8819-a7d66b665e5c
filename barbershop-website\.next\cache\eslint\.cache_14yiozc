[{"C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\analytics\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\appointments\\calendar\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\appointments\\new\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\appointments\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\appointments\\[id]\\edit\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\appointments\\[id]\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\customers\\new\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\customers\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\customers\\[id]\\edit\\page.tsx": "10", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\customers\\[id]\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\layout.tsx": "12", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\login\\page.tsx": "13", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\page.tsx": "14", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\services\\categories\\page.tsx": "15", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\services\\new\\page.tsx": "16", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\services\\page.tsx": "17", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\services\\[id]\\edit\\page.tsx": "18", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\services\\[id]\\page.tsx": "19", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\staff\\new\\page.tsx": "20", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\staff\\page.tsx": "21", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\staff\\[id]\\edit\\page.tsx": "22", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\staff\\[id]\\page.tsx": "23", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\booking\\page.tsx": "24", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\contact\\page.tsx": "25", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\gallery\\page.tsx": "26", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\layout.tsx": "27", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\page.tsx": "28", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\services\\page.tsx": "29", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\accessibility\\skip-links.tsx": "30", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\admin\\appointments\\appointment-form.tsx": "31", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\admin\\appointments\\calendar-view.tsx": "32", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\admin\\auth\\auth-guard.tsx": "33", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\admin\\customers\\customer-form.tsx": "34", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\admin\\layout\\admin-layout.tsx": "35", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\admin\\layout\\breadcrumb.tsx": "36", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\admin\\layout\\header.tsx": "37", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\admin\\layout\\sidebar.tsx": "38", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\admin\\services\\service-form.tsx": "39", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\admin\\staff\\staff-form.tsx": "40", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\admin\\ui\\badge.tsx": "41", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\admin\\ui\\data-table.tsx": "42", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\animations\\fade-in.tsx": "43", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\animations\\page-transition.tsx": "44", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\layout\\footer.tsx": "45", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\layout\\main-layout.tsx": "46", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\layout\\navbar.tsx": "47", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\pages\\about-page-content.tsx": "48", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\pages\\booking-page-content.tsx": "49", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\pages\\contact-page-content.tsx": "50", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\pages\\gallery-page-content.tsx": "51", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\pages\\services-page-content.tsx": "52", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\performance\\web-vitals.tsx": "53", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\sections\\about-preview.tsx": "54", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\sections\\cta-section.tsx": "55", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\sections\\hero-section.tsx": "56", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\sections\\services-preview.tsx": "57", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\sections\\testimonials-section.tsx": "58", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\seo\\meta-tags.tsx": "59", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\seo\\structured-data.tsx": "60", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\ui\\button.tsx": "61", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\ui\\card.tsx": "62", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\ui\\input.tsx": "63", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\ui\\optimized-image.tsx": "64", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\ui\\textarea.tsx": "65", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\lib\\admin\\auth.ts": "66", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\lib\\admin\\storage.ts": "67", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\lib\\types\\admin.ts": "68", "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\lib\\utils.ts": "69"}, {"size": 601, "mtime": 1751007230134, "results": "70", "hashOfConfig": "71"}, {"size": 18779, "mtime": 1751085242103, "results": "72", "hashOfConfig": "71"}, {"size": 1439, "mtime": 1751085621152, "results": "73", "hashOfConfig": "71"}, {"size": 647, "mtime": 1751085694765, "results": "74", "hashOfConfig": "71"}, {"size": 10629, "mtime": 1751085803182, "results": "75", "hashOfConfig": "71"}, {"size": 2082, "mtime": 1751085327003, "results": "76", "hashOfConfig": "71"}, {"size": 14640, "mtime": 1751085586551, "results": "77", "hashOfConfig": "71"}, {"size": 640, "mtime": 1751086291410, "results": "78", "hashOfConfig": "71"}, {"size": 12663, "mtime": 1751086328658, "results": "79", "hashOfConfig": "71"}, {"size": 2032, "mtime": 1751085900639, "results": "80", "hashOfConfig": "71"}, {"size": 14986, "mtime": 1751086207745, "results": "81", "hashOfConfig": "71"}, {"size": 414, "mtime": 1751078222031, "results": "82", "hashOfConfig": "71"}, {"size": 4611, "mtime": 1751078010839, "results": "83", "hashOfConfig": "71"}, {"size": 8601, "mtime": 1751081892208, "results": "84", "hashOfConfig": "71"}, {"size": 12512, "mtime": 1751086975878, "results": "85", "hashOfConfig": "71"}, {"size": 640, "mtime": 1751087447525, "results": "86", "hashOfConfig": "71"}, {"size": 11820, "mtime": 1751087487393, "results": "87", "hashOfConfig": "71"}, {"size": 2022, "mtime": 1751086497536, "results": "88", "hashOfConfig": "71"}, {"size": 13733, "mtime": 1751086856219, "results": "89", "hashOfConfig": "71"}, {"size": 639, "mtime": 1751088037066, "results": "90", "hashOfConfig": "71"}, {"size": 13198, "mtime": 1751084505287, "results": "91", "hashOfConfig": "71"}, {"size": 1989, "mtime": 1751087662800, "results": "92", "hashOfConfig": "71"}, {"size": 15238, "mtime": 1751087956581, "results": "93", "hashOfConfig": "71"}, {"size": 612, "mtime": 1751008074495, "results": "94", "hashOfConfig": "71"}, {"size": 612, "mtime": 1751008056369, "results": "95", "hashOfConfig": "71"}, {"size": 615, "mtime": 1751007491557, "results": "96", "hashOfConfig": "71"}, {"size": 2452, "mtime": 1751007137197, "results": "97", "hashOfConfig": "71"}, {"size": 842, "mtime": 1751007172533, "results": "98", "hashOfConfig": "71"}, {"size": 678, "mtime": 1751007207799, "results": "99", "hashOfConfig": "71"}, {"size": 6022, "mtime": 1751007046818, "results": "100", "hashOfConfig": "71"}, {"size": 17071, "mtime": 1751088175842, "results": "101", "hashOfConfig": "71"}, {"size": 12175, "mtime": 1751081771075, "results": "102", "hashOfConfig": "71"}, {"size": 3919, "mtime": 1751088241324, "results": "103", "hashOfConfig": "71"}, {"size": 10131, "mtime": 1751088570349, "results": "104", "hashOfConfig": "71"}, {"size": 4212, "mtime": 1751078211572, "results": "105", "hashOfConfig": "71"}, {"size": 3210, "mtime": 1751078187088, "results": "106", "hashOfConfig": "71"}, {"size": 7481, "mtime": 1751078164064, "results": "107", "hashOfConfig": "71"}, {"size": 6201, "mtime": 1751078130257, "results": "108", "hashOfConfig": "71"}, {"size": 11854, "mtime": 1751088941539, "results": "109", "hashOfConfig": "71"}, {"size": 16516, "mtime": 1751089006929, "results": "110", "hashOfConfig": "71"}, {"size": 1465, "mtime": 1751078054701, "results": "111", "hashOfConfig": "71"}, {"size": 10105, "mtime": 1751078380169, "results": "112", "hashOfConfig": "71"}, {"size": 7331, "mtime": 1750951635750, "results": "113", "hashOfConfig": "71"}, {"size": 7375, "mtime": 1750951672231, "results": "114", "hashOfConfig": "71"}, {"size": 5648, "mtime": 1751010481801, "results": "115", "hashOfConfig": "71"}, {"size": 364, "mtime": 1750929120327, "results": "116", "hashOfConfig": "71"}, {"size": 3831, "mtime": 1751010333700, "results": "117", "hashOfConfig": "71"}, {"size": 14927, "mtime": 1750953139611, "results": "118", "hashOfConfig": "71"}, {"size": 23434, "mtime": 1751008642675, "results": "119", "hashOfConfig": "71"}, {"size": 20054, "mtime": 1751009009995, "results": "120", "hashOfConfig": "71"}, {"size": 14040, "mtime": 1751011869710, "results": "121", "hashOfConfig": "71"}, {"size": 10555, "mtime": 1750952548255, "results": "122", "hashOfConfig": "71"}, {"size": 6035, "mtime": 1751089125102, "results": "123", "hashOfConfig": "71"}, {"size": 4360, "mtime": 1751006759820, "results": "124", "hashOfConfig": "71"}, {"size": 3679, "mtime": 1750953043429, "results": "125", "hashOfConfig": "71"}, {"size": 5266, "mtime": 1751011090857, "results": "126", "hashOfConfig": "71"}, {"size": 3854, "mtime": 1751011043179, "results": "127", "hashOfConfig": "71"}, {"size": 4996, "mtime": 1751006546230, "results": "128", "hashOfConfig": "71"}, {"size": 5036, "mtime": 1751007016363, "results": "129", "hashOfConfig": "71"}, {"size": 5311, "mtime": 1751006984742, "results": "130", "hashOfConfig": "71"}, {"size": 3601, "mtime": 1750951794950, "results": "131", "hashOfConfig": "71"}, {"size": 2526, "mtime": 1750951843401, "results": "132", "hashOfConfig": "71"}, {"size": 824, "mtime": 1750929132136, "results": "133", "hashOfConfig": "71"}, {"size": 2862, "mtime": 1751006953929, "results": "134", "hashOfConfig": "71"}, {"size": 772, "mtime": 1750929144515, "results": "135", "hashOfConfig": "71"}, {"size": 9124, "mtime": 1751077979053, "results": "136", "hashOfConfig": "71"}, {"size": 17718, "mtime": 1751084785450, "results": "137", "hashOfConfig": "71"}, {"size": 6238, "mtime": 1751084968295, "results": "138", "hashOfConfig": "71"}, {"size": 619, "mtime": 1750929980254, "results": "139", "hashOfConfig": "71"}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1nlce79", {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\analytics\\page.tsx", ["347", "348", "349", "350"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\appointments\\calendar\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\appointments\\new\\page.tsx", ["351"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\appointments\\page.tsx", ["352", "353", "354", "355", "356"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\appointments\\[id]\\edit\\page.tsx", ["357"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\appointments\\[id]\\page.tsx", ["358", "359", "360"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\customers\\new\\page.tsx", ["361"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\customers\\page.tsx", ["362", "363", "364", "365", "366", "367"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\customers\\[id]\\edit\\page.tsx", ["368"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\customers\\[id]\\page.tsx", ["369", "370", "371", "372", "373", "374"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\login\\page.tsx", ["375", "376", "377"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\page.tsx", ["378"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\services\\categories\\page.tsx", ["379"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\services\\new\\page.tsx", ["380"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\services\\page.tsx", ["381", "382"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\services\\[id]\\edit\\page.tsx", ["383"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\services\\[id]\\page.tsx", ["384", "385", "386", "387"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\staff\\new\\page.tsx", ["388"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\staff\\page.tsx", ["389", "390", "391"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\staff\\[id]\\edit\\page.tsx", ["392"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\admin\\staff\\[id]\\page.tsx", ["393", "394", "395"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\booking\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\contact\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\gallery\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\app\\services\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\accessibility\\skip-links.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\admin\\appointments\\appointment-form.tsx", ["396", "397", "398"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\admin\\appointments\\calendar-view.tsx", ["399"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\admin\\auth\\auth-guard.tsx", ["400"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\admin\\customers\\customer-form.tsx", ["401", "402"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\admin\\layout\\admin-layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\admin\\layout\\breadcrumb.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\admin\\layout\\header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\admin\\layout\\sidebar.tsx", ["403", "404"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\admin\\services\\service-form.tsx", ["405", "406", "407"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\admin\\staff\\staff-form.tsx", ["408", "409", "410", "411"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\admin\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\admin\\ui\\data-table.tsx", ["412", "413", "414", "415"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\animations\\fade-in.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\animations\\page-transition.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\layout\\footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\layout\\main-layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\layout\\navbar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\pages\\about-page-content.tsx", ["416", "417", "418"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\pages\\booking-page-content.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\pages\\contact-page-content.tsx", ["419"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\pages\\gallery-page-content.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\pages\\services-page-content.tsx", ["420"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\performance\\web-vitals.tsx", ["421", "422", "423"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\sections\\about-preview.tsx", ["424"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\sections\\cta-section.tsx", ["425"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\sections\\hero-section.tsx", ["426", "427", "428", "429"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\sections\\services-preview.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\sections\\testimonials-section.tsx", ["430", "431"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\seo\\meta-tags.tsx", ["432"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\seo\\structured-data.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\ui\\input.tsx", ["433"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\ui\\optimized-image.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\components\\ui\\textarea.tsx", ["434"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\lib\\admin\\auth.ts", ["435"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\lib\\admin\\storage.ts", ["436", "437", "438"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\lib\\types\\admin.ts", ["439"], [], "C:\\Users\\<USER>\\Desktop\\python_study\\tony_project\\barbershop-website\\src\\lib\\utils.ts", [], [], {"ruleId": "440", "severity": 1, "message": "441", "line": 7, "column": 3, "nodeType": null, "messageId": "442", "endLine": 7, "endColumn": 12}, {"ruleId": "440", "severity": 1, "message": "443", "line": 13, "column": 3, "nodeType": null, "messageId": "442", "endLine": 13, "endColumn": 11}, {"ruleId": "444", "severity": 1, "message": "445", "line": 68, "column": 6, "nodeType": "446", "endLine": 68, "endColumn": 17, "suggestions": "447"}, {"ruleId": "448", "severity": 1, "message": "449", "line": 381, "column": 64, "nodeType": "450", "messageId": "451", "endLine": 381, "endColumn": 67, "suggestions": "452"}, {"ruleId": "440", "severity": 1, "message": "453", "line": 5, "column": 10, "nodeType": null, "messageId": "442", "endLine": 5, "endColumn": 22}, {"ruleId": "440", "severity": 1, "message": "454", "line": 12, "column": 3, "nodeType": null, "messageId": "442", "endLine": 12, "endColumn": 8}, {"ruleId": "440", "severity": 1, "message": "455", "line": 14, "column": 3, "nodeType": null, "messageId": "442", "endLine": 14, "endColumn": 8}, {"ruleId": "440", "severity": 1, "message": "456", "line": 15, "column": 3, "nodeType": null, "messageId": "442", "endLine": 15, "endColumn": 17}, {"ruleId": "444", "severity": 1, "message": "457", "line": 37, "column": 6, "nodeType": "446", "endLine": 37, "endColumn": 47, "suggestions": "458"}, {"ruleId": "440", "severity": 1, "message": "459", "line": 190, "column": 9, "nodeType": null, "messageId": "442", "endLine": 190, "endColumn": 24}, {"ruleId": "440", "severity": 1, "message": "460", "line": 7, "column": 10, "nodeType": null, "messageId": "442", "endLine": 7, "endColumn": 14}, {"ruleId": "440", "severity": 1, "message": "461", "line": 15, "column": 3, "nodeType": null, "messageId": "442", "endLine": 15, "endColumn": 9}, {"ruleId": "440", "severity": 1, "message": "462", "line": 20, "column": 3, "nodeType": null, "messageId": "442", "endLine": 20, "endColumn": 12}, {"ruleId": "440", "severity": 1, "message": "463", "line": 21, "column": 3, "nodeType": null, "messageId": "442", "endLine": 21, "endColumn": 16}, {"ruleId": "440", "severity": 1, "message": "464", "line": 5, "column": 10, "nodeType": null, "messageId": "442", "endLine": 5, "endColumn": 18}, {"ruleId": "440", "severity": 1, "message": "465", "line": 15, "column": 3, "nodeType": null, "messageId": "442", "endLine": 15, "endColumn": 13}, {"ruleId": "440", "severity": 1, "message": "456", "line": 16, "column": 3, "nodeType": null, "messageId": "442", "endLine": 16, "endColumn": 17}, {"ruleId": "440", "severity": 1, "message": "466", "line": 20, "column": 3, "nodeType": null, "messageId": "442", "endLine": 20, "endColumn": 7}, {"ruleId": "444", "severity": 1, "message": "467", "line": 37, "column": 6, "nodeType": "446", "endLine": 37, "endColumn": 47, "suggestions": "468"}, {"ruleId": "440", "severity": 1, "message": "469", "line": 170, "column": 31, "nodeType": null, "messageId": "442", "endLine": 170, "endColumn": 37}, {"ruleId": "448", "severity": 1, "message": "449", "line": 213, "column": 23, "nodeType": "450", "messageId": "451", "endLine": 213, "endColumn": 26, "suggestions": "470"}, {"ruleId": "440", "severity": 1, "message": "460", "line": 7, "column": 10, "nodeType": null, "messageId": "442", "endLine": 7, "endColumn": 14}, {"ruleId": "440", "severity": 1, "message": "471", "line": 10, "column": 3, "nodeType": null, "messageId": "442", "endLine": 10, "endColumn": 7}, {"ruleId": "440", "severity": 1, "message": "465", "line": 14, "column": 3, "nodeType": null, "messageId": "442", "endLine": 14, "endColumn": 13}, {"ruleId": "440", "severity": 1, "message": "466", "line": 16, "column": 3, "nodeType": null, "messageId": "442", "endLine": 16, "endColumn": 7}, {"ruleId": "440", "severity": 1, "message": "454", "line": 17, "column": 3, "nodeType": null, "messageId": "442", "endLine": 17, "endColumn": 8}, {"ruleId": "440", "severity": 1, "message": "461", "line": 18, "column": 3, "nodeType": null, "messageId": "442", "endLine": 18, "endColumn": 9}, {"ruleId": "440", "severity": 1, "message": "472", "line": 20, "column": 3, "nodeType": null, "messageId": "442", "endLine": 20, "endColumn": 13}, {"ruleId": "440", "severity": 1, "message": "473", "line": 44, "column": 14, "nodeType": null, "messageId": "442", "endLine": 44, "endColumn": 19}, {"ruleId": "474", "severity": 1, "message": "475", "line": 67, "column": 68, "nodeType": "476", "messageId": "477", "suggestions": "478"}, {"ruleId": "474", "severity": 1, "message": "475", "line": 136, "column": 30, "nodeType": "476", "messageId": "477", "suggestions": "479"}, {"ruleId": "474", "severity": 1, "message": "475", "line": 280, "column": 24, "nodeType": "476", "messageId": "477", "suggestions": "480"}, {"ruleId": "448", "severity": 1, "message": "449", "line": 219, "column": 23, "nodeType": "450", "messageId": "451", "endLine": 219, "endColumn": 26, "suggestions": "481"}, {"ruleId": "440", "severity": 1, "message": "482", "line": 5, "column": 10, "nodeType": null, "messageId": "442", "endLine": 5, "endColumn": 14}, {"ruleId": "440", "severity": 1, "message": "465", "line": 13, "column": 3, "nodeType": null, "messageId": "442", "endLine": 13, "endColumn": 13}, {"ruleId": "444", "severity": 1, "message": "483", "line": 37, "column": 6, "nodeType": "446", "endLine": 37, "endColumn": 47, "suggestions": "484"}, {"ruleId": "440", "severity": 1, "message": "460", "line": 7, "column": 10, "nodeType": null, "messageId": "442", "endLine": 7, "endColumn": 14}, {"ruleId": "440", "severity": 1, "message": "465", "line": 11, "column": 3, "nodeType": null, "messageId": "442", "endLine": 11, "endColumn": 13}, {"ruleId": "440", "severity": 1, "message": "472", "line": 16, "column": 3, "nodeType": null, "messageId": "442", "endLine": 16, "endColumn": 13}, {"ruleId": "440", "severity": 1, "message": "485", "line": 17, "column": 3, "nodeType": null, "messageId": "442", "endLine": 17, "endColumn": 8}, {"ruleId": "440", "severity": 1, "message": "441", "line": 18, "column": 3, "nodeType": null, "messageId": "442", "endLine": 18, "endColumn": 12}, {"ruleId": "440", "severity": 1, "message": "464", "line": 5, "column": 10, "nodeType": null, "messageId": "442", "endLine": 5, "endColumn": 18}, {"ruleId": "440", "severity": 1, "message": "485", "line": 11, "column": 3, "nodeType": null, "messageId": "442", "endLine": 11, "endColumn": 8}, {"ruleId": "440", "severity": 1, "message": "486", "line": 24, "column": 17, "nodeType": null, "messageId": "442", "endLine": 24, "endColumn": 28}, {"ruleId": "444", "severity": 1, "message": "483", "line": 38, "column": 6, "nodeType": "446", "endLine": 38, "endColumn": 47, "suggestions": "487"}, {"ruleId": "440", "severity": 1, "message": "460", "line": 7, "column": 10, "nodeType": null, "messageId": "442", "endLine": 7, "endColumn": 14}, {"ruleId": "440", "severity": 1, "message": "471", "line": 10, "column": 3, "nodeType": null, "messageId": "442", "endLine": 10, "endColumn": 7}, {"ruleId": "440", "severity": 1, "message": "472", "line": 18, "column": 3, "nodeType": null, "messageId": "442", "endLine": 18, "endColumn": 13}, {"ruleId": "440", "severity": 1, "message": "441", "line": 19, "column": 3, "nodeType": null, "messageId": "442", "endLine": 19, "endColumn": 12}, {"ruleId": "440", "severity": 1, "message": "455", "line": 13, "column": 3, "nodeType": null, "messageId": "442", "endLine": 13, "endColumn": 8}, {"ruleId": "440", "severity": 1, "message": "482", "line": 15, "column": 3, "nodeType": null, "messageId": "442", "endLine": 15, "endColumn": 7}, {"ruleId": "444", "severity": 1, "message": "488", "line": 79, "column": 6, "nodeType": "446", "endLine": 79, "endColumn": 38, "suggestions": "489"}, {"ruleId": "444", "severity": 1, "message": "490", "line": 29, "column": 6, "nodeType": "446", "endLine": 29, "endColumn": 19, "suggestions": "491"}, {"ruleId": "448", "severity": 1, "message": "449", "line": 101, "column": 11, "nodeType": "450", "messageId": "451", "endLine": 101, "endColumn": 14, "suggestions": "492"}, {"ruleId": "440", "severity": 1, "message": "455", "line": 11, "column": 3, "nodeType": null, "messageId": "442", "endLine": 11, "endColumn": 8}, {"ruleId": "440", "severity": 1, "message": "493", "line": 12, "column": 3, "nodeType": null, "messageId": "442", "endLine": 12, "endColumn": 7}, {"ruleId": "440", "severity": 1, "message": "494", "line": 3, "column": 10, "nodeType": null, "messageId": "442", "endLine": 3, "endColumn": 18}, {"ruleId": "474", "severity": 1, "message": "475", "line": 114, "column": 63, "nodeType": "476", "messageId": "477", "suggestions": "495"}, {"ruleId": "440", "severity": 1, "message": "496", "line": 13, "column": 3, "nodeType": null, "messageId": "442", "endLine": 13, "endColumn": 6}, {"ruleId": "440", "severity": 1, "message": "482", "line": 16, "column": 3, "nodeType": null, "messageId": "442", "endLine": 16, "endColumn": 7}, {"ruleId": "448", "severity": 1, "message": "449", "line": 133, "column": 52, "nodeType": "450", "messageId": "451", "endLine": 133, "endColumn": 55, "suggestions": "497"}, {"ruleId": "440", "severity": 1, "message": "455", "line": 11, "column": 3, "nodeType": null, "messageId": "442", "endLine": 11, "endColumn": 8}, {"ruleId": "440", "severity": 1, "message": "493", "line": 12, "column": 3, "nodeType": null, "messageId": "442", "endLine": 12, "endColumn": 7}, {"ruleId": "440", "severity": 1, "message": "466", "line": 14, "column": 3, "nodeType": null, "messageId": "442", "endLine": 14, "endColumn": 7}, {"ruleId": "448", "severity": 1, "message": "449", "line": 176, "column": 52, "nodeType": "450", "messageId": "451", "endLine": 176, "endColumn": 55, "suggestions": "498"}, {"ruleId": "440", "severity": 1, "message": "499", "line": 7, "column": 10, "nodeType": null, "messageId": "442", "endLine": 7, "endColumn": 15}, {"ruleId": "440", "severity": 1, "message": "456", "line": 13, "column": 3, "nodeType": null, "messageId": "442", "endLine": 13, "endColumn": 17}, {"ruleId": "448", "severity": 1, "message": "449", "line": 20, "column": 20, "nodeType": "450", "messageId": "451", "endLine": 20, "endColumn": 23, "suggestions": "500"}, {"ruleId": "448", "severity": 1, "message": "449", "line": 50, "column": 52, "nodeType": "450", "messageId": "451", "endLine": 50, "endColumn": 55, "suggestions": "501"}, {"ruleId": "440", "severity": 1, "message": "502", "line": 8, "column": 27, "nodeType": null, "messageId": "442", "endLine": 8, "endColumn": 35}, {"ruleId": "474", "severity": 1, "message": "503", "line": 155, "column": 32, "nodeType": "476", "messageId": "477", "suggestions": "504"}, {"ruleId": "474", "severity": 1, "message": "503", "line": 155, "column": 42, "nodeType": "476", "messageId": "477", "suggestions": "505"}, {"ruleId": "474", "severity": 1, "message": "475", "line": 335, "column": 38, "nodeType": "476", "messageId": "477", "suggestions": "506"}, {"ruleId": "440", "severity": 1, "message": "507", "line": 3, "column": 8, "nodeType": null, "messageId": "442", "endLine": 3, "endColumn": 13}, {"ruleId": "448", "severity": 1, "message": "449", "line": 21, "column": 51, "nodeType": "450", "messageId": "451", "endLine": 21, "endColumn": 54, "suggestions": "508"}, {"ruleId": "448", "severity": 1, "message": "449", "line": 22, "column": 16, "nodeType": "450", "messageId": "451", "endLine": 22, "endColumn": 19, "suggestions": "509"}, {"ruleId": "448", "severity": 1, "message": "449", "line": 92, "column": 40, "nodeType": "450", "messageId": "451", "endLine": 92, "endColumn": 43, "suggestions": "510"}, {"ruleId": "440", "severity": 1, "message": "511", "line": 6, "column": 44, "nodeType": null, "messageId": "442", "endLine": 6, "endColumn": 51}, {"ruleId": "440", "severity": 1, "message": "512", "line": 6, "column": 10, "nodeType": null, "messageId": "442", "endLine": 6, "endColumn": 25}, {"ruleId": "440", "severity": 1, "message": "513", "line": 6, "column": 35, "nodeType": null, "messageId": "442", "endLine": 6, "endColumn": 42}, {"ruleId": "514", "severity": 1, "message": "515", "line": 57, "column": 15, "nodeType": "516", "messageId": "517", "endLine": 60, "endColumn": 21}, {"ruleId": "514", "severity": 1, "message": "515", "line": 61, "column": 15, "nodeType": "516", "messageId": "517", "endLine": 64, "endColumn": 21}, {"ruleId": "514", "severity": 1, "message": "515", "line": 65, "column": 15, "nodeType": "516", "messageId": "517", "endLine": 68, "endColumn": 21}, {"ruleId": "474", "severity": 1, "message": "503", "line": 92, "column": 21, "nodeType": "476", "messageId": "477", "suggestions": "518"}, {"ruleId": "474", "severity": 1, "message": "503", "line": 92, "column": 58, "nodeType": "476", "messageId": "477", "suggestions": "519"}, {"ruleId": "448", "severity": 1, "message": "449", "line": 169, "column": 52, "nodeType": "450", "messageId": "451", "endLine": 169, "endColumn": 55, "suggestions": "520"}, {"ruleId": "521", "severity": 1, "message": "522", "line": 5, "column": 18, "nodeType": "523", "messageId": "524", "endLine": 5, "endColumn": 28, "suggestions": "525"}, {"ruleId": "521", "severity": 1, "message": "522", "line": 5, "column": 18, "nodeType": "523", "messageId": "524", "endLine": 5, "endColumn": 31, "suggestions": "526"}, {"ruleId": "440", "severity": 1, "message": "473", "line": 51, "column": 14, "nodeType": null, "messageId": "442", "endLine": 51, "endColumn": 19}, {"ruleId": "448", "severity": 1, "message": "449", "line": 66, "column": 38, "nodeType": "450", "messageId": "451", "endLine": 66, "endColumn": 41, "suggestions": "527"}, {"ruleId": "448", "severity": 1, "message": "449", "line": 78, "column": 30, "nodeType": "450", "messageId": "451", "endLine": 78, "endColumn": 33, "suggestions": "528"}, {"ruleId": "448", "severity": 1, "message": "449", "line": 79, "column": 30, "nodeType": "450", "messageId": "451", "endLine": 79, "endColumn": 33, "suggestions": "529"}, {"ruleId": "448", "severity": 1, "message": "449", "line": 265, "column": 27, "nodeType": "450", "messageId": "451", "endLine": 265, "endColumn": 30, "suggestions": "530"}, "@typescript-eslint/no-unused-vars", "'BarChart3' is defined but never used.", "unusedVar", "'Scissors' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadAnalytics'. Either include it or remove the dependency array.", "ArrayExpression", ["531"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["532", "533"], "'CalendarPlus' is defined but never used.", "'Clock' is defined but never used.", "'Phone' is defined but never used.", "'MoreHorizontal' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadAppointments'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["534"], "'actionMenuItems' is assigned a value but never used.", "'Edit' is defined but never used.", "'MapPin' is defined but never used.", "'ArrowLeft' is defined but never used.", "'MessageSquare' is defined but never used.", "'UserPlus' is defined but never used.", "'DollarSign' is defined but never used.", "'Star' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadCustomers'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["535"], "'record' is defined but never used.", ["536", "537"], "'User' is defined but never used.", "'TrendingUp' is defined but never used.", "'error' is defined but never used.", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["538", "539", "540", "541"], ["542", "543", "544", "545"], ["546", "547", "548", "549"], ["550", "551"], "'Plus' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["552"], "'Users' is defined but never used.", "'Appointment' is defined but never used.", ["553"], "React Hook useEffect has a missing dependency: 'appointment'. Either include it or remove the dependency array.", ["554"], "React Hook useEffect has a missing dependency: 'loadAppointments'. Either include it or remove the dependency array.", ["555"], ["556", "557"], "'Mail' is defined but never used.", "'useState' is defined but never used.", ["558", "559", "560", "561"], "'Tag' is defined but never used.", ["562", "563"], ["564", "565"], "'Badge' is defined but never used.", ["566", "567"], ["568", "569"], "'Parallax' is defined but never used.", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["570", "571", "572", "573"], ["574", "575", "576", "577"], ["578", "579", "580", "581"], "'Image' is defined but never used.", ["582", "583"], ["584", "585"], ["586", "587"], "'CountUp' is defined but never used.", "'FloatingElement' is defined but never used.", "'ScaleIn' is defined but never used.", "react/jsx-key", "Missing \"key\" prop for element in array", "JSXElement", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", ["588", "589", "590", "591"], ["592", "593", "594", "595"], ["596", "597"], "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["598"], ["599"], ["600", "601"], ["602", "603"], ["604", "605"], ["606", "607"], {"desc": "608", "fix": "609"}, {"messageId": "610", "fix": "611", "desc": "612"}, {"messageId": "613", "fix": "614", "desc": "615"}, {"desc": "616", "fix": "617"}, {"desc": "618", "fix": "619"}, {"messageId": "610", "fix": "620", "desc": "612"}, {"messageId": "613", "fix": "621", "desc": "615"}, {"messageId": "622", "data": "623", "fix": "624", "desc": "625"}, {"messageId": "622", "data": "626", "fix": "627", "desc": "628"}, {"messageId": "622", "data": "629", "fix": "630", "desc": "631"}, {"messageId": "622", "data": "632", "fix": "633", "desc": "634"}, {"messageId": "622", "data": "635", "fix": "636", "desc": "625"}, {"messageId": "622", "data": "637", "fix": "638", "desc": "628"}, {"messageId": "622", "data": "639", "fix": "640", "desc": "631"}, {"messageId": "622", "data": "641", "fix": "642", "desc": "634"}, {"messageId": "622", "data": "643", "fix": "644", "desc": "625"}, {"messageId": "622", "data": "645", "fix": "646", "desc": "628"}, {"messageId": "622", "data": "647", "fix": "648", "desc": "631"}, {"messageId": "622", "data": "649", "fix": "650", "desc": "634"}, {"messageId": "610", "fix": "651", "desc": "612"}, {"messageId": "613", "fix": "652", "desc": "615"}, {"desc": "653", "fix": "654"}, {"desc": "653", "fix": "655"}, {"desc": "656", "fix": "657"}, {"desc": "658", "fix": "659"}, {"messageId": "610", "fix": "660", "desc": "612"}, {"messageId": "613", "fix": "661", "desc": "615"}, {"messageId": "622", "data": "662", "fix": "663", "desc": "625"}, {"messageId": "622", "data": "664", "fix": "665", "desc": "628"}, {"messageId": "622", "data": "666", "fix": "667", "desc": "631"}, {"messageId": "622", "data": "668", "fix": "669", "desc": "634"}, {"messageId": "610", "fix": "670", "desc": "612"}, {"messageId": "613", "fix": "671", "desc": "615"}, {"messageId": "610", "fix": "672", "desc": "612"}, {"messageId": "613", "fix": "673", "desc": "615"}, {"messageId": "610", "fix": "674", "desc": "612"}, {"messageId": "613", "fix": "675", "desc": "615"}, {"messageId": "610", "fix": "676", "desc": "612"}, {"messageId": "613", "fix": "677", "desc": "615"}, {"messageId": "622", "data": "678", "fix": "679", "desc": "680"}, {"messageId": "622", "data": "681", "fix": "682", "desc": "683"}, {"messageId": "622", "data": "684", "fix": "685", "desc": "686"}, {"messageId": "622", "data": "687", "fix": "688", "desc": "689"}, {"messageId": "622", "data": "690", "fix": "691", "desc": "680"}, {"messageId": "622", "data": "692", "fix": "693", "desc": "683"}, {"messageId": "622", "data": "694", "fix": "695", "desc": "686"}, {"messageId": "622", "data": "696", "fix": "697", "desc": "689"}, {"messageId": "622", "data": "698", "fix": "699", "desc": "625"}, {"messageId": "622", "data": "700", "fix": "701", "desc": "628"}, {"messageId": "622", "data": "702", "fix": "703", "desc": "631"}, {"messageId": "622", "data": "704", "fix": "705", "desc": "634"}, {"messageId": "610", "fix": "706", "desc": "612"}, {"messageId": "613", "fix": "707", "desc": "615"}, {"messageId": "610", "fix": "708", "desc": "612"}, {"messageId": "613", "fix": "709", "desc": "615"}, {"messageId": "610", "fix": "710", "desc": "612"}, {"messageId": "613", "fix": "711", "desc": "615"}, {"messageId": "622", "data": "712", "fix": "713", "desc": "680"}, {"messageId": "622", "data": "714", "fix": "715", "desc": "683"}, {"messageId": "622", "data": "716", "fix": "717", "desc": "686"}, {"messageId": "622", "data": "718", "fix": "719", "desc": "689"}, {"messageId": "622", "data": "720", "fix": "721", "desc": "680"}, {"messageId": "622", "data": "722", "fix": "723", "desc": "683"}, {"messageId": "622", "data": "724", "fix": "725", "desc": "686"}, {"messageId": "622", "data": "726", "fix": "727", "desc": "689"}, {"messageId": "610", "fix": "728", "desc": "612"}, {"messageId": "613", "fix": "729", "desc": "615"}, {"messageId": "730", "fix": "731", "desc": "732"}, {"messageId": "730", "fix": "733", "desc": "732"}, {"messageId": "610", "fix": "734", "desc": "612"}, {"messageId": "613", "fix": "735", "desc": "615"}, {"messageId": "610", "fix": "736", "desc": "612"}, {"messageId": "613", "fix": "737", "desc": "615"}, {"messageId": "610", "fix": "738", "desc": "612"}, {"messageId": "613", "fix": "739", "desc": "615"}, {"messageId": "610", "fix": "740", "desc": "612"}, {"messageId": "613", "fix": "741", "desc": "615"}, "Update the dependencies array to be: [dateRange, loadAnalytics]", {"range": "742", "text": "743"}, "suggestUnknown", {"range": "744", "text": "745"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "746", "text": "747"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "Update the dependencies array to be: [loadAppointments, pagination.pageSize]", {"range": "748", "text": "749"}, "Update the dependencies array to be: [loadCustomers, pagination.pageSize]", {"range": "750", "text": "751"}, {"range": "752", "text": "745"}, {"range": "753", "text": "747"}, "replaceWithAlt", {"alt": "754"}, {"range": "755", "text": "756"}, "Replace with `&apos;`.", {"alt": "757"}, {"range": "758", "text": "759"}, "Replace with `&lsquo;`.", {"alt": "760"}, {"range": "761", "text": "762"}, "Replace with `&#39;`.", {"alt": "763"}, {"range": "764", "text": "765"}, "Replace with `&rsquo;`.", {"alt": "754"}, {"range": "766", "text": "767"}, {"alt": "757"}, {"range": "768", "text": "769"}, {"alt": "760"}, {"range": "770", "text": "771"}, {"alt": "763"}, {"range": "772", "text": "773"}, {"alt": "754"}, {"range": "774", "text": "775"}, {"alt": "757"}, {"range": "776", "text": "777"}, {"alt": "760"}, {"range": "778", "text": "779"}, {"alt": "763"}, {"range": "780", "text": "781"}, {"range": "782", "text": "745"}, {"range": "783", "text": "747"}, "Update the dependencies array to be: [loadData, pagination.pageSize]", {"range": "784", "text": "785"}, {"range": "786", "text": "785"}, "Update the dependencies array to be: [prefilledCustomerId, customers, appointment]", {"range": "787", "text": "788"}, "Update the dependencies array to be: [currentDate, loadAppointments]", {"range": "789", "text": "790"}, {"range": "791", "text": "745"}, {"range": "792", "text": "747"}, {"alt": "754"}, {"range": "793", "text": "756"}, {"alt": "757"}, {"range": "794", "text": "759"}, {"alt": "760"}, {"range": "795", "text": "762"}, {"alt": "763"}, {"range": "796", "text": "765"}, {"range": "797", "text": "745"}, {"range": "798", "text": "747"}, {"range": "799", "text": "745"}, {"range": "800", "text": "747"}, {"range": "801", "text": "745"}, {"range": "802", "text": "747"}, {"range": "803", "text": "745"}, {"range": "804", "text": "747"}, {"alt": "805"}, {"range": "806", "text": "807"}, "Replace with `&quot;`.", {"alt": "808"}, {"range": "809", "text": "810"}, "Replace with `&ldquo;`.", {"alt": "811"}, {"range": "812", "text": "813"}, "Replace with `&#34;`.", {"alt": "814"}, {"range": "815", "text": "816"}, "Replace with `&rdquo;`.", {"alt": "805"}, {"range": "817", "text": "818"}, {"alt": "808"}, {"range": "819", "text": "820"}, {"alt": "811"}, {"range": "821", "text": "822"}, {"alt": "814"}, {"range": "823", "text": "824"}, {"alt": "754"}, {"range": "825", "text": "826"}, {"alt": "757"}, {"range": "827", "text": "828"}, {"alt": "760"}, {"range": "829", "text": "830"}, {"alt": "763"}, {"range": "831", "text": "832"}, {"range": "833", "text": "745"}, {"range": "834", "text": "747"}, {"range": "835", "text": "745"}, {"range": "836", "text": "747"}, {"range": "837", "text": "745"}, {"range": "838", "text": "747"}, {"alt": "805"}, {"range": "839", "text": "840"}, {"alt": "808"}, {"range": "841", "text": "842"}, {"alt": "811"}, {"range": "843", "text": "844"}, {"alt": "814"}, {"range": "845", "text": "846"}, {"alt": "805"}, {"range": "847", "text": "848"}, {"alt": "808"}, {"range": "849", "text": "850"}, {"alt": "811"}, {"range": "851", "text": "852"}, {"alt": "814"}, {"range": "853", "text": "854"}, {"range": "855", "text": "745"}, {"range": "856", "text": "747"}, "replaceEmptyInterfaceWithSuper", {"range": "857", "text": "858"}, "Replace empty interface with a type alias.", {"range": "859", "text": "860"}, {"range": "861", "text": "745"}, {"range": "862", "text": "747"}, {"range": "863", "text": "745"}, {"range": "864", "text": "747"}, {"range": "865", "text": "745"}, {"range": "866", "text": "747"}, {"range": "867", "text": "745"}, {"range": "868", "text": "747"}, [1429, 1440], "[dateR<PERSON><PERSON>, loadAnalytics]", [13764, 13767], "unknown", [13764, 13767], "never", [988, 1029], "[loadAppointments, pagination.pageSize]", [959, 1000], "[loadCustomers, pagination.pageSize]", [6331, 6334], [6331, 6334], "&apos;", [1880, 1897], "Tony&<PERSON><PERSON>s;s Barbershop", "&lsquo;", [1880, 1897], "Tony&lsquo;s Barbershop", "&#39;", [1880, 1897], "<PERSON>&#39;s <PERSON><PERSON><PERSON>", "&rsquo;", [1880, 1897], "Tony&rsquo;s Barbershop", [4342, 4393], "&copy; 2024 Tony&apos;s Barbershop. All rights reserved.", [4342, 4393], "&copy; 2024 Tony&lsquo;s Barbershop. All rights reserved.", [4342, 4393], "&copy; 2024 Tony&#39;s Barbersh<PERSON>. All rights reserved.", [4342, 4393], "&copy; 2024 Tony&rsquo;s Barbershop. All rights reserved.", [7766, 7821], "\n              欢迎使用 Tony&apos;s Barbershop 管理系统\n            ", [7766, 7821], "\n              欢迎使用 Tony&lsquo;s Barbershop 管理系统\n            ", [7766, 7821], "\n              欢迎使用 Tony&#39;s Barbershop 管理系统\n            ", [7766, 7821], "\n              欢迎使用 Tony&rsquo;s Barbershop 管理系统\n            ", [6410, 6413], [6410, 6413], [1025, 1066], "[loadData, pagination.pageSize]", [949, 990], [2296, 2328], "[prefilledCustomerId, customers, appointment]", [844, 857], "[currentDate, loadAppointments]", [2802, 2805], [2802, 2805], [2494, 2511], [2494, 2511], [2494, 2511], [2494, 2511], [3316, 3319], [3316, 3319], [4672, 4675], [4672, 4675], [426, 429], [426, 429], [1204, 1207], [1204, 1207], "&quot;", [4405, 4570], "\n                    十五年来，我们始终坚持&quot;品质第一，客户至上\"的经营理念，\n                    不断提升服务质量，引进先进设备和优质产品。我们相信，\n                    每一次剪发都是一次艺术创作，每一位客户都值得我们用心对待。\n                  ", "&ldquo;", [4405, 4570], "\n                    十五年来，我们始终坚持&ldquo;品质第一，客户至上\"的经营理念，\n                    不断提升服务质量，引进先进设备和优质产品。我们相信，\n                    每一次剪发都是一次艺术创作，每一位客户都值得我们用心对待。\n                  ", "&#34;", [4405, 4570], "\n                    十五年来，我们始终坚持&#34;品质第一，客户至上\"的经营理念，\n                    不断提升服务质量，引进先进设备和优质产品。我们相信，\n                    每一次剪发都是一次艺术创作，每一位客户都值得我们用心对待。\n                  ", "&rdquo;", [4405, 4570], "\n                    十五年来，我们始终坚持&rdquo;品质第一，客户至上\"的经营理念，\n                    不断提升服务质量，引进先进设备和优质产品。我们相信，\n                    每一次剪发都是一次艺术创作，每一位客户都值得我们用心对待。\n                  ", [4405, 4570], "\n                    十五年来，我们始终坚持\"品质第一，客户至上&quot;的经营理念，\n                    不断提升服务质量，引进先进设备和优质产品。我们相信，\n                    每一次剪发都是一次艺术创作，每一位客户都值得我们用心对待。\n                  ", [4405, 4570], "\n                    十五年来，我们始终坚持\"品质第一，客户至上&ldquo;的经营理念，\n                    不断提升服务质量，引进先进设备和优质产品。我们相信，\n                    每一次剪发都是一次艺术创作，每一位客户都值得我们用心对待。\n                  ", [4405, 4570], "\n                    十五年来，我们始终坚持\"品质第一，客户至上&#34;的经营理念，\n                    不断提升服务质量，引进先进设备和优质产品。我们相信，\n                    每一次剪发都是一次艺术创作，每一位客户都值得我们用心对待。\n                  ", [4405, 4570], "\n                    十五年来，我们始终坚持\"品质第一，客户至上&rdquo;的经营理念，\n                    不断提升服务质量，引进先进设备和优质产品。我们相信，\n                    每一次剪发都是一次艺术创作，每一位客户都值得我们用心对待。\n                  ", [12746, 12822], "\n                          S8-32号 <PERSON>&apos;s Barbershop\n                        ", [12746, 12822], "\n                          S8-32号 <PERSON>&lsquo;s Barbershop\n                        ", [12746, 12822], "\n                          S8-32号 <PERSON>&#39;s Barbersh<PERSON>\n                        ", [12746, 12822], "\n                          S8-32号 Tony&rsquo;s Barbershop\n                        ", [487, 490], [487, 490], [515, 518], [515, 518], [2556, 2559], [2556, 2559], [2604, 2626], "\n                    &quot;", [2604, 2626], "\n                    &ldquo;", [2604, 2626], "\n                    &#34;", [2604, 2626], "\n                    &rdquo;", [2662, 2682], "&quot;\n                  ", [2662, 2682], "&ldquo;\n                  ", [2662, 2682], "&#34;\n                  ", [2662, 2682], "&rdquo;\n                  ", [3951, 3954], [3951, 3954], [73, 150], "type InputProps = React.InputHTMLAttributes<HTMLInputElement>", [73, 159], "type TextareaProps = React.TextareaHTMLAttributes<HTMLTextAreaElement>", [1652, 1655], [1652, 1655], [1921, 1924], [1921, 1924], [1971, 1974], [1971, 1974], [5101, 5104], [5101, 5104]]