<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#d4af37;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#b8860b;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#8b7355;stop-opacity:1" />
    </linearGradient>
    <pattern id="barbershop" patternUnits="userSpaceOnUse" width="40" height="40">
      <rect width="40" height="40" fill="url(#grad1)" opacity="0.1"/>
      <path d="M20 5 L35 20 L20 35 L5 20 Z" fill="#d4af37" opacity="0.2"/>
    </pattern>
  </defs>
  
  <!-- Background -->
  <rect width="100%" height="100%" fill="url(#barbershop)"/>
  
  <!-- Overlay -->
  <rect width="100%" height="100%" fill="rgba(0,0,0,0.3)"/>
  
  <!-- Barbershop Icon -->
  <g transform="translate(200, 110)">
    <circle cx="0" cy="0" r="30" fill="#d4af37" opacity="0.8"/>
    <text x="0" y="8" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" fill="#000">✂️</text>
  </g>
  
  <!-- Text -->
  <text x="200" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#d4af37">精致胡须造型</text>
  <text x="200" y="195" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#ffffff" opacity="0.8">Tony's Barbershop</text>
  
  <!-- Decorative elements -->
  <rect x="10" y="10" width="3" height="20" fill="#d4af37" opacity="0.6"/>
  <rect x="10" y="35" width="3" height="20" fill="#d4af37" opacity="0.6"/>
  <rect x="387" y="10" width="3" height="20" fill="#d4af37" opacity="0.6"/>
  <rect x="387" y="35" width="3" height="20" fill="#d4af37" opacity="0.6"/>
</svg>