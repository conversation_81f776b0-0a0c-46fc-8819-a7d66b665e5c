"use client"

import { useState, useEffect } from 'react'
import { usePathname } from 'next/navigation'
import { Bell, Search, User, ChevronDown } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/admin/ui/badge'
import { Input } from '@/components/ui/input'
import { authService } from '@/lib/admin/auth'
import { User as UserType } from '@/lib/types/admin'
import { MobileMenuButton } from './sidebar'

interface HeaderProps {
  onMenuToggle: () => void
  className?: string
}

// 页面标题映射
const pageTitles: Record<string, { title: string; subtitle?: string }> = {
  '/admin': { title: '仪表板', subtitle: '总览和快速操作' },
  '/admin/appointments': { title: '预约管理', subtitle: '查看和管理客户预约' },
  '/admin/customers': { title: '客户管理', subtitle: '客户信息和服务历史' },
  '/admin/services': { title: '服务项目', subtitle: '管理服务项目和价格' },
  '/admin/staff': { title: '员工管理', subtitle: '员工信息和工作安排' },
  '/admin/analytics': { title: '数据统计', subtitle: '营业数据和分析报告' },
  '/admin/settings': { title: '系统设置', subtitle: '系统配置和偏好设置' }
}

export function Header({ onMenuToggle, className }: HeaderProps) {
  const pathname = usePathname()
  const [currentUser, setCurrentUser] = useState<UserType | null>(null)
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [notifications] = useState(3) // 模拟通知数量

  useEffect(() => {
    const user = authService.getCurrentUser()
    setCurrentUser(user)
  }, [])

  // 获取当前页面信息
  const currentPage = pageTitles[pathname] || { title: '管理后台', subtitle: '' }

  // 获取当前时间问候语
  const getGreeting = () => {
    const hour = new Date().getHours()
    if (hour < 12) return '早上好'
    if (hour < 18) return '下午好'
    return '晚上好'
  }

  const handleLogout = () => {
    authService.logout()
    window.location.href = '/admin/login'
  }

  return (
    <header className={cn("bg-card border-b border-border", className)}>
      <div className="flex items-center justify-between px-4 py-3">
        {/* 左侧：菜单按钮 + 页面标题 */}
        <div className="flex items-center space-x-4">
          <MobileMenuButton onClick={onMenuToggle} />
          
          <div>
            <h1 className="text-xl font-semibold text-foreground">
              {currentPage.title}
            </h1>
            {currentPage.subtitle && (
              <p className="text-sm text-muted-foreground">
                {currentPage.subtitle}
              </p>
            )}
          </div>
        </div>

        {/* 中间：搜索框 (桌面端) */}
        <div className="hidden md:flex flex-1 max-w-md mx-8">
          <div className="relative w-full">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="搜索客户、预约、服务..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 bg-background"
            />
          </div>
        </div>

        {/* 右侧：通知 + 用户菜单 */}
        <div className="flex items-center space-x-4">
          {/* 通知按钮 */}
          <button className="relative p-2 rounded-md hover:bg-accent transition-colors">
            <Bell className="h-5 w-5 text-muted-foreground" />
            {notifications > 0 && (
              <Badge 
                variant="destructive" 
                size="sm"
                className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center text-xs"
              >
                {notifications}
              </Badge>
            )}
          </button>

          {/* 用户菜单 */}
          <div className="relative">
            <button
              onClick={() => setShowUserMenu(!showUserMenu)}
              className="flex items-center space-x-2 p-2 rounded-md hover:bg-accent transition-colors"
            >
              <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                <User className="h-4 w-4 text-primary-foreground" />
              </div>
              <div className="hidden sm:block text-left">
                <p className="text-sm font-medium text-foreground">
                  {getGreeting()}, {currentUser?.name || '管理员'}
                </p>
                <p className="text-xs text-muted-foreground">
                  {currentUser?.role === 'super_admin' ? '超级管理员' : '管理员'}
                </p>
              </div>
              <ChevronDown className="h-4 w-4 text-muted-foreground" />
            </button>

            {/* 用户下拉菜单 */}
            {showUserMenu && (
              <>
                <div 
                  className="fixed inset-0 z-10"
                  onClick={() => setShowUserMenu(false)}
                />
                <div className="absolute right-0 top-full mt-2 w-48 bg-card border border-border rounded-md shadow-lg z-20">
                  <div className="p-3 border-b border-border">
                    <p className="font-medium text-foreground">{currentUser?.name}</p>
                    <p className="text-sm text-muted-foreground">{currentUser?.username}</p>
                  </div>
                  
                  <div className="py-1">
                    <button
                      onClick={() => {
                        setShowUserMenu(false)
                        // TODO: 打开个人资料页面
                      }}
                      className="w-full px-3 py-2 text-left text-sm text-foreground hover:bg-accent transition-colors"
                    >
                      个人资料
                    </button>
                    <button
                      onClick={() => {
                        setShowUserMenu(false)
                        // TODO: 打开修改密码页面
                      }}
                      className="w-full px-3 py-2 text-left text-sm text-foreground hover:bg-accent transition-colors"
                    >
                      修改密码
                    </button>
                    <div className="border-t border-border my-1" />
                    <button
                      onClick={() => {
                        setShowUserMenu(false)
                        handleLogout()
                      }}
                      className="w-full px-3 py-2 text-left text-sm text-destructive hover:bg-accent transition-colors"
                    >
                      退出登录
                    </button>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* 移动端搜索框 */}
      <div className="md:hidden px-4 pb-3">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            type="text"
            placeholder="搜索..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 bg-background"
          />
        </div>
      </div>
    </header>
  )
}
