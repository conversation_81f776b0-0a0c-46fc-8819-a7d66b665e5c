"use client"

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/admin/ui/badge'
import { 
  User, 
  Phone, 
  Mail,
  Clock,
  Star,
  Award,
  Plus,
  X,
  Save,
  ArrowLeft
} from 'lucide-react'
import { staffStore, serviceStore } from '@/lib/admin/storage'
import { Staff, Service } from '@/lib/types/admin'

interface StaffFormProps {
  staff?: Staff
  onSubmit?: (staff: Staff) => void
  onCancel?: () => void
}

export function StaffForm({ staff, onSubmit, onCancel }: StaffFormProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [services, setServices] = useState<Service[]>([])
  const [newSpecialty, setNewSpecialty] = useState('')
  
  const [formData, setFormData] = useState({
    name: staff?.name || '',
    phone: staff?.phone || '',
    email: staff?.email || '',
    specialties: staff?.specialties || [] as string[],
    workingHours: staff?.workingHours || [] as string[],
    startTime: staff?.startTime || '09:00',
    endTime: staff?.endTime || '18:00',
    isManager: staff?.isManager || false,
    isActive: staff?.isActive ?? true,
    notes: staff?.notes || ''
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    // 加载服务列表用于技能选择
    const allServices = serviceStore.getAll()
    setServices(allServices.filter(s => s.isActive))
  }, [])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = '请输入员工姓名'
    }

    if (!formData.phone.trim()) {
      newErrors.phone = '请输入手机号码'
    } else if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
      newErrors.phone = '请输入正确的手机号码'
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = '请输入正确的邮箱地址'
    }

    if (formData.workingHours.length === 0) {
      newErrors.workingHours = '请选择至少一个工作日'
    }

    if (!formData.startTime) {
      newErrors.startTime = '请设置开始工作时间'
    }

    if (!formData.endTime) {
      newErrors.endTime = '请设置结束工作时间'
    }

    if (formData.startTime && formData.endTime && formData.startTime >= formData.endTime) {
      newErrors.endTime = '结束时间必须晚于开始时间'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)
    try {
      let result: Staff | null

      if (staff) {
        // 更新员工
        result = staffStore.update(staff.id, {
          name: formData.name.trim(),
          phone: formData.phone.trim(),
          email: formData.email.trim() || undefined,
          specialties: formData.specialties,
          workingHours: formData.workingHours,
          startTime: formData.startTime,
          endTime: formData.endTime,
          isManager: formData.isManager,
          isActive: formData.isActive,
          notes: formData.notes.trim() || undefined
        })
      } else {
        // 创建新员工
        result = staffStore.create({
          name: formData.name.trim(),
          phone: formData.phone.trim(),
          email: formData.email.trim() || undefined,
          specialties: formData.specialties,
          workingHours: formData.workingHours,
          startTime: formData.startTime,
          endTime: formData.endTime,
          isManager: formData.isManager,
          isActive: formData.isActive,
          notes: formData.notes.trim() || undefined,
          rating: 5.0 // 新员工默认评分
        })
      }

      if (result) {
        if (onSubmit) {
          onSubmit(result)
        } else {
          router.push(`/admin/staff/${result.id}`)
        }
      }
    } catch (error) {
      console.error('Failed to save staff:', error)
      setErrors({ submit: '保存失败，请重试' })
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    if (onCancel) {
      onCancel()
    } else {
      router.back()
    }
  }

  const addSpecialty = (specialty: string) => {
    if (specialty && !formData.specialties.includes(specialty)) {
      setFormData(prev => ({
        ...prev,
        specialties: [...prev.specialties, specialty]
      }))
    }
    setNewSpecialty('')
  }

  const removeSpecialty = (index: number) => {
    setFormData(prev => ({
      ...prev,
      specialties: prev.specialties.filter((_, i) => i !== index)
    }))
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const handleWorkingHoursChange = (day: string, checked: boolean) => {
    if (checked) {
      setFormData(prev => ({
        ...prev,
        workingHours: [...prev.workingHours, day]
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        workingHours: prev.workingHours.filter(d => d !== day)
      }))
    }
    
    // 清除工作时间错误
    if (errors.workingHours) {
      setErrors(prev => ({ ...prev, workingHours: '' }))
    }
  }

  const weekDays = [
    { value: '1', label: '周一' },
    { value: '2', label: '周二' },
    { value: '3', label: '周三' },
    { value: '4', label: '周四' },
    { value: '5', label: '周五' },
    { value: '6', label: '周六' },
    { value: '7', label: '周日' }
  ]

  const timeOptions = []
  for (let hour = 8; hour <= 22; hour++) {
    for (let minute = 0; minute < 60; minute += 30) {
      const timeStr = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
      timeOptions.push(timeStr)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* 基本信息 */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium flex items-center">
          <User className="h-5 w-5 mr-2" />
          基本信息
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              员工姓名 <span className="text-red-500">*</span>
            </label>
            <Input
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="请输入员工姓名"
              className={errors.name ? 'border-red-500' : ''}
            />
            {errors.name && (
              <p className="text-sm text-red-500 mt-1">{errors.name}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              手机号码 <span className="text-red-500">*</span>
            </label>
            <Input
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              placeholder="请输入手机号码"
              className={errors.phone ? 'border-red-500' : ''}
            />
            {errors.phone && (
              <p className="text-sm text-red-500 mt-1">{errors.phone}</p>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">
            邮箱地址
          </label>
          <Input
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            placeholder="请输入邮箱地址（可选）"
            className={errors.email ? 'border-red-500' : ''}
          />
          {errors.email && (
            <p className="text-sm text-red-500 mt-1">{errors.email}</p>
          )}
        </div>
      </div>

      {/* 专业技能 */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">专业技能</h3>
        
        {/* 当前技能 */}
        <div>
          <label className="block text-sm font-medium mb-2">已掌握技能</label>
          <div className="flex flex-wrap gap-2 min-h-[40px] p-3 border border-border rounded-md bg-muted/20">
            {formData.specialties.length > 0 ? (
              formData.specialties.map((skill, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="flex items-center space-x-1"
                >
                  <span>{skill}</span>
                  <button
                    type="button"
                    onClick={() => removeSpecialty(index)}
                    className="ml-1 hover:text-red-500"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))
            ) : (
              <span className="text-sm text-muted-foreground">暂无技能设置</span>
            )}
          </div>
        </div>

        {/* 添加技能 */}
        <div>
          <label className="block text-sm font-medium mb-2">添加技能</label>
          <div className="flex space-x-2">
            <Input
              value={newSpecialty}
              onChange={(e) => setNewSpecialty(e.target.value)}
              placeholder="输入专业技能"
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault()
                  addSpecialty(newSpecialty)
                }
              }}
            />
            <Button
              type="button"
              variant="outline"
              onClick={() => addSpecialty(newSpecialty)}
              disabled={!newSpecialty.trim()}
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* 快速选择服务技能 */}
        <div>
          <label className="block text-sm font-medium mb-2">快速选择</label>
          <div className="flex flex-wrap gap-2">
            {services.map(service => (
              <Button
                key={service.id}
                type="button"
                variant="outline"
                size="sm"
                onClick={() => addSpecialty(service.name)}
                disabled={formData.specialties.includes(service.name)}
                className="text-xs"
              >
                {service.name}
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* 工作时间 */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium flex items-center">
          <Clock className="h-5 w-5 mr-2" />
          工作时间
        </h3>
        
        {/* 工作日选择 */}
        <div>
          <label className="block text-sm font-medium mb-2">
            工作日 <span className="text-red-500">*</span>
          </label>
          <div className="grid grid-cols-7 gap-2">
            {weekDays.map(day => (
              <label key={day.value} className="flex items-center space-x-2 p-2 border border-border rounded-md hover:bg-accent/5 cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.workingHours.includes(day.value)}
                  onChange={(e) => handleWorkingHoursChange(day.value, e.target.checked)}
                  className="w-4 h-4 text-primary border-border rounded focus:ring-primary"
                />
                <span className="text-sm">{day.label}</span>
              </label>
            ))}
          </div>
          {errors.workingHours && (
            <p className="text-sm text-red-500 mt-1">{errors.workingHours}</p>
          )}
        </div>

        {/* 工作时间段 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              开始时间 <span className="text-red-500">*</span>
            </label>
            <select
              value={formData.startTime}
              onChange={(e) => handleInputChange('startTime', e.target.value)}
              className={`w-full px-3 py-2 border border-border rounded-md bg-background ${
                errors.startTime ? 'border-red-500' : ''
              }`}
            >
              {timeOptions.map(time => (
                <option key={time} value={time}>{time}</option>
              ))}
            </select>
            {errors.startTime && (
              <p className="text-sm text-red-500 mt-1">{errors.startTime}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              结束时间 <span className="text-red-500">*</span>
            </label>
            <select
              value={formData.endTime}
              onChange={(e) => handleInputChange('endTime', e.target.value)}
              className={`w-full px-3 py-2 border border-border rounded-md bg-background ${
                errors.endTime ? 'border-red-500' : ''
              }`}
            >
              {timeOptions.map(time => (
                <option key={time} value={time}>{time}</option>
              ))}
            </select>
            {errors.endTime && (
              <p className="text-sm text-red-500 mt-1">{errors.endTime}</p>
            )}
          </div>
        </div>
      </div>

      {/* 权限设置 */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">权限设置</h3>
        
        <div className="space-y-3">
          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="isManager"
              checked={formData.isManager}
              onChange={(e) => handleInputChange('isManager', e.target.checked)}
              className="w-4 h-4 text-primary border-border rounded focus:ring-primary"
            />
            <label htmlFor="isManager" className="text-sm font-medium flex items-center">
              <Award className="h-4 w-4 mr-1" />
              管理员权限
            </label>
            <Badge variant={formData.isManager ? 'default' : 'outline'} size="sm">
              {formData.isManager ? '管理员' : '普通员工'}
            </Badge>
          </div>

          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="isActive"
              checked={formData.isActive}
              onChange={(e) => handleInputChange('isActive', e.target.checked)}
              className="w-4 h-4 text-primary border-border rounded focus:ring-primary"
            />
            <label htmlFor="isActive" className="text-sm font-medium">
              启用此员工
            </label>
            <Badge variant={formData.isActive ? 'success' : 'destructive'} size="sm">
              {formData.isActive ? '在职' : '离职'}
            </Badge>
          </div>
        </div>
      </div>

      {/* 备注 */}
      <div>
        <label className="block text-sm font-medium mb-2">备注信息</label>
        <Textarea
          value={formData.notes}
          onChange={(e) => handleInputChange('notes', e.target.value)}
          placeholder="员工的特殊说明、注意事项等..."
          rows={4}
        />
      </div>

      {/* 错误信息 */}
      {errors.submit && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{errors.submit}</p>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="flex justify-end space-x-3 pt-6 border-t">
        <Button
          type="button"
          variant="outline"
          onClick={handleCancel}
          disabled={loading}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          取消
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? (
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
          ) : (
            <Save className="h-4 w-4 mr-2" />
          )}
          {staff ? '更新员工' : '创建员工'}
        </Button>
      </div>
    </form>
  )
}
