"use client"

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { AdminLayout, PageContainer } from '@/components/admin/layout/admin-layout'
import { DataTable } from '@/components/admin/ui/data-table'
import { SuccessAlert } from '@/components/admin/ui/alert'
import { Badge } from '@/components/admin/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Plus, 
  User, 
  Phone,
  Mail,
  Calendar,
  DollarSign,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Star
} from 'lucide-react'
import { customerStore, appointmentStore } from '@/lib/admin/storage'
import { Customer } from '@/lib/types/admin'

export default function CustomersPage() {
  const searchParams = useSearchParams()
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([])
  const [showSuccessAlert, setShowSuccessAlert] = useState(false)
  const [successMessage, setSuccessMessage] = useState('')
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })

  useEffect(() => {
    loadCustomers()
  }, [pagination.current, pagination.pageSize])

  // 检测成功状态参数
  useEffect(() => {
    const success = searchParams.get('success')
    if (success) {
      if (success === 'created') {
        setSuccessMessage('客户添加成功！')
      } else if (success === 'updated') {
        setSuccessMessage('客户信息更新成功！')
      }
      setShowSuccessAlert(true)

      // 清除URL参数
      const url = new URL(window.location.href)
      url.searchParams.delete('success')
      window.history.replaceState({}, '', url.toString())
    }
  }, [searchParams])

  const loadCustomers = () => {
    setLoading(true)
    try {
      const result = customerStore.getPaginated({
        page: pagination.current,
        limit: pagination.pageSize,
        sortBy: 'totalVisits',
        sortOrder: 'desc'
      })
      
      setCustomers(result.data)
      setPagination(prev => ({
        ...prev,
        total: result.total
      }))
    } catch (error) {
      console.error('Failed to load customers:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (searchValue: string) => {
    setLoading(true)
    try {
      const result = customerStore.getPaginated({
        page: 1,
        limit: pagination.pageSize,
        search: searchValue,
        sortBy: 'totalVisits',
        sortOrder: 'desc'
      })
      
      setCustomers(result.data)
      setPagination(prev => ({
        ...prev,
        current: 1,
        total: result.total
      }))
    } catch (error) {
      console.error('Failed to search customers:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = (customerId: string) => {
    if (confirm('确定要删除这个客户吗？删除后相关预约记录也会受到影响。')) {
      try {
        customerStore.delete(customerId)
        loadCustomers()
      } catch (error) {
        console.error('Failed to delete customer:', error)
      }
    }
  }

  const getCustomerLevel = (totalSpent: number) => {
    if (totalSpent >= 1000) return { level: 'VIP', color: 'bg-yellow-100 text-yellow-800' }
    if (totalSpent >= 500) return { level: '金牌', color: 'bg-orange-100 text-orange-800' }
    if (totalSpent >= 200) return { level: '银牌', color: 'bg-gray-100 text-gray-800' }
    return { level: '普通', color: 'bg-blue-100 text-blue-800' }
  }

  const getRecentAppointments = (customerId: string) => {
    const appointments = appointmentStore.getAll()
    return appointments
      .filter(apt => apt.customerId === customerId)
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, 1)[0]
  }

  const formatLastVisit = (customerId: string) => {
    const lastAppointment = getRecentAppointments(customerId)
    if (!lastAppointment) return '从未到店'
    
    const visitDate = new Date(lastAppointment.date)
    const now = new Date()
    const diffTime = now.getTime() - visitDate.getTime()
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays === 0) return '今天'
    if (diffDays === 1) return '昨天'
    if (diffDays < 7) return `${diffDays}天前`
    if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`
    if (diffDays < 365) return `${Math.floor(diffDays / 30)}个月前`
    return `${Math.floor(diffDays / 365)}年前`
  }

  const columns = [
    {
      key: 'name',
      title: '客户信息',
      render: (value: string, record: Customer) => {
        const level = getCustomerLevel(record.totalSpent)
        return (
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
              <User className="h-5 w-5 text-primary" />
            </div>
            <div>
              <div className="flex items-center space-x-2">
                <p className="font-medium">{value}</p>
                <Badge 
                  variant="outline" 
                  size="sm"
                  className={level.color}
                >
                  {level.level}
                </Badge>
              </div>
              <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                <span className="flex items-center">
                  <Phone className="h-3 w-3 mr-1" />
                  {record.phone}
                </span>
                {record.email && (
                  <span className="flex items-center">
                    <Mail className="h-3 w-3 mr-1" />
                    {record.email}
                  </span>
                )}
              </div>
            </div>
          </div>
        )
      }
    },
    {
      key: 'totalVisits',
      title: '到店次数',
      render: (value: number, record: Customer) => (
        <div className="text-center">
          <p className="text-lg font-semibold text-primary">{value}</p>
          <p className="text-xs text-muted-foreground">次</p>
        </div>
      ),
      sortable: true
    },
    {
      key: 'totalSpent',
      title: '消费金额',
      render: (value: number) => (
        <div className="text-center">
          <p className="text-lg font-semibold text-green-600">¥{value}</p>
          <p className="text-xs text-muted-foreground">累计</p>
        </div>
      ),
      sortable: true
    },
    {
      key: 'preferences',
      title: '偏好服务',
      render: (value: string[]) => (
        <div className="space-y-1">
          {value.slice(0, 2).map((pref, index) => (
            <Badge key={index} variant="outline" size="sm">
              {pref}
            </Badge>
          ))}
          {value.length > 2 && (
            <Badge variant="outline" size="sm">
              +{value.length - 2}
            </Badge>
          )}
          {value.length === 0 && (
            <span className="text-sm text-muted-foreground">暂无</span>
          )}
        </div>
      )
    },
    {
      key: 'lastVisit',
      title: '最近到店',
      render: (value: any, record: Customer) => {
        const lastVisit = formatLastVisit(record.id)
        const isRecent = lastVisit.includes('天前') || lastVisit === '今天' || lastVisit === '昨天'
        
        return (
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span className={isRecent ? 'text-green-600 font-medium' : 'text-muted-foreground'}>
              {lastVisit}
            </span>
          </div>
        )
      }
    }
  ]

  return (
    <AdminLayout>
      <PageContainer
        title="客户管理"
        description="管理客户信息、查看消费记录和服务偏好"
        action={
          <Link href="/admin/customers/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              新增客户
            </Button>
          </Link>
        }
      >
        {/* 成功提示 */}
        {showSuccessAlert && (
          <div className="mb-6">
            <SuccessAlert
              message={successMessage}
              onClose={() => setShowSuccessAlert(false)}
            />
          </div>
        )}

        <div className="bg-card border border-border rounded-lg">
          <DataTable
            data={customers}
            columns={columns}
            loading={loading}
            searchable
            searchPlaceholder="搜索客户姓名、电话或邮箱..."
            onSearch={handleSearch}
            selectable
            selectedRowKeys={selectedRowKeys}
            onSelectChange={setSelectedRowKeys}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              onChange: (page, pageSize) => {
                setPagination(prev => ({
                  ...prev,
                  current: page,
                  pageSize
                }))
              }
            }}
            actions={{
              title: '操作',
              render: (record: Customer) => (
                <div className="flex items-center space-x-1">
                  <Link href={`/admin/customers/${record.id}`}>
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </Link>
                  <Link href={`/admin/customers/${record.id}/edit`}>
                    <Button variant="ghost" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                  </Link>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => handleDelete(record.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              )
            }}
          />
        </div>

        {/* 批量操作 */}
        {selectedRowKeys.length > 0 && (
          <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-card border border-border rounded-lg shadow-lg p-4">
            <div className="flex items-center space-x-4">
              <span className="text-sm text-muted-foreground">
                已选择 {selectedRowKeys.length} 位客户
              </span>
              <div className="flex space-x-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => {
                    // 导出客户数据
                    const selectedCustomers = customers.filter(c => selectedRowKeys.includes(c.id))
                    const csvData = selectedCustomers.map(c => 
                      `${c.name},${c.phone},${c.email || ''},${c.totalVisits},${c.totalSpent}`
                    ).join('\n')
                    const blob = new Blob([`姓名,电话,邮箱,到店次数,消费金额\n${csvData}`], { type: 'text/csv' })
                    const url = URL.createObjectURL(blob)
                    const a = document.createElement('a')
                    a.href = url
                    a.download = '客户数据.csv'
                    a.click()
                    setSelectedRowKeys([])
                  }}
                >
                  导出数据
                </Button>
                <Button
                  variant="secondary"
                  className="bg-red-600 hover:bg-red-700 text-white"
                  size="sm"
                  onClick={() => {
                    if (confirm(`确定要删除选中的 ${selectedRowKeys.length} 位客户吗？`)) {
                      customerStore.deleteMany(selectedRowKeys)
                      setSelectedRowKeys([])
                      loadCustomers()
                    }
                  }}
                >
                  批量删除
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* 统计信息 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
          <div className="bg-card border border-border rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-primary">{pagination.total}</div>
            <div className="text-sm text-muted-foreground">总客户数</div>
          </div>
          <div className="bg-card border border-border rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-green-600">
              {customers.filter(c => getCustomerLevel(c.totalSpent).level === 'VIP').length}
            </div>
            <div className="text-sm text-muted-foreground">VIP客户</div>
          </div>
          <div className="bg-card border border-border rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">
              ¥{customers.reduce((sum, c) => sum + c.totalSpent, 0)}
            </div>
            <div className="text-sm text-muted-foreground">总消费额</div>
          </div>
          <div className="bg-card border border-border rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">
              {Math.round(customers.reduce((sum, c) => sum + c.totalSpent, 0) / Math.max(customers.length, 1))}
            </div>
            <div className="text-sm text-muted-foreground">平均消费</div>
          </div>
        </div>
      </PageContainer>
    </AdminLayout>
  )
}
