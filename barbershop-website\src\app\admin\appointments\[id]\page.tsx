"use client"

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import { AdminLayout, PageContainer, CardContainer } from '@/components/admin/layout/admin-layout'
import { Badge } from '@/components/admin/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Calendar, 
  Clock, 
  User, 
  Phone,
  Scissors,
  MapPin,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  ArrowLeft,
  MessageSquare
} from 'lucide-react'
import { appointmentStore } from '@/lib/admin/storage'
import { Appointment, AppointmentStatus } from '@/lib/types/admin'

export default function AppointmentDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [appointment, setAppointment] = useState<Appointment | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (params.id) {
      loadAppointment(params.id as string)
    }
  }, [params.id])

  const loadAppointment = (id: string) => {
    setLoading(true)
    try {
      const data = appointmentStore.getById(id)
      setAppointment(data || null)
    } catch (error) {
      console.error('Failed to load appointment:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleStatusChange = (newStatus: AppointmentStatus) => {
    if (!appointment) return
    
    try {
      appointmentStore.update(appointment.id, { status: newStatus })
      setAppointment(prev => prev ? { ...prev, status: newStatus } : null)
    } catch (error) {
      console.error('Failed to update appointment status:', error)
    }
  }

  const handleDelete = () => {
    if (!appointment) return
    
    if (confirm('确定要删除这个预约吗？')) {
      try {
        appointmentStore.delete(appointment.id)
        router.push('/admin/appointments')
      } catch (error) {
        console.error('Failed to delete appointment:', error)
      }
    }
  }

  const getStatusBadge = (status: AppointmentStatus) => {
    const statusConfig = {
      pending: { variant: 'warning' as const, label: '待确认', color: 'bg-yellow-100 text-yellow-800' },
      confirmed: { variant: 'success' as const, label: '已确认', color: 'bg-green-100 text-green-800' },
      in_progress: { variant: 'default' as const, label: '进行中', color: 'bg-blue-100 text-blue-800' },
      completed: { variant: 'default' as const, label: '已完成', color: 'bg-gray-100 text-gray-800' },
      cancelled: { variant: 'destructive' as const, label: '已取消', color: 'bg-red-100 text-red-800' },
      no_show: { variant: 'destructive' as const, label: '未到店', color: 'bg-red-100 text-red-800' }
    }

    const config = statusConfig[status]
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  const formatDateTime = (date: string, time: string) => {
    const dateObj = new Date(date)
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    const weekday = weekdays[dateObj.getDay()]
    
    return `${date} (${weekday}) ${time}`
  }

  const getStatusActions = (status: AppointmentStatus) => {
    const actions = []
    
    switch (status) {
      case 'pending':
        actions.push(
          <Button
            key="confirm"
            size="sm"
            onClick={() => handleStatusChange('confirmed')}
            className="bg-green-600 hover:bg-green-700"
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            确认预约
          </Button>
        )
        actions.push(
          <Button
            key="cancel"
            variant="outline"
            size="sm"
            onClick={() => handleStatusChange('cancelled')}
          >
            <XCircle className="h-4 w-4 mr-2" />
            取消预约
          </Button>
        )
        break
        
      case 'confirmed':
        actions.push(
          <Button
            key="start"
            size="sm"
            onClick={() => handleStatusChange('in_progress')}
          >
            开始服务
          </Button>
        )
        actions.push(
          <Button
            key="cancel"
            variant="outline"
            size="sm"
            onClick={() => handleStatusChange('cancelled')}
          >
            <XCircle className="h-4 w-4 mr-2" />
            取消预约
          </Button>
        )
        break
        
      case 'in_progress':
        actions.push(
          <Button
            key="complete"
            size="sm"
            onClick={() => handleStatusChange('completed')}
            className="bg-green-600 hover:bg-green-700"
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            完成服务
          </Button>
        )
        break
    }
    
    return actions
  }

  if (loading) {
    return (
      <AdminLayout>
        <PageContainer>
          <div className="flex items-center justify-center py-12">
            <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin" />
            <span className="ml-2">加载中...</span>
          </div>
        </PageContainer>
      </AdminLayout>
    )
  }

  if (!appointment) {
    return (
      <AdminLayout>
        <PageContainer>
          <div className="text-center py-12">
            <p className="text-muted-foreground">预约不存在</p>
            <Link href="/admin/appointments">
              <Button className="mt-4">返回预约列表</Button>
            </Link>
          </div>
        </PageContainer>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <PageContainer
        title="预约详情"
        description={`预约编号: ${appointment.id}`}
        action={
          <div className="flex space-x-2">
            <Link href={`/admin/appointments/${appointment.id}/edit`}>
              <Button variant="outline">
                <Edit className="h-4 w-4 mr-2" />
                编辑
              </Button>
            </Link>
            <Button variant="secondary" onClick={handleDelete} className="bg-red-600 hover:bg-red-700 text-white">
              <Trash2 className="h-4 w-4 mr-2" />
              删除
            </Button>
          </div>
        }
      >
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 主要信息 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 预约状态和操作 */}
            <CardContainer>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="text-2xl">📅</div>
                  <div>
                    <h3 className="text-lg font-semibold">预约状态</h3>
                    <div className="flex items-center space-x-2 mt-1">
                      {getStatusBadge(appointment.status)}
                      <span className="text-sm text-muted-foreground">
                        创建时间: {new Date(appointment.createdAt).toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex space-x-2">
                  {getStatusActions(appointment.status)}
                </div>
              </div>
            </CardContainer>

            {/* 客户信息 */}
            <CardContainer title="客户信息">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                    <User className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium text-lg">{appointment.customerName}</p>
                    <p className="text-muted-foreground">客户姓名</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                    <Phone className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <p className="font-medium text-lg">{appointment.customerPhone}</p>
                    <p className="text-muted-foreground">联系电话</p>
                  </div>
                </div>
              </div>
            </CardContainer>

            {/* 预约时间 */}
            <CardContainer title="预约时间">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <Calendar className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium text-lg">
                      {formatDateTime(appointment.date, appointment.startTime)}
                    </p>
                    <p className="text-muted-foreground">开始时间</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                    <Clock className="h-6 w-6 text-purple-600" />
                  </div>
                  <div>
                    <p className="font-medium text-lg">{appointment.duration} 分钟</p>
                    <p className="text-muted-foreground">
                      结束时间: {appointment.endTime}
                    </p>
                  </div>
                </div>
              </div>
            </CardContainer>

            {/* 服务项目 */}
            <CardContainer title="服务项目">
              <div className="space-y-4">
                {appointment.services.map((service, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border border-border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-accent/20 rounded-full flex items-center justify-center">
                        <Scissors className="h-5 w-5 text-accent" />
                      </div>
                      <div>
                        <p className="font-medium">{service.serviceName}</p>
                        <p className="text-sm text-muted-foreground">{service.duration} 分钟</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-primary">¥{service.price}</p>
                    </div>
                  </div>
                ))}
                
                <div className="border-t border-border pt-4">
                  <div className="flex items-center justify-between text-lg font-semibold">
                    <span>总计</span>
                    <div className="flex items-center space-x-4">
                      <span className="text-muted-foreground">{appointment.duration} 分钟</span>
                      <span className="text-primary">¥{appointment.totalPrice}</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContainer>

            {/* 备注信息 */}
            {appointment.notes && (
              <CardContainer title="备注信息">
                <div className="bg-muted/20 border border-border rounded-lg p-4">
                  <p className="text-foreground">{appointment.notes}</p>
                </div>
              </CardContainer>
            )}
          </div>

          {/* 侧边栏信息 */}
          <div className="space-y-6">
            {/* 理发师信息 */}
            <CardContainer title="理发师" className="text-center">
              <div className="flex flex-col items-center space-y-3">
                <div className="w-16 h-16 bg-accent/20 rounded-full flex items-center justify-center">
                  <span className="text-2xl">✂️</span>
                </div>
                <div>
                  <p className="font-semibold text-lg">{appointment.staffName}</p>
                  <p className="text-sm text-muted-foreground">专业理发师</p>
                </div>
              </div>
            </CardContainer>

            {/* 快速操作 */}
            <CardContainer title="快速操作">
              <div className="space-y-3">
                <Link href={`/admin/customers/${appointment.customerId}`}>
                  <Button variant="outline" className="w-full justify-start">
                    <User className="h-4 w-4 mr-2" />
                    查看客户详情
                  </Button>
                </Link>
                
                <Link href={`/admin/appointments/${appointment.id}/edit`}>
                  <Button variant="outline" className="w-full justify-start">
                    <Edit className="h-4 w-4 mr-2" />
                    编辑预约信息
                  </Button>
                </Link>
                
                <Button 
                  variant="outline" 
                  className="w-full justify-start"
                  onClick={() => window.print()}
                >
                  <Calendar className="h-4 w-4 mr-2" />
                  打印预约单
                </Button>
              </div>
            </CardContainer>

            {/* 预约统计 */}
            <CardContainer title="相关统计">
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">预约编号</span>
                  <span className="font-mono">{appointment.id.slice(-8)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">创建时间</span>
                  <span>{new Date(appointment.createdAt).toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">更新时间</span>
                  <span>{new Date(appointment.updatedAt).toLocaleDateString()}</span>
                </div>
              </div>
            </CardContainer>
          </div>
        </div>
      </PageContainer>
    </AdminLayout>
  )
}
