// <PERSON>'s Barbershop - 身份验证系统

import { User, UserRole } from '@/lib/types/admin'
import { userStore } from './storage'

// JWT Token 接口
interface TokenPayload {
  userId: string
  username: string
  role: UserRole
  exp: number
}

// 认证状态接口
export interface AuthState {
  isAuthenticated: boolean
  user: User | null
  token: string | null
}

// 登录响应接口
export interface LoginResponse {
  success: boolean
  user?: User
  token?: string
  message?: string
}

class AuthService {
  private readonly TOKEN_KEY = 'barbershop_admin_token'
  private readonly TOKEN_EXPIRY = 24 * 60 * 60 * 1000 // 24小时

  // 简单的JWT编码 (仅用于演示，生产环境应使用真正的JWT库)
  private encodeToken(payload: TokenPayload): string {
    const header = { alg: 'HS256', typ: 'JWT' }
    const encodedHeader = btoa(JSON.stringify(header))
    const encodedPayload = btoa(JSON.stringify(payload))
    const signature = btoa(`${encodedHeader}.${encodedPayload}.secret`)
    
    return `${encodedHeader}.${encodedPayload}.${signature}`
  }

  // 简单的JWT解码
  private decodeToken(token: string): TokenPayload | null {
    try {
      const parts = token.split('.')
      if (parts.length !== 3) return null
      
      const payload = JSON.parse(atob(parts[1]))
      return payload
    } catch (error) {
      return null
    }
  }

  // 验证密码 (简单比较，生产环境应使用哈希)
  private verifyPassword(inputPassword: string, storedPassword: string): boolean {
    return inputPassword === storedPassword
  }

  // 生成密码哈希 (简单实现，生产环境应使用bcrypt等)
  private hashPassword(password: string): string {
    // 这里只是简单返回原密码，实际应用中应该使用真正的哈希算法
    return password
  }

  // 用户登录
  async login(username: string, password: string): Promise<LoginResponse> {
    try {
      // 查找用户
      const users = userStore.getAll()
      const user = users.find(u => u.username === username)

      if (!user) {
        return {
          success: false,
          message: '用户名或密码错误'
        }
      }

      // 验证密码
      if (!this.verifyPassword(password, user.password)) {
        return {
          success: false,
          message: '用户名或密码错误'
        }
      }

      // 生成Token
      const tokenPayload: TokenPayload = {
        userId: user.id,
        username: user.username,
        role: user.role,
        exp: Date.now() + this.TOKEN_EXPIRY
      }

      const token = this.encodeToken(tokenPayload)

      // 更新最后登录时间
      userStore.update(user.id, {
        lastLogin: new Date().toISOString()
      })

      // 存储Token
      localStorage.setItem(this.TOKEN_KEY, token)

      return {
        success: true,
        user: { ...user, password: '' }, // 不返回密码
        token
      }
    } catch (error) {
      console.error('Login error:', error)
      return {
        success: false,
        message: '登录失败，请稍后重试'
      }
    }
  }

  // 用户登出
  logout(): void {
    localStorage.removeItem(this.TOKEN_KEY)
  }

  // 获取当前认证状态
  getAuthState(): AuthState {
    const token = localStorage.getItem(this.TOKEN_KEY)
    
    if (!token) {
      return {
        isAuthenticated: false,
        user: null,
        token: null
      }
    }

    const payload = this.decodeToken(token)
    
    if (!payload || payload.exp < Date.now()) {
      // Token过期
      this.logout()
      return {
        isAuthenticated: false,
        user: null,
        token: null
      }
    }

    // 获取用户信息
    const user = userStore.getById(payload.userId)
    
    if (!user) {
      this.logout()
      return {
        isAuthenticated: false,
        user: null,
        token: null
      }
    }

    return {
      isAuthenticated: true,
      user: { ...user, password: '' }, // 不返回密码
      token
    }
  }

  // 验证Token
  verifyToken(token?: string): boolean {
    const tokenToVerify = token || localStorage.getItem(this.TOKEN_KEY)
    
    if (!tokenToVerify) return false

    const payload = this.decodeToken(tokenToVerify)
    return payload !== null && payload.exp > Date.now()
  }

  // 检查用户权限
  hasPermission(requiredRole: UserRole): boolean {
    const authState = this.getAuthState()
    
    if (!authState.isAuthenticated || !authState.user) {
      return false
    }

    const userRole = authState.user.role
    
    // 权限层级: super_admin > admin
    if (userRole === 'super_admin') return true
    if (userRole === 'admin' && requiredRole === 'admin') return true
    
    return false
  }

  // 获取当前用户
  getCurrentUser(): User | null {
    const authState = this.getAuthState()
    return authState.user
  }

  // 刷新Token
  refreshToken(): string | null {
    const authState = this.getAuthState()
    
    if (!authState.isAuthenticated || !authState.user) {
      return null
    }

    const tokenPayload: TokenPayload = {
      userId: authState.user.id,
      username: authState.user.username,
      role: authState.user.role,
      exp: Date.now() + this.TOKEN_EXPIRY
    }

    const newToken = this.encodeToken(tokenPayload)
    localStorage.setItem(this.TOKEN_KEY, newToken)
    
    return newToken
  }

  // 修改密码
  async changePassword(currentPassword: string, newPassword: string): Promise<{ success: boolean; message: string }> {
    const user = this.getCurrentUser()
    
    if (!user) {
      return {
        success: false,
        message: '用户未登录'
      }
    }

    // 获取完整用户信息（包含密码）
    const fullUser = userStore.getById(user.id)
    
    if (!fullUser) {
      return {
        success: false,
        message: '用户不存在'
      }
    }

    // 验证当前密码
    if (!this.verifyPassword(currentPassword, fullUser.password)) {
      return {
        success: false,
        message: '当前密码错误'
      }
    }

    // 更新密码
    const hashedNewPassword = this.hashPassword(newPassword)
    userStore.update(user.id, {
      password: hashedNewPassword
    })

    return {
      success: true,
      message: '密码修改成功'
    }
  }

  // 创建新用户 (仅超级管理员)
  async createUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<{ success: boolean; user?: User; message: string }> {
    if (!this.hasPermission('super_admin')) {
      return {
        success: false,
        message: '权限不足'
      }
    }

    // 检查用户名是否已存在
    const existingUsers = userStore.getAll()
    const existingUser = existingUsers.find(u => u.username === userData.username)
    
    if (existingUser) {
      return {
        success: false,
        message: '用户名已存在'
      }
    }

    // 创建用户
    const hashedPassword = this.hashPassword(userData.password)
    const newUser = userStore.create({
      ...userData,
      password: hashedPassword
    })

    return {
      success: true,
      user: { ...newUser, password: '' }, // 不返回密码
      message: '用户创建成功'
    }
  }

  // 删除用户 (仅超级管理员)
  async deleteUser(userId: string): Promise<{ success: boolean; message: string }> {
    if (!this.hasPermission('super_admin')) {
      return {
        success: false,
        message: '权限不足'
      }
    }

    const currentUser = this.getCurrentUser()
    
    if (currentUser?.id === userId) {
      return {
        success: false,
        message: '不能删除自己的账户'
      }
    }

    const success = userStore.delete(userId)
    
    return {
      success,
      message: success ? '用户删除成功' : '用户删除失败'
    }
  }

  // 获取所有用户 (仅超级管理员)
  getAllUsers(): User[] {
    if (!this.hasPermission('super_admin')) {
      return []
    }

    return userStore.getAll().map(user => ({ ...user, password: '' }))
  }
}

// 导出单例实例
export const authService = new AuthService()

// React Hook 用于认证状态
export function useAuth() {
  const [authState, setAuthState] = React.useState<AuthState>(authService.getAuthState())

  React.useEffect(() => {
    // 定期检查Token状态
    const interval = setInterval(() => {
      const currentState = authService.getAuthState()
      setAuthState(currentState)
    }, 60000) // 每分钟检查一次

    return () => clearInterval(interval)
  }, [])

  const login = async (username: string, password: string) => {
    const result = await authService.login(username, password)
    if (result.success) {
      setAuthState(authService.getAuthState())
    }
    return result
  }

  const logout = () => {
    authService.logout()
    setAuthState(authService.getAuthState())
  }

  return {
    ...authState,
    login,
    logout,
    hasPermission: authService.hasPermission.bind(authService),
    changePassword: authService.changePassword.bind(authService)
  }
}

// 导入React (在实际使用时需要)
import * as React from 'react'
